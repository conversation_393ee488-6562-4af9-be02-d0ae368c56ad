#ifndef __COMMON_PROTOCOL_S2C_H__
#define __COMMON_PROTOCOL_S2C_H__

#include <stdint.h>
#include <vector>
#include "types.h"
#include "commonmacro.h"

#pragma pack(1)

namespace S2C
{
enum //CMD
{
//0
	SELF_ENTER_WORLD,		//自己进入世界的时候会收到这个
	SELF_LEAVE_WORLD,		//同上规则,离开世界时
	SELF_ENTER_SCENE,		//同上规则,进入场景时
	SELF_LEAVE_SCENE,		//同上规则,离开场景时
	NULL_NO_USE1,			//自己的简单信息
//5
	SELF_DEFINITE_INFO,		//自己的详细信息
	NO_USE,
	PLAYER_DEFINITE_INFO,		//其他玩家的详细信息
	PLAYER_DEFINITE_INFO_LIST,	//其他玩家的详细信息列表,自己走动跨越格子时会收到这个
	PLAYER_ENTER_SCENE,		//其他玩家在视野中进入场景时会收到这个
//10
	PLAYER_ENTER_SLICE,		//其他玩家从视野外走到视野内时会收到这个
	REFINE_EQUIP_RESULT,		//装备升阶的结果
	NPC_DEFINITE_INFO,		//NPC详细信息
	NPC_DEFINITE_INFO_LIST,		//NPC详细信息列表
	NPC_ENTER_SCENE,		//NPC进入视野
//15
	NPC_ENTER_SLICE,		//NPC进入视野格子范围
	//MATTER 没有简单信息,已经很简单了
	MATTER_INFO,
	MATTER_INFO_LIST,
	MATTER_ENTER_SCENE,
	//MATTER 不会动所以不会enter_slice
	//子物体也一样没有简单信息
	SUBOBJECT_INFO,
//20
	SUBOBJECT_INFO_LIST,
	SUBOBJECT_ENTER_SCENE,
	SUBOBJECT_ENTER_SLICE,
	OBJECT_LEAVE_SLICE,		//所有类型的目标离开视野都会收到这个协议
	OBJECT_LEAVE_SCENE,		//所有类型的目标离开场景,会收到这个,消失只是一种类型
//25
	SELF_DATA_SEND_END,		//这个协议意思是告诉客户端,角色数据发送完毕,可以进行下步交互逻辑
	OBJECT_MOVE,			//所有类型的目标移动时都会收到这个
	OBJECT_TURN,			//转向,同上
	OUT_OF_SIGHT_LIST,		//离开视野列表
	ERROR_MESSAGE,			//错误号
//30
	EXTEND_PROPERTY,		//玩家属性
	SERVER_TIMESTAMP,		//server端时间
	INVENTORY_DATA,			//包裹数据
	TASK_DATA,			//任务数据
	NOTIFY_POS,			//同步坐标
//35
	OBJECT_NOTIFY_PROP,		//通知属性给附近玩家
	OBJECT_NOTIFY_PROP_DELTA,	//将属性以增量方式通知给附近玩家
	OBJECT_NOTIFY_POZHAN,
	UNLOCK_ITEM_SLOT,		//解锁玩家物品
	GET_ITEM_INFO,			//玩家获取物品信息
//40
	SWAP_ITEM,			//玩家交换物品,玩家将2个位置的物品交换
	MOVE_ITEM,			//玩家移动物品,玩家将1个位置的物品移动一定数量到另外1个位置
	INC_ITEM,			//增加物品
	DEC_ITEM,			//减少物品
	TEAM_FOLLOW_INVITE,		//组队邀请
//45
	INC_MONEY,			//增加金钱
	DEC_MONEY,			//减少金钱
	ENTER_TEAM_FOLLOW_MODE,		//进入组队跟随状态
	LEAVE_TEAM_FOLLOW_MODE,		//离开组队跟随状态
	INVALID_OBJECT,			//非法目标
//50
	NPC_GREETING,
	TITLE_LIST,			//称号列表
	ADD_TITLE,			//增加称号
	DEL_TITLE,			//删除称号
	PLAYER_CHANGE_TITLE,		//玩家更改当前称号
//55
	ILLUSTRATION_DATA,		//图鉴数据
	ILLUSTRATION_CHANGE,		//图鉴变化
	DISCOVERMAP_DATA,		//地图探索数据
	DISCOVERMAP_CHANGE,		//地图探索变化
	REPUTATION_DATA,		//声望数据
//60
	REPUTATION_CHANGE,		//声望变化
	PRODUCE_RECIPE_DATA,		//生产配方数据
	PRODUCE_RECIPE_CHANGE,		//生产配方变化
	PRODUCE_SKILL_DATA,		//生产技能数据
	PRODUCE_SKILL_CHANGE,		//生产技能变化
//65
	COOLDOWN_DATA,			//冷却数据
	SET_COOLDOWN,			//设置冷却
	OBSLOETE3,
	UPDATE_GFX_STATE,
	AUTO_BOT_START,			//挂机开始
//70
	AUTO_BOT_STOP,			//挂机停止
	CASH_INFO,			//玩家元宝信息
	RECEIVE_EXP,			//获得经验
	OBJECT_LEVEL_UP,		//升级
	PLAYER_PROF_LEVEL_UP,		//职业升级
//75
	PLAYER_CHANGE_PROF,		//职业变更
	INVENTORY_SIZE,			//包裹大小通知
	SAFE_LOCK,			//安全锁
	PLAYER_SET_SLOGAN,
	OTHER_EQUIP_DETAIL,
//80
	OBJECT_BUFF,			//对象的buff
	SELF_BUFF,			//自己的buff
	MAIL_INFO,			//邮件信息
	SKILL_DATA,			//技能
	LEARN_SKILL,			//学习技能
//85
	SELF_HP,
	OBJECT_PERFORM_SKILL,
	SELF_LEVEL,
	CANNOT_CAST_SKILL,
	TASK_VAR_DATA,
//90
	OBJECT_START_ACTION,		//对象开始动作
	OBJECT_STOP_ACTION,		//对象结束动作
	OBJECT_DO_EMOTE,		//对象做表情
	REGION_SETTING,			//区域属性
	PLAYER_REVIVAL,			//玩家复活
//95
	PLAYER_PK_LEVEL,
	PLAYER_CHANGE_PK_SETTING,	//玩家更改自己的pk设置
	FIGHT_BACK_LIST_ADD,		//反击列表里增加
	FIGHT_BACK_LIST_SUB,		//反击列表里减少
	FAMILY_MAFIA_INFO,		//玩家家族帮派信息
//100
	OPEN_LOOT,			//打开loot
	CLOSE_LOOT,			//关闭loot
	ITEM_INFO_IN_LOOT,		//loot中物品信息
	PICKUP_ITEM_IN_LOOT,		//拾取物品
	LOOT_PRIVILEGE,			//loot权限
//105
	PLAYER_DIE,			//玩家死亡
	NPC_DIE,			//npc死亡
	PLAYER_USE_ITEM,		//玩家使用物品
	PLAYER_USE_ITEM_WITH_TARGET,	//玩家对目标使用物品
	PLAYER_USE_ITEM_WITH_ARG,	//玩家带参数使用物品
//110
	ROLL_ITEM_INFO,
	PLAYER_ROLL_POINT,
	OBSLOETE4,
	PET_INFO,			//玩家的宠物相关信息
	DEPOSITORY_PASSWORD_STATE,	//仓库密码状态
//115
	BE_HEALED,
	BE_HURT,
	OBJECT_BE_ATTACKED,
	OBJECT_BE_ENCHANTED,
	NO_USE2,
//120
	PK_SETTING,			//pk设置
	SUMMON_PET,			//玩家召唤宠物
	RECALL_PET,			//玩家收回宠物
	COMBINE_PET,			//玩家与宠物合体
	UNCOMBINE_PET,			//玩家与宠物解除合体
//125
	TEAM_FOLLOW_MOVE,		//队伍跟随移动
	EQUIPMENT_INFO_CHANGE,		//装备信息变化
	PET_RECV_EXP,			//宠物获得经验
	PET_LEVEL_UP,			//宠物升级
	PET_HP,				//宠物血量
//130
	PET_ENERGY,			//宠物精力
	PET_LIFE,			//宠物寿命
	NO_USE3,
	PET_DEAD,			//宠物死亡
	OBJECT_CHANGE_VISUAL_EFFECT,	//目标变身形态变化
//135
	PET_PROPERTY,			//宠物属性
	PLAYER_TEAM_STATE,		//玩家的队伍信息
	TEAM_MEMBER_DATA,		//队友信息
	TEAM_MEMBER_POS,		//队友位置
	TEAM_MEMBER_PICKUP,		//队友拾取
//140
	PET_FEALTY,			//宠物忠诚度
	PET_POTENTIAL_CHANGE,		//宠物潜力
	PET_NAME_CHANGE,		//宠物名字变化
	NPC_CHANGE_NAME,		//发给附近所有人
	ASSIGNED_POTENTIAL,		//分配的潜力
//145
	INV_ARRANGE_RE,			//包裹整理返回
	RESURRECT_READY,		//复活准备情况
	OBJECT_CHANGE_FACTION,		//阵营变化
	SIMPLE_ACHIEVEMENT_DATA,	//简单成就信息
	ACTIVE_ACHIEVEMENT_DATA,	//成就数据
//150
	ACHIEVEMENT_ACTIVE,		//服务器端成就激活,存在存档数据
	ACHIEVEMENT_COMPLETE,		//成就完成
	OTHER_PLAYER_ACHIEVEMENT,	//其他人的成就
	ACHIEVEMENT_MESSAGE,
	NO_USE4,
//155
	NO_USE5,
	CHECK_PING,			//ping值
	SUBOBJECT_TAKE_EFFECT,		//子物体生效(比如爆炸效果)
	ITEM_BUY_BACK,			//回购
	OBJECT_CHANGE_LOCK_ENEMY_STATE, //目标锁敌状态变化
//160
	ENTER_SERVER_MOVE_CONTROL_MODE,	//玩家进入移动控制状态
	LEAVE_SERVER_MOVE_CONTROL_MODE,	//玩家离开移动控制状态
	TRADE_INVITE,			//交易邀请
	TRADE_REPLY,			//交易回应
	TRADE_START,			//交易开始
//165
	TRADE_STATE,			//交易状态变化
	TRADE_SET_MONEY,		//钱
	TRADE_MOVE_ITEM,		//物品
	TOGGLE_INVISIBLE,		//GM隐身
	TOGGLE_INVINCIBLE,		//GM无敌
//170
	SELF_MARKET_INFO,		//自己的摊位信息
	SET_MARKET_NAME_RE,		//设置摊位名称的返回,表示设置完成
	MARKET_ADD_SELL_ITEM,		//出售物品上架
	MARKET_SELL_ITEM_CHANGE_STATUS,	//出售物品状态变化
	MARKET_SUB_SELL_ITEM,		//出售物品下架
//175
	MARKET_ADD_BUY_ITEM,		//收购物品上架
	MARKET_BUY_ITEM_CHANGE_STATUS,	//收购物品状态变化
	MARKET_SUB_BUY_ITEM,		//收购物品下架
	PLAYER_OPEN_MARKET,		//玩家开启摆摊
	PLAYER_CLOSE_MARKET,		//玩家关闭摆摊
//180
	PLAYER_MARKET_INFO,
	PLAYER_MARKET_TRADE_SUCCESS,
	MARKET_TRADE_ITEM,
	SCENE_MARKET_INFO,
	MARKET_CLEAR,
//185
	KUNGFU_DATA,			//技能序列
	LEARN_KUNGFU,			//学习技能序列
	BIND_INVITE,			//bind邀请
	BIND_INVITE_REPLY,		//bind邀请返回
	BIND_REQUEST,			//bind申请
//190
	BIND_REQUEST_REPLY,		//bind申请返回
	BIND_INFO,			//bind信息
	BIND_STOP,			//bind结束
	INC_FORCE,			//增加功力
	DEC_FORCE,			//减少功力
//195
	NPC_ASCRIPTION_CHANGE,		//npc归属变化
	PLAYER_WALLOW_INFO,		//沉迷
	NPC_CHANGE_SHAPE,		//npc变身形态
	MATTER_CHANGE_STATUS,		//矿物属性
	OBJECT_ACTION_SEAL_MASK,	//行为状态限制掩码
//200
	PLAYER_INSTANCE_INFO,		//玩家副本信息  废弃
	PLAYER_INSTANCE_INFO_CHANGE,	//玩家副本信息变化 废弃
	KICKOUT_INSTANCE,		//踢出副本
	COMBAT_STATE,			//战斗状态
	OBJECT_TARGET_CHANGE,		//对象目标变化
//205
	PULL_HORSE,			//拉马
	PET_SCRIPT_MESSAGE,		//宠物脚本信息反馈
	PET_TRAIN_INFO,			//宠物训练信息
	PET_IDENTITY,			//宠物身份信息
	PET_ENHANCE_LEVEL,		//宠物强化等级
//210
	PET_INSANE,			//宠物走火入魔
	OBJECT_CHANGE_CONTROL_STATE,	//目标控制状态
	PET_LAYER,			//宠物修为变化
	MINE_CAN_GATHER_COUNT_CHANGE,	//矿物可以被挖的次数变化
	PLAYER_PET_SAY_HELLO,		//宠物打招呼
//215
	LOCATE_PLAYER_RE,		//追踪玩家结果
	EQUIPMENT_DURABILITY,		//装备耐久
	SERVER_CONFIG_DATA,
	PET_XP,				//宠物xp
	BOARD_INFO,			//版面信息
//220
	GROUP_SELF_JOIN,
	GROUP_SELF_LEAVE,
	GROUP_MEMBER_JOIN,
	GROUP_MEMBER_LEAVE,
	GROUP_MEMBER_DATA,
//225
	DUEL_INVITE,			//决斗邀请
	DUEL_INVITE_REPLY,		//决斗回应
	DUEL_PREPARE,			//决斗准备
	DUEL_CANCEL,			//决斗取消
	___DUEL_START,			//决斗开始
//230
	___DUEL_STOP,			//决斗结束
	DUEL_RESULT,			//决斗结果
	PLAYER_SET_TEAM_DATA,
	NPC_FIGHT_POLICY,		//npc战斗广播策略
	XYXW_INVITE,			//相依相偎邀请
//235
	XYXW_INVITE_REPLY,		//相依相偎邀请返回
	XYXW_REQUEST,			//相依相偎申请
	XYXW_REQUEST_REPLY,		//相依相偎申请返回
	XYXW_INFO,			//相依相偎信息
	XYXW_STOP,			//相依相偎结束
//240
	PLAYER_SKILL_ADDON,		//附加技能级别(已有技能附加)
	PLAYER_EXTRA_SKILL,		//添加额外技能
	EDIT_DIY_KUNGFU_RE,		//编辑自定义技能包结果
	TITLE_UPDATE,			//称号信息更新
	LOTTERY_PRIZE,			//彩票奖励信息
//245
	SELF_VIGOR,			//生产点,社交点,活动点通知
	WORLD_PARAMS,			//世界参数
	WORLD_PARAM_CHANGE,		//世界参数变化
	INSTANCE_SPEAK,			//副本喊话
	PET_AUTO_PICKUP_FLAG,		//宠物自动拾取标志
//250
	CONTROL_END,			//控制结束
	PLAYER_PET_SET_SKILL_COOLDOWN,	//宠物技能冷却
	SCENE_INFO_CHANGE,		//场景信息变化
	HELP_MASK_DATA,			//帮助数据
	HELP_MASK_CHANGE,		//帮助变化
//255
	OBJECT_RUN_PERFORM,		//执行过程
	OTHER_PET_INFO,			//宠物信息
	LUCKEY_DRAW_INFO,		//抽奖信息
	BINDTHROW_START,		//摔投开始
	BINDTHROW_STOP,			//摔投结束
//260
	SELF_MP,			//自己的mp
	SELF_MAX_HP,
	SELF_MAX_MP,
	SELF_HP_GEN,
	SELF_MP_GEN,
//265
	SELF_ABILITY_PROP,
	SELF_POTENTIAL,
	SELF_ATTR_DAMAGE,
	SELF_ATTR_RESIST,
	SELF_DEBUFF_RESIST,
//270
	SELF_JUDGEMENT_EVADE,
	SELF_JUDGEMENT_RESIST,
	SELF_WALK_SPEED,
	SELF_RUN_SPEED,
	SELF_MOUNT_SPEED,
//275
	SELF_NORMAL_ATTACK_SIGHT,
	SELF_VALID_ATTACK_SIGHT,
	SELF_COMBO,
	SYNTHETIZE_ITEM2_RESULT,
	BIND_CONFIRM_REQUEST,		//发出绑定确认请求
//280
	BIND_CONFIRM_START,		//通知绑定确认开始
	REINFORCE_EQUIP_RESULT,		//强化结果
	PRODUCE_RECIPE_LEVELUP,		//生产配方升级考核结果
	OBSLOETE1,
	SELF_APTITUDE,			//资质
//285
	SELF_KILL_MONSTER,		//本人杀怪数量
	PLAYER_TIRED,			//队友杀怪数量过多，本队无掉落
	CLEAR_BUF_SKILL,		//清除buf skill
	TEAM_INST_ERROR,		//队伍副本错误信息
	BE_HEALED_MP,			//恢复mp
//290
	SPEAK_USE_CLIENTID,		//策略客户端喊话，不再使用
	WEDDING_LIST,			//已预约的婚礼列表
	SUPPORT_SPACTATE_INSTANCE_INFO,	//支持的观战副本信息
	SELF_COMPENSATE_EXP,		//本人累积补偿经验
	MULTI_EXP_STATE,		//多倍经验状态
//295
	TRANSMIT_FLAG_INFO,		//飞行棋相关数据
	SAFE_REVIVE_GUIDE,
	WEDDING_APPLY_RE,		//婚庆申请结果
	HOME_INFO,
	HOME_INFO_LIST,
//300
	HOME_ENTER_SCENE,
	SELF_MAX_VIGOR,			//生产/社交/精力点上限
	SOCIAL_MSG,			//社交消息
	SERVER_DATA_VERSION_INFO,	//服务器版本信息
	SELF_MULTI_EXP,			//多倍经验时间
//305
	BATTLEFIELD_FACTION_INFO,	//战场的阵营信息
	BATTLEFIELD_FACTION_STATE,	//战场阵营状态信息
	BATTLEFIELD_COUNT_DOWN,		//战场开始倒计时
	DEPOSITORY_STATE,		//仓库状态
	INSTANCE_SCORE,			//副本评价
//310
	RANDOM_MAP,
	SELF_FAMILY_CALL,		//剩余结义召集次数
	GIFT_NOTIFY,			//在线发奖通知
	BATTLE_LOTTERY_PRIZE,		//战场奖励信息
	BATTLEFIELD_STATE_INFO,		//战场状态信息
//315
	BATTLEFIELD_FACTION_RESULT,	//战场结局
	PLAYER_CHANGE_OBJECT_STATE,	//玩家状态变化
	SELF_FAMILY_ACCESS,		//上次查看结义时间
	LUCK_ACTION_NOTIFY,		//当前幸运活动通知
	PLAYER_LOGIN_INFO,		//玩家登陆信息
//320
	FLOURISH_PRIZE_NOTIFY,		//活跃度礼包通知
	START_QINGGONG_RE,		//开启轻功返回
	STOP_QINGGONG,			//通知客户端停止轻功
	OBSLOETE2,
	JOB_INFO,			//差事信息
//325
	LOADING_PROGRESS,		//进入场景时的加载进度
	CUR_ACTIVE_JOB_CHANGE,		//当前激活差事变化
	JOB_EXP_CHANGE,			//工作经验变化
	JOB_LEVEL_NOTIFY,		//差事级别变化,包括学习和升级等
	FORGET_JOB,			//遗忘差事

//330
	SELF_HIT,			//连击数
	TPAWARD_NOTICE,			//通知排行榜发奖信息
	TPAWARD_RESULT,			//排行榜发奖结果信息
	ENTER_WEAK,			//开始虚弱状态倒计时
	PRACTICE_RESULT,		//修炼结果信息

//335
	UPDATE_OBJECT_STATE,		//更新object_state
	SERVICE_CONTENT,		//npc服务信息
	PLAYER_ACTIVE_JOB,		//群发激活差事信息
	MALL_SHOPPING_RESULT,		//商城购买结果
	LIMIT_PURCHASE_END,		//限量收购完成
//340
	TRANSFORM_STATE,		//变身状态
	COMMON_USE_LIMIT_DATA,		//公共使用限制数据
	COMMON_USE_LIMIT_CHANGE,	//公共使用限制变化
	SCENE_PARAMS,			//场景参数 ---暂时用 World Param
	SCENE_PARAM_CHANGE,		//场景参数变化	---暂时用World Param
//345
	ALLIANCE_WAR_RESULT,		//盟主战结果列表
	SCENE_SPECIAL_OBJECT_INFO_1,	//场景内特殊对象信息,可能广播
	SCENE_SPECIAL_OBJECT_DISAPPEAR,	//场景内特殊对象消失,可能广播
	ALLIANCE_WAR_APPLY_RE,		//盟主战报名结果
	SELECT_ROLE_INFO,		//变身选择结果
//350
	MAFIA_LOGISTIC_AVAILABLE,	//招工物品可生产数
	MAFIA_LOGISTIC_ALLINFO,		//所有招工信息
	MAFIA_BUILD_UP_INFO,		//帮派建筑物升级信息
	MAFIA_MONEY,			//帮派资金
	INST_SPECIAL_MODE,		//副本特殊模式
//355
	CHANGE_BOARD_WAIT_TIME,		//版面切换到数计时
	DEBUG_HOT_UPDATE_RE,
	OTHER_PROPERTY_NEXT,		//其它玩家的属性职业等级等数据
	nouse4,
	SUTRA_DATA,			//心法激活技能
//360
	SUTRA_EDIT_RE,			//心法技能管理
	PLAYER_PICK_RESULT,		//拾取结果通知
	DEL_DIY_KUNGFU_RE,		//编辑自定义技能包结果
	ACTIVE_EQUIP_SLOT,		//激活的装备栏位
	EQUIPMENT_EXP,			//装备经验
//365
	SOUL_PICKED,			//魂被玩家吸收
	OBJECT_BEATTACK_CHANGED,	//可被攻击状态发生变化
	HOME_DATA,			//家园玩法数据
	HOME_ATTEND_RESULT,		//家园主屋服侍结果
	BATTLE_OPTION_INFO,		//战场设置信息
//370
	BATTLEFIELD_FACTION_RECORD,	//战场战况记录
	INSTANCE_NPC_SCORE_RATIO,	//副本战斗积分比率
	INSTANCE_AWARD_EXP,		//副本奖励经验
	SCENE_LOAD_PROCESS,		//进度加载
	PLAYER_SET_SIGNATURE,		//设定签名
//375
	PLAYER_RECORD_INFO,		//玩家战绩信息
	PLAYER_APPEARANCE_INFO,		//角色自定义外观数据
	PLAYER_RECORD_INFO_INC,		//玩家战绩增量信息
	SELF_CRIT_RESIST,		//自身会心及抵御相关属性
	BIND_HIJACK_NPC_INFO,		//劫持绑定怪物信息
//380
	BE_HIJACKED_TRANSINFO,		//被劫持跳转场景坐标信息
	PLAYER_EXTEND_DATA,		//玩家的装备、角色自定义数据
	REWARD_INFO,			//玩家奖励信息
	ADD_REWARD,			//增加奖励
	DEL_REWARD,			//删除奖励
//385
	ESCORT_TRAP_INFO,		//镖车陷阱信息
	GATHER_INFO_REQ_RESULT,		//打探消息服务结果相关
	GUESSGAME_NEEDMSG,		//推理游戏消息序列
	GUESSGAME_RESULT,		//推理游戏结果通知
	JUEWEI_TASK_PUBLISH_RE,		//爵位任务发布返回
//390
	JUEWEI_TASK_SETTLE_RE,		//爵位任务结算返回
	JUEWEI_TASK_DELIVER_RE,		//爵位任务发放返回
	RESET_INSTANCE,			//重置副本
	TEMP_ENEMY_RELATION,		//当前场景的临时阵营敌对关系
	MAINFACTION_OPER_RESULT,	//搬迁总舵结果
//395
	MAFIA_TREASURE_TRAP_INFO,	//藏宝阁机关信息
	MAFIA_TREASURE_BUFF_INFO,	//藏宝阁buff信息
	PLAYER_ALIAS,			//玩家化名信息
	MAFIA_ACTIVITY_INFO,		//帮派活动信息
	MAFIA_ACTIVITY_OPENRESULT,	//开启帮派活动结果
//400
	MAFIA_CUR_ACTIVITY,		//帮派当前开启活动信息
	MAFIA_TRAPOPER_RESULT,		//机关操作结果反馈
	MINGXING_LIST,			//明星身份列表
	ADD_MINGXING,			//增加明星身份
	DEL_MINGXING,			//删除明星身份
//405
	NPC_WOO_OWNER_CHANGE,		//求爱NPC归属变化
	MAFIA_SUBMITMSG_RESULT,		//帮派提交消息结果
	NPC_RESPONSE_SKILL,		//npc回应玩家技能
	BADGE_LIST,			//徽章信息
	MAFIA_WELFARE_GET,		//领取福利后通知玩家
//410
	MAFIA_ACTIVITY_END_INFO,	//帮派活动结束信息
	MAFIA_PLAYER_CHANGEDATA,	//玩家增加帮派数据
	SELF_ESCORT_COUNT,		//412 本人今日剩余运镖/劫镖次数
	ESCORT_STATE,			//413 镖车状态，包含驿站、陷阱、下药等
	GATHER_INFO_LIST,		//采集信息列表
//415
	PET_TRANS_FORCE_RESULT,		//415 宠物传功结果
	PET_LEARN_SKILL_RESULT,		//416 宠物学技能结果
	PET_REBORN_RESULT,		//417 宠物洗髓结果
	PRODUCE_SKILL_UPGRADE_RESULT,	//418 生产技能升级结果
	SKILL_NOTIFY,			//419 状态包掩码集合
//420
	BADGE_SWAP,			//徽章操作回应
	OTHER_BADGE_DETAIL,		//其他玩家的徽章信息
	BATTLE_STAT_PANEL,		//战场击杀记录
	BATTLE_STAT_PANEL_INC,		//战场击杀记录增量协议
	FIRE_IN_THE_HOLE,		//集中攻击地点
//425
	PLAYER_APTITUDE,		//玩家心法资质属性
	BADGE_ADD,			//获得徽章
	BADGE_DEL,			//失去徽章
	BADGE_UPDATE,			//徽章更新
	NPC_TIZI_INFO_CHANGE,		//涂鸦内容改变
//430
	PLAYER_TIZI_RESULT,		//玩家题字操作结果
	SELF_ATTR_DAMAGE_RESIST_IGNORE,	//属性伤害抗性无视
	SELF_ATTR_DAMAGE_PIERCE,	//属性伤害穿透
	CLUE_TO_CLUE_RESULT,		//线索合成线索结果
	CLUE_TO_FINAL_RESULT,		//线索合成结局结果
//435
	BATTLEFIELD_PLAYER_BUY_RE,	//战场内玩家购买返回结果
	BATTLEFIELD_PLAYER_SELL_RE,	//战场内玩家购买返回结果
	BATTLEFIELD_SHOP_SYNC,		//战场商店数据同步
	WUXUE_DATA,			//武学数据
	WUXUE_UPDATE,			//武学编辑反馈
//440
	WUXUE_SELECT,			//武学激活反馈
	WUXUE_USER_WUXUE_MAX,		//自创武学上限
	WUXUE_JUEJI_MAX,		//武学能包含绝技上限
	BATTLEFIELD_SKILL_LEVEL,	//战场技能等级
	BATTLEFIELD_PLAYER_LEVEL,	//战场名人等级
//445
	SECURITY_PASSWORD_CONTROL_STATE,	//安全码控制状态
	SPAW_CONTROLLER_STATE,		//控制器状态通知，用于战场5v5
	PLAYER_EXTEND_PROPERTY,		//玩家的扩展属性数据
	JIACHI_INFO,			//加持信息
	JIACHI_DATA,			//加持完整数据
//450
	JIACHI_LEVEL_UP,		//加持升级
	TRANSMIT_FLAG_CHANGED,		//记录飞行棋位置
	MAFIA_PARTY_COUNT,		//帮派宴会次数
	MAFIA_GFT_INFO,			//帮派发粮任务信息
	SELF_TRANS_FORCE_COUNT,		//454 本人今日剩余传功次数
//455

	NOTIFY_OBJECT_POS,		//同步坐标(广播)
	PLAYER_PROPERTY_NEXT, 		//新的属性同步协议
	SELF_PROPERTY_DELTA_DEPRECATED,		//新的属性同步协议（差值）(已废弃，通过pb同步，用PLAYER_PROPERTY_NEXT)
	SERVER_PING,			//服务器主动ping客户端
	SERVER_PONG,			//服务器使用pong回应客户端发来的ping
//460
	STAGE_START,			//版面开启
	STAGE_CLOSE,			//版面关闭
	ENTER_DYING,			//濒死
	START_CG,			//开始播放CG
	STOP_CG,			//停止播放CG
//465
	PICKUP_DROP_MATTER,		//拾取了掉落物
	SCENE_STAGE_REWARD,		//发放版面奖励
	SCENE_LEVEL_REWARD,		//发放关卡奖励
	SCENE_LEVEL_SCORE,		//关卡分数通知
	SCENE_LEVEL_COUNTDOWN,		//关卡倒计时
//470
	NPC_ANIMATION,			//NPC 播放动画（比如挑衅）
	NPC_ANIMATION_MOVE,		//NPC 播放动画且移动
	USE_ELIXIR_RESULT,		//使用丹药的结果
	HIDDEN_TREASURE_LIST,		//版面开始时发送给玩家的隐藏物品列表
	DISCOVER_HIDDEN_TREASURE,	//发现了隐藏物品
//475
	COMBINE_ELIXIR_RESULT,		//合成丹药的返回结果
	CHU_POZHAN,			//出破绽(删掉)
	STAGE_BLOCK_LIST,		//当前版面的阻挡列表
	INSTANCE_AC_LIST,		//副本完成列表
	INSTANCE_ACCOMPLISHED,		//副本完成，用于判断是否可以开启后继副本，并非关卡完成提示

//480
	CLIENT_LEVEL_STATUS,		//服务器通知客户端关卡状态：等待其它玩家中，可以进入游戏中 在副本关卡中，这作为开启副本的触发条件之一
	LEVEL_ATTRIBUTE,		//场景的属性通知命令
	SUPER_HERO_LIST,		//名人列表
	ACTIVE_HERO_LIST,		//激活名人属性列表
	ACTIVATE_HERO,			//激活和取消激活名人
//485
	ADD_NEW_SUPER_HERO,		//增加新名人
	LEVEL_CHEST_OP_RESULT,		//宝箱升阶和打开的返回
	LEVEL_ARENA_RESULT,		//和玩家化身对战的竞技场战斗结果
	LEVEL_SQUAD_LIST,		//关卡小队列表，在版面开启时和小队更新时会发送给玩家（注：小队更新包括召唤，玩家离开副本和召唤物死亡等） 另外，小队成员可能不在视野内，需要考虑这种情况下的显示，
	SCRIPT_COMMON_DATA,		//脚本发给玩家的通用数据

//490
	SQUAD_OPEN_CHEST,		//小队其他人打开了箱子
	FRIEND_SUMMON_INFO,		//好友的可召唤数据
	HERO_PROF_EXP_MODIFY,		//名人使用或者增加了职业经验(prof_exp);
	HERO_PROF_LEVEL_UP,		//名人的战斗等级升级
	TRAINIG_SUCCESS,		//修炼成功

//495
	FIND_WAY_RE,			//寻路结果
	SKILL_BYTES_MODE,		//通知客户端是否记录技能bytes，默认不记录 ，这个协议没有数据，有协议本身就可以
	CAST_SKILL_RE,			//CAST_SKILL回应，用于计算延迟，实际上和技能完全无关
	ATTACK_LOOP_START,		//攻击循环开启，（移动攻击）
	ATTACK_LOOP_STOP,		//攻击循环结束

//500
	OTHER_PLAYER_REPUTATION,	//其他玩家的声望
	AUTO_REWARD_LIST,		//自动发奖礼包数据（签到，在线，月卡，等级，VIP礼包）
	DIRECT_LOTTERY_SEEK_RESULT,	//直接抽奖（寻访）返回结果
	PATA_DATA,			//玩家爬塔信息
	HERO_RECV_EXP,			//名人增加了经验(exp);
//505
//以后都是protocbuf解析
//由于定义都在protocbuff里面，所有直接申请出所有空间 200个差不多了够了不够再加
	GP_BEGIN	= 505,		//第一个
	GP_CAR_RACE_INNER = 826,	//赛车副本内部协议
	GP_END		= 999,		//最后一个protobuff协议

//add protocol index here
	PROTOCOL_COUNT,


//2000
	GS_DEBUG_QUERY_CTRL_STATUS_RE = 2000,
	GS_DEBUG_QUERY_SCENE_PARAM_RE = 2001,
	GS_DEBUG_QUERY_NPC_PARAM_RE = 2002,
};

struct single_data_header
{
	unsigned short cmd;
};

struct multi_data_header
{
	unsigned short cmd;
	unsigned short count;
};

namespace INFO
{
struct self_definite_info	//自己的详细信息
{
	ruid_t id;
	int newid;
	unsigned char level;		//级别
	unsigned char prof;		//职业
	unsigned char prof_level;	//职业级别
	uint64_t exp;			//经验
	int force;			//功力
	uint64_t stored_exp;	//存储经验
	A3DVECTOR3 pos;			//位置
	unsigned short dir;		//方向0 - 65535的定点数表示度数
	unsigned int faction;		//阵营
	short pk_level;			//pk级别 -1000~1000
	unsigned short equip_crc;
	unsigned char lock_enemy_state;
	unsigned char combat_state;
	ruid_t target_id;
	unsigned char control_state;
	int nation;
	unsigned short hairid;		//发型索引
	unsigned char body_size;	//体型
	object_state_t object_state;	//当前状态
	//动态部分
};

struct player_definite_info_common
{
	ruid_t id;
	int newid;
	unsigned char name_size;
	//name_buff; 保留
	//	unsigned char clothesid;
	//	unsigned char idphoto;
	//	unsigned char faceid;
	//	unsigned char hairid;
	//	unsigned char haircolor;
	//	unsigned char skincolor;
	//	unsigned char beardid;
	//	unsigned char tattoo;
	//	unsigned char sharp;
	unsigned char gender;
	unsigned char level;		//级别
	unsigned char prof;		//职业
	//	unsigned char prof_level;	//职业级别
	A3DVECTOR3 pos;			//位置
	unsigned short dir;		//方向0 - 65535的定点数表示度数
	unsigned int faction;		//阵营
	short pk_level;			//pk级别 -1000~1000
	unsigned short equip_crc;
	unsigned short appearance_crc;
	//	unsigned char lock_enemy_state;
	unsigned char combat_state;
	//	ruid_t target_id;
	unsigned char control_state;
	//prop_notify 保留
	//	int active_job;
	//	unsigned char job_level;
	//	unsigned int wulin;		//武林声望
	int nation;
	unsigned short hairid;		//发型索引
	unsigned short clothesid;	//衣服
	unsigned char body_size;	//体型
	//动态部分
};

struct player_definite_info
{
	object_state_t object_state;	//当前状态
	player_definite_info_common common;
};

struct npc_definite_info_common
{
	//下面区分类型
	//PlayerNPC 压 tid + player_definite_info_common
	//普通npc入下
	ruid_t id;		//id
	int newid;
	level_t level;
	tid_t tid;			//模板id
	A3DVECTOR3 pos;
	faction_t faction;
	unsigned short crc;		// seed of customize data
	dir_t dir;
	//		unsigned char lock_enemy_state;
	unsigned char combat_state;
	//		ruid_t target_id;
	unsigned char control_state;
	//		unsigned char shape;
	//prop_notify 保留
	char can_not_be_attack;
};

struct npc_definite_info
{
	object_state_t state;
	npc_definite_info_common common;
	//动态部分
};

struct player_npc_definite_info
{
	ruid_t origin_id;
	player_definite_info info;
};

struct matter_info
{
	enum
	{
		OWNER_PLAYER = 0,
		OWNER_TEAM = 1,
		OWNER_ROLL = 2,
		OWNER_ALL = 3,
		OWNER_MAFIA = 4,
	};
	ruid_t id;
	int new_id;
	tid_t tid;
	A3DVECTOR3 pos;
	unsigned short dir0;
	unsigned short dir1;
	unsigned short rad;
	unsigned char matter_type;
};

struct subobject_info
{
	int newid;
	tid_t tid;
	A3DVECTOR3 pos;
	unsigned short dir;
	float speed;
	int owner_newid;
	int target_newid;
	unsigned char extend_mask;

	//顺时针还是逆时针是从顺着子物体的朝向看
	unsigned char move_type;//0=直线，1=圆周, 2=导弹，3=追踪，4=钟摆
	unsigned char max_angle;//最大角度，垂直地面为0
	float cur_angle;//当前角度，垂直地面为0，正为顺时针，负为逆时针

	A3DVECTOR3 target_pos;
	unsigned char is_shadow_back; //是否影子子物体返回
};
//enum EXTEND_MASK
//{
//	EM_WEAPON = 0x01,
//};
//int weapon_tid;
//int weapon_mid;

struct home_info
{
	ruid_t id;          // object XID.id
	unsigned char type; // 0 家园 1 帮派

	//{{家园
	unsigned int homesite_id;          //家园地基ID
	ruid_t owner_id;                   //家园主人角色ID
	unsigned char home_building_count; //家园建筑数量
	struct home_building
	{
		int tid;
		unsigned char place; //位置
		unsigned char level; //等级
		unsigned char style; //风格
		unsigned char state; //0 正常 1升级中
	} home_buildings[0];
	//家园}}

	//{{帮派
	A3DVECTOR3 pos;
	unsigned char faction_type;
	unsigned char mafia_building_count;
	struct mafia_building
	{
		int tid;
		char level; //等级
		char state; //0 正常 1升级中
	} mafia_buildings[0];
	//帮派}}

	object_state_t state; // object state
};

struct scene_info
{
	enum SCENE_INFO_MASK
	{
		SIM_MIRROR		= 0x01,	//是否存在镜像
		SIM_PARAM		= 0x02,	//场景参数,天气信息在这里
		SIM_BATTLE_MASTER	= 0x04, //战场主场景
		SIM_GS_INFO		= 0x08, //gs线号信息
		SIM_BATTLE_SUB		= 0x10, //战场子场景
		SIM_TIME_STILL		= 0x20, //时间静止
	};
	unsigned char scene_info_mask;

	//SIM_MIRROR
	unsigned char mirror_id;	//0 - n-1
	unsigned char all_mirror_count;	//>= 2
//			unsigned char mirror_list[0];

	//SIM_PARAM
	unsigned char param_count;
	struct param_t
	{
		unsigned short index;
		int param;
	};
	param_t params[0];

	//SIM_GS_INFO
	int line_id;
	unsigned char mask;		// 0 普通线 0x01 私有线

	//SIM_BATTLE_SUB
	int scene_tid;
};
}

namespace CMD
{
struct self_enter_world			//SELF_ENTER_WORLD
{
	single_data_header header;
	int world_tid;
	unsigned char line_id;
	int world_id;
};

struct self_leave_world			//SELF_LEAVE_WORLD
{
	single_data_header header;
	int world_tid;
};

struct self_enter_scene			//SELF_ENTER_SCENE
{
	single_data_header header;
	int self_newid;
	scene_tag_t scene_tag;
	A3DVECTOR3 pos;
	unsigned short dir;
	md5_t hmap_md5;
	md5_t rmap_md5;
	md5_t dirmap_md5;
	INFO::scene_info info;
};

struct self_leave_scene			//SELF_LEAVE_SCENE
{
	single_data_header header;
	scene_tag_t scene_tag;
};

struct self_definite_info		//SELF_DEFINITE_INFO
{
	single_data_header header;
	INFO::self_definite_info info;
};

struct player_definite_info		//PLAYER_DEFINITE_INFO
{
	single_data_header header;
	INFO::player_definite_info info;
};

struct player_definite_info_list_discard 	//PLAYER_DEFINITE_INFO_LIST
{
	multi_data_header header;
	INFO::player_definite_info list[0];
};

struct player_enter_scene		//PLAYER_ENTER_SCENE
{
	single_data_header header;
	INFO::player_definite_info info;
};

struct player_enter_slice		//PLAYER_ENTER_SLICE
{
	single_data_header header;
	INFO::player_definite_info info;
};

struct npc_definite_info		//NPC_DEFINITE_INFO
{
	single_data_header header;
	INFO::npc_definite_info info;
};

struct npc_definite_info_list_discard 		//NPC_DEFINITE_INFO_LIST
{
	multi_data_header header;
	INFO::npc_definite_info list[0];
};

struct npc_enter_scene			//NPC_ENTER_SCENE
{
	single_data_header header;
	unsigned short init_action;	//初始动作， 如果为0 代表没有动作
	INFO::npc_definite_info info;
};

struct npc_enter_slice			//NPC_ENTER_SLICE
{
	single_data_header header;
	INFO::npc_definite_info info;
};

struct matter_info			//MATTER_INFO
{
	single_data_header header;
	INFO::matter_info info;
};

struct matter_info_list			//MATTER_INFO_LIST
{
	multi_data_header header;
	INFO::matter_info list[0];
};

struct matter_enter_scene		//MATTER_ENTER_SCENE
{
	single_data_header header;
	INFO::matter_info info;
};

struct subobject_info			//SUBOBJECT_INFO
{
	single_data_header header;
	INFO::subobject_info info;
};

struct subobject_info_list		//SUBOBJECT_INFO_LIST
{
	multi_data_header header;
	INFO::subobject_info list[0];
};

struct subobject_enter_scene		//SUBOBJECT_ENTER_SCENE
{
	single_data_header header;
	INFO::subobject_info info;
};

struct subobject_enter_slice		//SUBOBJECT_ENTER_SLICE
{
	single_data_header header;
	INFO::subobject_info info;
};

struct object_leave_slice		//OBJECT_LEAVE_SLICE
{
	single_data_header header;
	int new_object_id;
};

struct object_leave_scene		//OBJECT_LEAVE_SCENE
{
	single_data_header header;
	int new_object_id;
	unsigned char type;			//0: 消失 1:....
};

struct self_data_send_end		//SELF_DATA_SEND_END
{
	single_data_header header;
};

struct object_move			//OBJECT_MOVE
{
	single_data_header header;
	int new_object_id;			//移动的对象ID
	A3DVECTOR3 pos;				//位置
	unsigned int flags;			//标志
	unsigned short move_dir;		//移动方向
	//unsigned short use_time;		//上一移动 使用的时间 单位是ms
	unsigned short speed;			//移动速度
	//if(flag & 0x0800)
	//unsigned short face_dir;		//朝向
	//if(flag & 0x0004)
	//unsigned char jump_type;		//跳跃类型
	//if(flag & 0x4000)
	//unsigned char land_type;		//疾落类型
	//if(flag & 0x10000)
	//unsigned short pitch;		//俯仰角度
	//if(flag & 0x80000)
	//unsigned short moveidle_act_index;		//不规则移动动作
};

struct object_turn			//OBJECT_TURN
{
	single_data_header header;
	int new_object_id;
	unsigned int flags;
	unsigned short dir;
};

struct out_of_sight_list		//OUT_OF_SIGHT_LIST
{
	single_data_header header;
	unsigned short count;
	int  id_list[0];			//new id
};

struct error_message			//ERROR_MESSAGE
{
	single_data_header header;
	unsigned char action_type;
	unsigned char show_type;
	unsigned short msg;
};

struct team_inst_error			//TEAM_INST_ERROR
{
	single_data_header header;
	unsigned short msg;
	unsigned char count;
	ruid_t role_id[0];
	unsigned short err_mask[0];
};

struct extend_property
{
	single_data_header header;
	//extend_prop	prop;			//不想引用头文件,所以注释掉了
};

struct server_timestamp			//SERVER_TIMESTAMP
{
	single_data_header header;
	int cur_time;
	int shutdown_timestamp;
};

struct inventory_data			//INVENTORY_DATA
{
	single_data_header header;
	unsigned char location;
	uint64_t bind_money;			//绑定币
	uint64_t trade_money;			//交易币
	unsigned short base_size;
	unsigned short effect_size;
	unsigned short inv_size;
	unsigned int content_length;
	char content[0];
	//content 结构
	//struct
	//{
	//	unsigned short count;
	//	struct
	//	{
	//		//单个物品的详细信息结构,下面的都用这种
	//		unsigned short index;
	//		int tid;
	//		int expire_date;
	//		unsigned int proctype_state;
	//		unsigned int count;
	//		unsigned short buf_size;
	//		char buf[buf_size];
	//	}item[count];
	//};
	ruid_t roleid;				//特殊需要广播包裹带ID
};

struct task_data			//TASK_DATA
{
	single_data_header header;
	unsigned int active_list_size;
	char active_list[0];
	unsigned int finished_list_size;
	char finished_list[0];
	unsigned int finished_time_size;
	char finished_time[0];
	int timestamp;
};

struct notify_pos			//NOTIFY_POS
{
	single_data_header header;
	unsigned short scene_tag;
	A3DVECTOR3 pos;
	unsigned short dir;
	unsigned char move_stamp;	//新的移动时戳
	unsigned char is_stage;		//是否版面变更带来的坐标移动
};

struct object_notify_prop		//OBJECT_NOTIFY_PROP
{
	single_data_header header;
	int new_obj_id;
};

struct object_notify_prop_delta		//OBJECT_NOTIFY_PROP_DELTA
{
	single_data_header header;
	ruid_t object_id;
	int new_obj_id;
	char pb_buf[0]; // PB::data_RegionNotify
};

struct object_notify_pozhan		//OBJECT_NOTIFY_POZHAN
{
	single_data_header header;
	ruid_t object_id;
	short pozhan;
};

struct unlock_item_slot			//UNLOCK_ITEM_SLOT
{
	single_data_header header;
	unsigned char location;
	unsigned short index;
};

struct get_item_info			//GET_ITEM_INFO
{
	single_data_header header;
	unsigned char location;
	unsigned short index;
	int item_tid;
	int expire_date;
	unsigned int state;
	unsigned int count;
	unsigned short buf_size;
	char buf[0];
};

struct swap_item			//SWAP_ITEM
{
	single_data_header header;
	unsigned char location1;
	unsigned short index1;
	unsigned char location2;
	unsigned short index2;
	ruid_t roleid;				//特殊需要广播包裹带ID
};

struct move_item			//MOVE_ITEM
{
	single_data_header header;
	unsigned char src_location;
	unsigned short src_index;
	unsigned char dst_location;
	unsigned short dst_index;
	unsigned int dst_state;
	unsigned int count;
	ruid_t roleid;				//特殊需要广播包裹带ID
};

struct inc_item				//INC_ITEM
{
	single_data_header header;
	int type;			//获得方式
	unsigned char location;			//栏位
	unsigned short index;			//位置索引
	int item_tid;				//物品模板ID
	int expire_date;			//过期时间
	unsigned int state;			//state
	unsigned int count;			//增加数量 可交易
	unsigned short content_size;		//如果原来地方有物品,这个为0
	char content_buf[0];
	ruid_t roleid;				//特殊需要广播包裹带ID
};

struct dec_item				//DEC_ITEM
{
	single_data_header header;
	int type;			//掉落方式
	unsigned char location;		//栏位
	unsigned short index;		//索引
	unsigned int count;			//减少数量
	ruid_t roleid;				//特殊需要广播包裹带ID
};

struct team_follow_invite		//TEAM_FOLLOW_INVITE
{
	single_data_header header;
	ruid_t leader_id;			//队长id
};

struct inc_money			//INC_MONEY
{
	single_data_header header;
	int inc_type;			//增加金钱的类型
	unsigned char money_type;		//钱币类型 0 绑定币 1 交易币
	unsigned char location;
	uint64_t inc;				//增加化量,如果为0表示通知钱数
	uint64_t money;				//增加后的当前金钱数量
};

struct dec_money			//DEC_MONEY
{
	single_data_header header;
	int dec_type;			//减少金钱的类型
	unsigned char money_type;		//钱币类型 0 绑定币 1 交易币
	unsigned char location;
	uint64_t dec;				//减少数量
	uint64_t money;				//减少后的当前金钱数量
};

struct enter_team_follow_mode		//ENTER_TEAM_FOLLOW_MODE
{
	single_data_header header;
	int player_id;
};

struct leave_team_follow_mode		//LEAVE_TEAM_FOLLOW_MODE
{
	single_data_header header;
	int player_id;
	int reason;		//离开组队跟随状态的原因
};

struct invalid_object			//INVALID_OBJECT
{
	single_data_header header;
	ruid_t object_id;
};

struct npc_greeting			//NPC_GREETING
{
	single_data_header header;
	ruid_t provider_npc_id;
};

struct title_list			//TITLE_LIST
{
	single_data_header header;
	unsigned short count;
	//struct title_t
	//{
	//int title_id;
	//unsigned char size;
	//char data[0];
	//};
	//title_t title_list[0];
};

struct add_title			//ADD_TITLE
{
	single_data_header header;
	int title_id;
	int time_expire;
};

struct del_title			//DEL_TITLE
{
	single_data_header header;
	int title_id;
};

struct player_change_title		//PLAYER_CHANGE_TITLE
{
	single_data_header header;
	ruid_t player_id;
	int title_id;				//称号id
};

struct illustration_data		//ILLUSTRATION_DATA
{
	single_data_header header;
	unsigned short size;
	unsigned char data[0];
};

struct illustration_change		//ILLUSTRATION_CHANGE
{
	single_data_header header;
	unsigned short index;
	unsigned char value;
};

struct discovermap_data			//DISCOVERMAP_DATA
{
	single_data_header header;
	unsigned short size;
	unsigned char data[0];
};

struct discovermap_change		//DISCOVERMAP_CHANGE
{
	single_data_header header;
	unsigned short index;
	unsigned char value;
};

struct reputation_data			//REPUTATION_DATA
{
	single_data_header header;
	char compress;
	unsigned short count;
	unsigned int size;
	char reputation[0];
};

struct reputation_change		//REPUTATION_CHANGE
{
	single_data_header header;
	unsigned short index;
	int reputation;
	int type;//新加的获得声望的类型
};

struct produce_recipe_data		//PRODUCE_RECIPE_DATA
{
	single_data_header header;
	unsigned short count;
	struct recipe_t
	{
		int id;
		unsigned char level;
		unsigned int recipe_point; //配方掌握度
	} list[0];
};

struct produce_recipe_change		//PRODUCE_RECIPE_CHANGE
{
	single_data_header header;
	int recipe_id;
	unsigned char recipe_level;
	unsigned int recipe_point; //配方掌握度
};

//用PB::gp_cooldown替代
struct cooldown_data			//COOLDOWN_DATA
{
	single_data_header header;
	unsigned short count;
	struct
	{
		unsigned short index;
		unsigned int cur_time;
		unsigned int max_time;
	} list[0];
};

//用PB::gp_cooldown替代
struct set_cooldown			//SET_COOLDOWN
{
	single_data_header header;
	unsigned short index;
	unsigned int time;
};

struct update_gfx_state			//UPDATE_GFX_STATE
{
	single_data_header header;
	ruid_t object_id;
	uint64_t state[8];
	unsigned char state_params[8];
};

struct auto_bot_start			//AUTO_BOT_START
{
	single_data_header header;
};

struct auto_bot_stop			//AUTO_BOT_STOP
{
	single_data_header header;
};

struct cash_info			//CASH_INFO
{
	single_data_header header;
	int64_t unbound_cash;
	int64_t bound_cash;
};

struct receive_exp			//RECEIVE_EXP
{
	single_data_header header;
	int type;			//获得经验的类型
	int64_t exp_inc;				//经验变化量,如为0,则表示了当前经验数
	uint64_t cur_exp;			//增加经验后的当前经验值
	int64_t stored_exp;				//exp_inc里有多少是储存经验
	uint64_t cur_stored_exp;	//当前存储经验
};

struct object_level_up			//OBJECT_LEVEL_UP
{
	single_data_header header;
	ruid_t object_id;			//目标ID 可能是玩家也可能是宠物
	unsigned char new_level;		//升级后的级别
};

struct player_prof_level_up		//PLAYER_PROF_LEVEL_UP
{
	single_data_header header;
	ruid_t player_id;
	unsigned char prof;
	unsigned char new_prof_level;
};

struct player_change_prof		//PLAYER_CHANGE_PROF
{
	single_data_header header;
	ruid_t player_id;
	unsigned char prof;
	unsigned char prof_level;
};

struct inventory_size			//INVENTORY_SIZE
{
	single_data_header header;
	unsigned char location;
	unsigned short base_size;
	unsigned short effect_size;
	unsigned short max_size;
};

struct safe_lock			//SAFE_LOCK
{
	single_data_header header;
	unsigned char msgtype;			//0: set(role login); 1: unlock
	int inactive_timestamp;			//当前安全锁的失效时间
	int locktime_now;			//当前安全锁时间设定，单位：秒
	int locktime_new;			//新的安全锁时间设定，单位：秒
	int newlock_active_timestamp;		//新安全锁时间设定的生效时间
	int urgentlock_inactive_timestamp;	//紧急控制的失效时间。如果>当前时间，则处于紧急控制状态；反之处于正常安全锁状态
};

struct player_set_slogan		//PLAYER_SET_SLOGAN
{
	single_data_header header;
	ruid_t player_id;
	unsigned char is_active;
	unsigned char type;
};

struct player_set_team_data		//PLAYER_SET_TEAM_DATA
{
	single_data_header header;
	ruid_t player_id;
	unsigned char purpose;
	int loot_rule;
};

struct other_equip_detail		//OTHER_EQUIP_DETAIL
{
	single_data_header header;
	ruid_t player_id;
	unsigned int size;
	char content[0];
};

struct object_buff			//OBJECT_BUFF
{
	single_data_header header;
	ruid_t object_id;
	unsigned short count;
	struct buff_t_s
	{
		unsigned short id;
		unsigned char level;
		float data[5];
		unsigned char size;
		char from[0];
	};
	buff_t_s list[0];
};

struct self_buff			//SELF_BUFF
{
	single_data_header header;
	unsigned short count;
	struct buff_t
	{
		unsigned short id;
		unsigned char level;
		int endtime;
		float data[5];
		unsigned char size;
		char from[0];
	};
	buff_t list[0];
};

struct mail_info			//MAIL_INFO
{
	single_data_header header;
	unsigned short system_new_mail_count;		//系统新邮件
	unsigned short system_total_mail_count;		//系统总邮件
	unsigned char system_mail_usage;		//系统邮件使用量 百分比0-100
	unsigned short player_new_mail_count;		//个人新邮件
	unsigned short player_total_mail_count;		//个人总邮件
	unsigned char player_mail_usage;		//个人邮件使用量 百分比0-100
	unsigned short offline_msg_count;		//离线消息数量
	unsigned short auction_total_count;		//拍卖行邮件总数量
	unsigned short auction_unread_count;	//拍卖行邮件未读数量
	unsigned short corps_auction_total_count;	//社团拍卖行邮件总数量
	unsigned short corps_auction_unread_count;	//社团拍卖行邮件未读数量
	unsigned short un_recycle_total_count;	//不循环邮件总数量
	unsigned short un_recycle_unread_count;	//不循环邮件未读数量
	unsigned short bouquet_total_count;		//花束邮件总数量
	unsigned short bouquet_unread_count;	//花束邮件未读数量
	unsigned short pray_total_count;		//祈愿邮件总数量
	unsigned short pray_unread_count;		//祈愿邮件未读数量
};

struct skill_data			//SKILL_DATA
{
	single_data_header header;
	unsigned short count;
	struct skill_t
	{
		unsigned short skill_id;
		unsigned char skill_level;
	};
	skill_t skill_list[0];
};

struct learn_skill			//LEARN_SKILL
{
	single_data_header header;
	unsigned short skill_id;
	unsigned char skill_level;
};

struct self_hp				//SELF_HP
{
	single_data_header header;
	float cur_hp;
};

struct object_perform_skill		//OBJECT_PERFORM_SKILL
{
	single_data_header header;
	int object_newid;
	unsigned short skill_id;
	unsigned char attack_stage;
	unsigned char extra_data_mask;

	//extra_data_mask & PERFORM_FLAGS_TARGET_LIST
	unsigned char target_count;
	int target_newid_list[0];
	//extra_data_mask & PERFORM_FLAGS_MOVE
	A3DVECTOR3 pos;
	//extra_data_mask & PERFORM_FLAGS_DIR
	unsigned short dir;
	//extra_data_mask & PERFORM_FLAGS_TIME_CHANGED
	unsigned short ms_time;
	//extra_data_mask & PERFORM_FLAGS_1ST_PERFORM
	int prior_target_newid;
	//extra_data_mask & PERFORM_FLAGS_SEND_LEVEL
	unsigned char skill_level;
};
//unsigned char cycle_count;

struct self_level			//SELF_LEVEL
{
	single_data_header header;
	unsigned char level;
};

struct cannot_cast_skill		//CANNOT_CAST_SKILL
{
	single_data_header header;
	unsigned short tick;
	unsigned short skill_id;
	unsigned char skill_level;
	unsigned char skill_sn;
	unsigned char is_buf_skill;		//是否为缓冲技能
	unsigned char reason;			//原因
};

struct task_var_data			//TASK_VAR_DATA
{
	single_data_header header;
	unsigned short size;
	char data[0];
};

struct object_start_action		//OBJECT_START_ACTION
{
	single_data_header header;
	unsigned short tick;
	ruid_t object_id;			//开始action对象id
	unsigned short action_type;		//开始的action类型
	unsigned short tick_time;		//持续时间,单位tick
	unsigned int param;			//开始的action的参数,不同的action对这个值有不同的解释
	unsigned int param2;
};

struct object_stop_action		//OBJECT_STOP_ACTION
{
	single_data_header header;
	unsigned short tick;
	//ruid_t object_id;
	int new_id;
	unsigned short action_type;		//停止的action类型
	unsigned int param;			//停止的action的参数,不同的action对这个值有不同的解释
	unsigned int param2;			//停止的action的参数2,不同的action对这个值有不同的解释
};

struct object_do_emote			//OBJECT_DO_EMOTE
{
	single_data_header header;
	ruid_t object_id;
	ruid_t taget_id;
	unsigned short emote_id;
	unsigned short mask;
};

struct region_setting			//region_setting
{
	single_data_header header;
	unsigned int mask;
};

struct player_revival			//PLAYER_REVIVAL
{
	single_data_header header;
	ruid_t player_id;
	A3DVECTOR3 pos;
	unsigned char type;			//复活的类型 0: 回城复活 1: 原地复活
};

struct player_pk_level			//PLAYER_PK_LEVEL
{
	single_data_header header;
	ruid_t player_id;
	short pk_level;
};

struct player_change_pk_setting		//PLAYER_CHANGE_PK_SETTING
{
	single_data_header header;
	ruid_t player_id;
	unsigned char mask;
};

struct fight_back_list_add		//FIGHT_BACK_LIST_ADD
{
	single_data_header header;
	ruid_t player_id;
};

struct fight_back_list_sub		//FIGHT_BACK_LIST_SUB
{
	single_data_header header;
	ruid_t player_id;
};

struct family_mafia_info		//FAMILY_MAFIA_INFO
{
	single_data_header header;
	ruid_t player_id;
	int family_id;
	ruid_t mafia_id;
	unsigned char mafia_rank;
	unsigned char mafia_domain;
	unsigned char mafia_owner;
};

struct open_loot			//OPEN_LOOT
{
	single_data_header header;
	ruid_t loot_matter_id;
};

struct close_loot			//CLOSE_LOOT
{
	single_data_header header;
	unsigned short tick;
	ruid_t loot_matter_id;
};

struct item_info_in_loot		//ITEM_INFO_IN_LOOT
{
	single_data_header header;
	unsigned short tick;
	unsigned char loot_type; //0 loot matter 1 普通矿物
	char pet_index;	//如果是人的话这个是-1
	ruid_t loot_matter_id;
	tid_t loot_tid;
	char content[0];
	//content 结构
	//struct
	//{
	//	unsigned short count;
	//	struct
	//	{
	//		unsigned short index;
	//		int tid;
	//		int expire_date;
	//		int state;
	//		unsigned int count;
	//		unsigned short buf_size;
	//		char buf[buf_size];
	//	}item[count];
	//};
};

struct pickup_item_in_loot		//PICKUP_ITEM_IN_LOOT
{
	single_data_header header;
	ruid_t loot_matter_id;
	unsigned char index;
	int item_tid;
};

struct loot_privilege			//LOOT_PRIVILEGE
{
	single_data_header header;
	ruid_t loot_matter_id;
	unsigned char can_pickup;
};

struct player_die			//PLAYER_DIE
{
	single_data_header header;
	ruid_t player_id;
	ruid_t attacker_id;
	unsigned short tick;
	skill_id_t kill_skill_id;
	unsigned char kill_stage;
	unsigned char state;//0=真死，1=死后续航，假死
};

struct npc_die				//NPC_DIE
{
	single_data_header header;
	ruid_t npc_id;
	ruid_t attacker_id;
	unsigned short tick;
	unsigned char corpse_delay;	//尸体残留
	skill_id_t kill_skill_id;
	unsigned char kill_stage;
};

struct player_use_item			//PLAYER_USE_ITEM
{
	single_data_header header;
	ruid_t player_id;
	int item_tid;
};

struct player_use_item_with_target	//PLAYER_USE_ITEM_WITH_TARGET
{
	single_data_header header;
	ruid_t player_id;
	int item_tid;
	ruid_t target_id;
};

struct player_use_item_with_arg		//PLAYER_USE_ITEM_WITH_ARG
{
	single_data_header header;
	ruid_t player_id;
	int item_tid;
	unsigned char arg_size;
	char arg_buf[0];
};

struct roll_item_info			//ROLL_ITEM_INFO
{
	single_data_header header;
	ruid_t matter_id;
	char content[0];
	//content 结构
	//struct
	//{
	//	unsigned short count;
	//	struct
	//	{
	//		unsigned short index;
	//		int tid;
	//		int expire_date;
	//		unsigned int proctype_state;
	//		unsigned int  count;
	//		unsigned short buf_size;
	//		char buf[buf_size];
	//	}item[count];
	//};
};

struct player_roll_point		//PLAYER_ROLL_POINT
{
	single_data_header header;
	ruid_t player_id;
	int item_tid;
	unsigned char roll_type;
	unsigned char roll_point;		//0-100
};

struct pet_info				//PET_INFO
{
	single_data_header header;
	unsigned char rmb_capacity;
	unsigned char can_summon_count;
	unsigned char can_combine_count;
};

struct depository_password_state	//DEPOSITORY_PASSWORD_STATE
{
	single_data_header header;
	unsigned char has_password;
	unsigned int unlock_time;
};

struct be_healed			//be_healed
{
	single_data_header header;
	unsigned int attacker;
	unsigned int victim;
	float hp;
};

struct be_hurt				//BE_HURT
{
	single_data_header header;
	unsigned int attacker;
	unsigned int victim;
	float damage;
};

struct object_be_attacked		//OBJECT_BE_ATTACKED
{
	single_data_header header;
	unsigned short tick;
	int attacker_new_id;
	int target_new_id;
	float damage;
	unsigned short skill_id;
	unsigned char skill_level;
	unsigned char attack_stage;
	unsigned int flags;
	//flags&0x00008000: 被控制位移(比如击退)
	//or flags&0x00040000: 同步位置(初进控制+非位移控制)
	A3DVECTOR3 pos;
	//flags&0x00008000: 被控制位移(比如击退)
	//or flags&0x00040000: 同步位置(初进控制+非位移控制)
	//or flags&0x00080000: 控制改变朝向
	unsigned short dir;
	//flags&0x00010000: 被控制
	char prev_ctrl_type;
	char ctrl_type;
	unsigned short ctrl_time;
	//flags&0x01000000: 闪电链
	int front_new_id;
};

struct control_end			//CONTROL_END
{
	single_data_header header;
	unsigned short tick;
	int new_id;
	char mask;//0x01 表示end_by_jieyun
	//maks & 0x02 有以下字段
	char resume_ctrl_type;
	unsigned short resume_ctrl_time;
};

struct object_be_enchanted		//OBJECT_BE_ENCHANTED
{
	single_data_header header;
	unsigned short tick;
	int attacker_new_id;
	int target_new_id;
	int arg;
	unsigned short skill_id;
	unsigned char skill_level;
	unsigned int flags;
	unsigned char attack_stage;
};

struct pk_setting			//PK_SETTING
{
	single_data_header header;
	unsigned char mask;
};

struct summon_pet			//SUMMON_PET
{
	single_data_header header;
	unsigned char pet_index;
	int pet_tid;
	ruid_t pet_id;
};

struct recall_pet			//RECALL_PET
{
	single_data_header header;
	unsigned char pet_index;
	int pet_tid;
	ruid_t pet_id;
};

struct combine_pet			//COMBINE_PET
{
	single_data_header header;
	unsigned char pet_index;
	int pet_tid;
	int enhance[6];
};

struct uncombine_pet			//UNCOMBINE_PET
{
	single_data_header header;
	unsigned char pet_index;
	int pet_tid;
};

struct team_follow_move			//TEAM_FOLLOW_MOVE
{
	single_data_header header;
	int player_id;
	A3DVECTOR3 pos;
	unsigned short speed;
	int fly;
	unsigned int flags;
	//if (flags & 0x0004)
	//unsigned char jump_type;
	//if (flags & 0x4000)
	//unsigned char land_type;
	//if (flags & 0x0800)
	//unsigned short face_dir;
	//if(flag & 0x10000)
	//unsigned short pitch;		//俯仰角度
	//if(flag & 0x80000)
	//unsigned short moveidle_act_index;		//不规则移动动作
};

struct equipment_info_change		//EQUIPMENT_INFO_CHANGE
{
	single_data_header header;
	unsigned short flag;
	unsigned short crc;
	ruid_t id;
	unsigned int mask_add;
	unsigned int mask_del;
	struct
	{
		int item_tid;
		unsigned char star;
	} data_add[0]; //0 ~ 16
};

struct pet_recv_exp			//PET_RECV_EXP
{
	single_data_header header;
	unsigned char pet_index;
	unsigned int exp_added;			//增加的经验
};

struct pet_level_up			//PET_LEVEL_UP
{
	single_data_header header;
	unsigned char pet_index;
	unsigned char new_level;
	int64_t cur_exp;
};

struct pet_hp				//PET_HP
{
	single_data_header header;
	unsigned char pet_index;
	int cur_hp;
	int max_hp;
};

struct pet_energy			//PET_ENERGY
{
	single_data_header header;
	unsigned char pet_index;
	unsigned short energy;
};

struct pet_life				//PET_LIFE
{
	single_data_header header;
	unsigned char pet_index;
	unsigned short cur_life;
};

struct pet_dead				//PET_DEAD
{
	single_data_header header;
	unsigned char pet_index;
	int can_active_timestamp;
};

struct object_change_visual_effect	//OBJECT_CHANGE_VISUAL_EFFECT
{
	single_data_header header;
	unsigned short tick;
	ruid_t id;
	unsigned int visual_effect;
};

struct pet_property
{
	single_data_header header;
	unsigned char pet_index;
	//extend_prop	prop;			//不想引用头文件,所以注释掉了
};

struct player_team_state		//PLAYER_TEAM_STATE
{
	single_data_header header;
	ruid_t id;				//玩家id
	unsigned char state;			//0 没队伍 1, 队长, 2 队员
	int team_id;				//队伍id
};

struct team_member_data			//TEAM_MEMBER_DATA
{
	single_data_header header;
	//这个变化不频繁,同步不是很准确
	unsigned char member_count;		//当前线队伍总人数
	unsigned char data_count;		//本次协议更新信息的人数
	ruid_t leader_id;			//队长id
	// 需要发送给客户端的队伍数据
	struct member_entry
	{
		ruid_t id;		//id
		unsigned char level;		//级别
		unsigned char prof;		//职业
		unsigned char prof_level;	//职业级别
		unsigned char team_follow;	//1表示处于组队跟随状态
		float cur_hp;			//血
		float max_hp;
		float cur_mp0;			//魔0
		float max_mp0;
		unsigned char pvp_state; //1表示处于pvp状态
	};
	member_entry data[0];
};

struct team_member_pos			//TEAM_MEMBER_POS
{
	//这个比较频繁
	single_data_header header;
	ruid_t id;
	int world_tid;
	unsigned short scene_tag;
	A3DVECTOR3 pos;
	unsigned short mid;
};

struct team_member_pickup		//TEAM_MEMBER_PICKUP
{
	single_data_header header;
	ruid_t player_id;
	int item_tid;
	unsigned short item_count;
};

struct pet_fealty			//PET_FEALTY
{
	single_data_header header;
	unsigned char pet_index;
	unsigned short cur_fealty;
};

struct pet_potential_change		//PET_POTENTIAL_CHANGE
{
	single_data_header header;
	unsigned char pet_index;
	unsigned short potential[8];
};

struct pet_name_change			//PET_NAME_CHANGE
{
	single_data_header header;
	unsigned char pet_index;
	unsigned char name_size;
	char name_buf[0];
};

struct npc_change_name			//NPC_CHANGE_NAME
{
	single_data_header header;
	ruid_t npc_id;
	unsigned char name_size;
	char name_buf[0];
};

struct assigned_potential		//ASSIGNED_POTENTIAL
{
	single_data_header header;
	unsigned short potential[8];
	unsigned char isreset;//0:不是reset,非0表示reset
};

struct inv_arrange_re			//INV_ARRANGE_RE
{
	single_data_header header;
	unsigned char location;
	unsigned char result;
};

struct resurrect_ready			//RESURRECT_READY
{
	single_data_header header;
	unsigned short delay;			//多久之后自动复活
	unsigned char can_revive;		//是否可以原地复活
	unsigned char can_cash_revive;		//是否可以使用元宝原地复活
	unsigned char can_revive_town;		//是否可以回复活点
	unsigned short cash_rerive_count;	//已经元宝复活的次数
	int need_cash;				//如果可以元宝复活，需要元宝数
};

struct object_change_faction		//OBJECT_CHANGE_FACTION
{
	single_data_header header;
	ruid_t id;
	unsigned int faction;
};

struct complete_achievement_data_t
{
	int tid;
	int timestamp;
};

struct active_achievement_data_t
{
	int tid;
	unsigned char event_cond_count;
	int event_cond_data[0];
};

struct simple_achievement_data		//SIMPLE_ACHIEVEMENT_DATA	上线发简单数据
{
	single_data_header header;
	unsigned short complete_count;        //完成的成就数量
	//complete_achievement_data_t temp[0]; //完成的成就数据
	unsigned short active_count;          //进行中的成就数量
	//int tid_list[0];                     //进行中的成就ID列表
};

struct active_achievement_data		//ACTIVE_ACHIEVEMENT_DATA	进行中的成就详细数据
{
	single_data_header header;
	unsigned short count;
	active_achievement_data_t data[0];
};

struct achievement_active		//ACHIEVEMENT_ACTIVE		成就服务器端激活
{
	single_data_header header;
	int tid;
};

struct achievement_complete		//ACHIEVEMENT_COMPLETE		成就完成
{
	single_data_header header;
	complete_achievement_data_t data;
};

struct other_player_achievement
{
	single_data_header header;
	ruid_t player_id;
	unsigned short complete_count;                 //完成的成就数量
	unsigned short active_count;                   //进行中的成就数量
	//complete_achievement_data_t complete_data[0]; //完成的成就数据
	//active_achievement_data_t active_data[0];	   //进行中的成就数据
};

struct achievement_message		//ACHIEVEMENT_MESSAGE		成就信息,单法,组播,广播
{
	single_data_header header;
	ruid_t player_id;
	int tid;
	unsigned char name_len;
	char name[0];
};

struct check_ping			//CHECK_PING
{
	single_data_header header;
	unsigned short seq;			//客户端发来的序列
	unsigned short tick;			//当前服务器tick
};

struct subobject_take_effect		//SUBOBJECT_TAKE_EFFECT
{
	single_data_header header;
	unsigned short tick;
	int object_newid;
	unsigned short perform_id;		//执行的技能段id
	A3DVECTOR3 pos;				//产生效果的位置
	unsigned char target_count;		//相关的目标数
	int target_id_list[0];			//相关的目标id表
	char range_type;
	float affect_radius;
	//float affect_radius2;
	float affect_length;
	float affect_angle;
};

struct item_buy_back			//ITEM_BUY_BACK
{
	single_data_header header;
	unsigned short buy_back_index;		//在回购栏里的索引
};

struct object_change_lock_enemy_state	//OBJECT_CHANGE_ENEMY_STATE
{
	single_data_header header;
	ruid_t id;
	unsigned char state;			//0为无锁敌状态
};

struct enter_server_move_control_mode	//ENTER_SERVER_MOVE_CONTROL_MODE
{
	single_data_header header;
	unsigned short tick;
	ruid_t id;
	unsigned short ms_time;			//时间
	A3DVECTOR3 stop_pos;			//预计停止位置
	unsigned char type;			//0直线 1抛物线
};

struct leave_server_move_control_mode	//LEAVE_SERVER_MOVE_CONTROL_MODE
{
	single_data_header header;
	unsigned short tick;
	ruid_t id;
};

struct trade_invite			//TRADE_INVITE
{
	single_data_header header;
	ruid_t player_id;
};

struct trade_reply			//TRADE_REPLY
{
	single_data_header header;
	ruid_t player_id;
	unsigned char reply;			//0:拒绝 1:同意
};

struct trade_start			//TRADE_START
{
	single_data_header header;
	ruid_t player_id1;
	ruid_t player_id2;
};

struct trade_state			//TRADE_STATE
{
	single_data_header header;
	unsigned char status;
	unsigned char param;
	//enum TRADE_STATE //交易状态机
	//{
	//	TS_WAIT_LOCK,			//等待锁定,准备状态 param 含义 0 1 2 3 4 5 6 7
	//	TS_A_LOCK,
	//	TS_B_LOCK,
	//	TS_WAIT_CONFIRM,		//等待确认,锁定完成状态
	//	TS_A_CONFIRM,
	//	TS_B_CONFIRM,
	//	TS_CLOSE,			//交易完成
	//	TS_DISCARD,			//交易中断
	//};
};

struct trade_set_money			//TRADE_SET_MONEY
{
	single_data_header header;
	ruid_t player_id;
	uint64_t trade_money;
};

struct trade_move_item
{
	single_data_header header;
	ruid_t player_id;
	unsigned char mode;			//0: add 1:sub
	unsigned short tarde_index;		//对应交易栏索引
	unsigned char location;			//栏位
	char buf[0];
	//	struct
	//	{
	//		unsigned short index;
	//		int tid;
	//		int expire_date;
	//		int proctype_state;
	//		unsigned int count;
	//		unsigned short buf_size;
	//		char buf[buf_size];
	//	}
};

struct toggle_invisible			//TOGGLE_INVISIBLE
{
	single_data_header header;
	unsigned char is_visible;
};

struct toggle_invincible		//TOGGLE_INVINCIBLE
{
	single_data_header header;
	unsigned char is_invincible;
};

struct self_market_info			//SELF_MARKET_INFO
{
	struct market_sell_item
	{
		unsigned char market_location;	//出售栏位号,普通物品和材料是放在一起的
		unsigned char market_index;	//出售索引
		unsigned char location;		//栏位
		unsigned short index;		//索引
		unsigned int count;			//卖出数量
		uint64_t price;			//单价
		unsigned char active;		//激活标志,不激活的意思是金钱满了,放不下了,无法出售物品
	};
	struct market_buy_item
	{
		unsigned char market_index;	//收购索引
		int tid;			//模板id
		unsigned int count;		//购买数量
		uint64_t price;			//单价
		unsigned char active;		//激活标志,不激活表示相应的包裹满了,放不下了,无法收购物品
	};
	single_data_header header;
	//结构解释如下
	//unsigned char name_len;
	//char name[0];
	//unsigned char sell_count;
	//market_sell_item sells[0];
	//unsigned char buy_count;
	//market_buy_item buys[0];
};

struct set_market_name_re		//SET_MARKET_NAME_RE
{
	single_data_header header;
};

struct market_add_sell_item		//MARKET_ADD_SELL_ITEM
{
	single_data_header header;
	unsigned char market_location;		//操作的出售栏位号,普通物品和材料是放在一起的
	unsigned char market_index;		//操作的出售索引
	unsigned char location;			//对应包裹栏位
	unsigned short index;			//对应包裹索引
	unsigned int count;			//出售数量
	uint64_t price;				//单价
};

struct market_sell_item_change_status	//MARKET_SELL_ITEM_CHANGE_STATUS
{
	single_data_header header;
	unsigned char market_location;		//操作的出售栏位号
	unsigned char market_index;		//操作的出售索引
	unsigned char active;			//是否激活
};

struct market_sub_sell_item		//MARKET_SUB_SELL_ITEM
{
	single_data_header header;
	unsigned char market_location;		//操作的出售栏位号
	unsigned char market_index;		//操作的出售索引
};

struct market_add_buy_item		//MARKET_ADD_BUY_ITEM
{
	single_data_header header;
	unsigned char market_index;		//操作的收购索引
	int tid;				//收购的物品数量
	unsigned int count;			//出售数量
	uint64_t price;				//单价
};

struct market_buy_item_change_status	//MARKET_BUY_ITEM_CHANGE_STATUS
{
	single_data_header header;
	unsigned char market_index;		//操作的收购索引
	unsigned char active;			//是否激活
};

struct market_sub_buy_item		//MARKET_SUB_BUY_ITEM
{
	single_data_header header;
	unsigned char market_index;		//操作的收购索引
};

struct player_open_market		//PLAYER_OPEN_MARKET
{
	single_data_header header;
	ruid_t player_id;
	unsigned char market_name_size;
	char market_name_buf[0];
};

struct player_close_market		//PLAYER_CLOSE_MARKET
{
	single_data_header header;
	ruid_t player_id;
};

struct player_market_info		//PLAYER_MARKET_INFO
{
	struct sell_node
	{
		unsigned char market_location;
		unsigned char market_index;
		unsigned char active;
		uint64_t price;			//单价
		int tid;			//物品类型
		int expire_date;		//过期时间
		unsigned int proctype_state;	//特殊状态
		unsigned int  count;		//剩余数量
		unsigned short content_length;
		char content[0];
	};

	struct buy_node
	{
		unsigned char market_index;
		unsigned char active;
		uint64_t price;			//单价
		int tid;			//物品类型
		unsigned int count;			//剩余数量
	};
	single_data_header header;
	ruid_t player_id;
	unsigned char market_seq;
	//结构解释如下
	//unsigned char sell_count;
	//sell_node item_list_sell[0];
	//unsigned char buy_count;
	//buy_node item_list_buy[0];
};

struct player_market_trade_success	//PLAYER_MARKET_TRADE_SUCCESS
{
	single_data_header header;
	ruid_t trader;
};

struct market_trade_item		//MARKET_TRADE_ITEM
{
	single_data_header header;
	unsigned char sell;
	unsigned char market_location;
	unsigned char market_index;
	unsigned int count;
	ruid_t trader;
};

struct scene_market_info		//SCENE_MARKET_INFO
{
	single_data_header header;
	unsigned char page;
	unsigned char count;
	//这里给什么 id seq name 待定,看需求?
};

struct market_clear			//MARKET_CLEAR
{
	single_data_header header;
	unsigned char mask;
};


struct learn_kungfu			//LEARN_KUNGFU
{
	single_data_header header;
	tid_t tid;
};

struct bind_invite			//BIND_INVITE
{
	single_data_header header;
	tid_t tid;
	ruid_t object_id;
};

struct bind_invite_reply		//BIND_INVITE_REPLY
{
	single_data_header header;
	tid_t tid;
	ruid_t object_id;
	unsigned char param;		//0代表同意，非0代表不同意
};

struct bind_request			//BIND_REQUEST
{
	single_data_header header;
	tid_t tid;
	ruid_t object_id;
};

struct bind_request_reply		//BIND_REQUEST_REPLY
{
	single_data_header header;
	tid_t tid;
	ruid_t object_id;
	unsigned char param;
};

struct bind_info			//BIND_INFO
{
	single_data_header header;
	tid_t tid;				//绑定模板ID
	int team_id;				//绑定参数,一般为队伍id表示组队限制
	ruid_t object_id;			//谁开始bind状态
	unsigned char pos_in_bind;		//在bind中的位置
	int bind_action_start_timestamp;
	unsigned char count;			//跟他一起bind的人数
	struct member_t
	{
		unsigned char pos;
		ruid_t id;
	};
	member_t bind_members[0];			//member列表
};

struct bind_stop			//BIND_STOP
{
	single_data_header header;
	int tid;				//这个tid本来不用带的,不过客户端逻辑上有需求......
	ruid_t object_id;
	unsigned char pos_in_bind;		//这个也一样
	unsigned char leave_type;		//离开原因
};

struct inc_force			//INC_FORCE
{
	single_data_header header;
	int type;			//获得功力的类型
	unsigned int inc;				//增加的功力,如为0,则表示了当前功力数
	unsigned int force;				//当前功力
};

struct dec_force			//DEC_FORCE
{
	single_data_header header;
	int type;			//减少经验的类型
	unsigned int dec;				//减少的功力
	unsigned int force;				//当前功力
};

struct npc_ascription_change		//NPC_ASCRIPTION_CHANGE
{
	single_data_header header;
	ruid_t npc_id;				//npcid
	ruid_t ascr_id;				//归属id,0表示无所有权,>0表示属于玩家id为player_id,<0表示属于队伍-id为队伍id
};

struct player_wallow_info		//PLAYER_WALLOW_INFO
{
	single_data_header header;
	unsigned char anti_wallow_active;
	unsigned char wallow_level;
	int play_time;
	int light_timestamp;
	int heavy_timestamp;
	int msg;
};

struct npc_change_shape			//NPC_CHANGE_SHAPE
{
	single_data_header header;
	ruid_t npc_id;
	unsigned char shape;
};

struct matter_change_status		//MATTER_CHANGE_STATUS
{
	single_data_header header;
	ruid_t matter_id;
	unsigned char status;			//0 未被挖, 1 正在被挖, 2 被挖完锁定等待, 3 矿物进入尸体状态, 4 通知当前玩家自己挖成功
};

struct object_action_seal_mask		//OBJECT_ACTION_SEAL_MASK
{
	single_data_header header;
	unsigned short tick;
	ruid_t object_id;
	//enum 技能里引用
	//{
	//	ACTIONMASK_ROOT		= 0x01, //禁止移动
	//	ACTIONMASK_DIET		= 0x02, //禁止进食
	//	ACTIONMASK_SILENT	= 0x04, //禁止技能
	//	ACTIONMASK_BATTLELOCK	= 0x08, //禁止其他一切
	//};
	unsigned short mask;
};

struct instance_info_t
{
	tid_t tid;			//模板ID
	int timestamp;			//最后一次进入时间戳
	unsigned char count;		//该类副本进入次数
	unsigned char addition_count;	//该类副本进入次数额外增加的上限
	unsigned char professional;	//是否专家副本
	//如果是 professional 则有下面数据
	int refresh_timestamp;		//刷新时间
	int use_time;			//使用时间,单位秒
	char max_difficulty;		//最大难度
};

struct player_instance_info		//PLAYER_INSTANCE_INFO
{
	single_data_header header;
	unsigned char count;			//副本种类数
	instance_info_t node[0];
};

struct player_instance_info_change	//PLAYER_INSTANCE_INFO_CHANGE
{
	single_data_header header;
	char mode;				//该副本模式,当前难度
	instance_info_t info;
};

struct kickout_instance			//KICKOUT_INSTANCE
{
	single_data_header header;
	unsigned short second;			//踢出时间 0为取消踢出
};

struct combat_state			//COMBAT_STATE
{
	single_data_header header;
	ruid_t object_id;
	unsigned char combat;			//0非战斗 1战斗
	unsigned char reason;			//0-127 退出原因 0普通退出 1死亡退出 128-255 进入原因 128普通战斗状态 129被呼救
	unsigned short tick;
};

struct object_target_change		//OBJECT_TARGET_CHANGE
{
	single_data_header header;
	ruid_t object_id;
	ruid_t target_id;
};

struct pull_horse			//PULL_HORSE
{
	single_data_header header;
	ruid_t object_id;
};

struct pet_script_message		//PET_SCRIPT_MESSAGE
{
	single_data_header header;
	unsigned char pet_index;
	unsigned short channel_id;
	unsigned short msg_id;
	unsigned short expression;
};

struct pet_train_info			//PET_TRAIN_INFO
{
	single_data_header header;
	unsigned char pet_index;
	unsigned char main_type;
	unsigned char sub_type;
	int end_timestamp;			//为0表示取消训练,或者训练完成
};

struct pet_identity			//PET_IDENTITY
{
	single_data_header header;
	unsigned char pet_index;
	int rand;				//pet的唯一id是由1个随机数加时戳组成的
	int timestamp;
};

struct pet_enhance_level		//PET_ENHANCE_LEVEL
{
	single_data_header header;
	unsigned char pet_index;
	unsigned char enhance_level;
};

struct pet_insane			//PET_INSANE
{
	single_data_header header;
	unsigned char pet_index;
	unsigned char insane;
};

struct object_change_control_state	//OBJECT_CHANGE_CONTROL_STATE
{
	single_data_header header;
	int object_newid;
	unsigned short tick;
	unsigned char state;
	unsigned short time;		//ms
	unsigned char mp2;
};

struct pet_layer			//PET_LAYER
{
	single_data_header header;
	unsigned char pet_index;
	unsigned char layer;
};

struct mine_can_gather_count_change 	//MINE_CAN_GATHER_COUNT_CHANGE
{
	single_data_header header;
	ruid_t object_id;
	int can_gather_count;
};

struct player_pet_say_hello	 	//PLAYER_PET_SAY_HELLO
{
	single_data_header header;
	ruid_t from_id;			//打招呼的人
	unsigned char name_size;
	char name[0];
	ruid_t to_id;			//打招呼的目标
	int pet_tid;				//宠物类型
	unsigned char emote;			//表情休闲动作
};

struct locate_player_re		 	//LOCATE_PLAYER_RE
{
	single_data_header header;
	unsigned char ret;			//0成功,其他失败,意义待定
	ruid_t player_id;			//追踪的玩家ID
	unsigned char line_id;			//线号,如果是副本,线号为0没意义
	int instance_tid;			//副本,0表示大世界
	unsigned short scene_tag;		//场景
	A3DVECTOR3 pos;				//位置
	unsigned char name_size;		//名字大小,最大40
	char name_buf[0];
};

struct equipment_durability 		//EQUIPMENT_DURABILITY
{
	single_data_header header;
	unsigned char location;			//栏位
	unsigned short index;			//索引
	unsigned int durability;			//当前耐久,0表示损坏
};

struct server_config_data		//SERVER_CONFIG_DATA
{
	single_data_header header;
	int inst;
	int region_time;
	int precinct_time;
	//int mall_time;
	int mall_version;
	int logic_level_limit; //当前已开放的最大角色等级
	unsigned char tick_per_second;		//服务器每秒的tick数
};

struct pet_xp				//PET_XP
{
	single_data_header header;
	unsigned char pet_index;
	unsigned char xp;
};

struct board_info			//BOARD_INFO
{
	single_data_header header;
	int old_id;				//没有是0
	unsigned char old_index;
	int new_id;
	unsigned char new_index;
	unsigned char stage;			//阶段 0: 准备 1: 开始 2: 完成
	int start_timestamp;
	int subscene_id;			//战场子场景ID
	//enum
	//{
	//	BFM_NONE                        = 0x00,
	//	BFM_KILL_ALL_SPECIFY_MONSTERS   = 0x01,
	//	BFM_KILL_SPECIAL_MONSTER        = 0x02,
	//	BFM_OPEN_SPECIAL_CTRL           = 0x04,
	//	BFM_ENTER_SPECIAL_AREA          = 0x08,
	//	BFM_ALL                         = BFM_KILL_ALL_SPECIFY_MONSTERS | BFM_KILL_SPECIAL_MONSTER | BFM_OPEN_SPECIAL_CTRL | BFM_ENTER_SPECIAL_AREA,
	//};
	unsigned int cur_board_finish_mask;
	unsigned char reset;
	unsigned char instance_mode;		//副本难度
};

struct reset_instance			//RESET_INSTANCE
{
	single_data_header header;
	unsigned char flag;			//0:失败重置,1:成功重置
	int wait_time;
};

struct group_self_join			//GROUP_SELF_JOIN
{
	single_data_header header;
	//自己加入一个组
	ruid_t group_id;			//组id
	int task_id;				//相关任务id
};

struct group_self_leave			//GROUP_SELF_LEAVE
{
	single_data_header header;
	//自己离开一个组
	ruid_t group_id;			//组id
};

struct group_member_join		//GROUP_MEMBER_JOIN
{
	//有目标加入我的组
	single_data_header header;
	ruid_t group_id;			//组id
	ruid_t object_id;			//目标id
};

struct group_member_leave		//GROUP_MEMBER_LEAVE
{
	single_data_header header;
	//有目标离开我的组
	ruid_t group_id;			//组id
	ruid_t object_id;			//目标id
};

struct group_member_data		//GROUP_MEMBER_DATA
{
	ruid_t group_id;			//组id
	unsigned char member_count;		//组人数
	unsigned char data_count;		//本次协议更新信息的人数
	ruid_t leader_id;			//组长id
	//需要发送给客户端的队伍数据
	struct member_entry
	{
		ruid_t id;				// id
		unsigned char level;			// 级别
		unsigned char prof;			// 职业
		unsigned char prof_level;		// 职业级别
		unsigned char combat_state;		// 战斗状态
		unsigned char online_state;		// 上线状态
		int family_id;				// 家族
		ruid_t mafia_id;			// 帮派
		unsigned int faction;			//阵营
	};
	member_entry data[0]; //类似组队其中npc部分数据无效
};

struct group_invite			//GROUP_INVITE
{
	//其他队友接到任务时可能会询问自己是否也加入组
	ruid_t group_id;			//组id
	int task_id;				//相关任务id
};

struct duel_invite			//DUEL_INVITE
{
	single_data_header header;
	ruid_t player_id;
	unsigned char prof;
	unsigned char level;
	int fighting_capacity;
};

struct duel_invite_reply		//DUEL_INVITE_REPLY
{
	single_data_header header;
	ruid_t player_id;
	unsigned char reply;			//0 同意 1 拒绝 2 超时 3 距离太远 4 状态不对
};

struct duel_prepare			//DUEL_PREPARE
{
	single_data_header header;
	ruid_t player_id;
	unsigned char delay;			//sec
};

struct duel_cancel			//DUEL_CANCEL
{
	single_data_header header;
	ruid_t player_id;
};

//struct duel_start			//DUEL_START
//{
//	single_data_header header;
//	ruid_t player_id;
//	ruid_t target_id;
//};

//struct duel_stop			//DUEL_STOP
//{
//	single_data_header header;
//	ruid_t player_id;
//};

struct duel_result			//DUEL_RESULT
{
	single_data_header header;
	ruid_t id1;
	unsigned char name1_size;
	char name1[0];
	ruid_t id2;
	unsigned char name2_size;
	char name2[0];
	unsigned char result;			//0 id1 win 1 id2 win 2 draw
};

struct npc_fight_policy			//NPC_FIGHT_POLICY
{
	single_data_header header;
	ruid_t npc_id;
	unsigned char policy;			//1呼救2逃跑
};

struct xyxw_invite			//XYXW_INVITE
{
	single_data_header header;
	ruid_t object_id;
};

struct xyxw_invite_reply		//XYXW_INVITE_REPLY
{
	single_data_header header;
	ruid_t object_id;
	unsigned char reply;
};

struct xyxw_request			//XYXW_REQUEST
{
	single_data_header header;
	ruid_t object_id;
};

struct xyxw_request_reply		//XYXW_REQUEST_REPLY
{
	single_data_header header;
	ruid_t object_id;
	unsigned char reply;
};

struct xyxw_info			//XYXW_INFO
{
	single_data_header header;
	ruid_t object_id;			//谁开始相依相偎状态
	unsigned char pos_in_xyxw;		//在相依相偎中的位置
	ruid_t xyxw_parter_id;
};

struct xyxw_stop			//XYXW_STOP
{
	single_data_header header;
	ruid_t object_id;
	unsigned char pos_in_xyxw;
};

struct player_skill_addon	//PLAYER_SKILL_ADDON
{
	single_data_header header;
	unsigned short skill_id;
	unsigned char skill_level;
};

struct player_extra_skill	//PLAYER_EXTRA_SKILL
{
	single_data_header header;
	unsigned short skill_id;
	unsigned char skill_level;
};

struct edit_diy_kungfu_re	//EDIT_DIY_KUNGFU_RE
{
	single_data_header header;
	int kungfu_id;
	unsigned char result;		//0成功 1失败
};

struct title_update		//TITLE_UPDATE
{
	single_data_header header;
	int title_id;
	unsigned char size;
	//char data[size];
};

struct lottery_prize			//LOTTERY_PRIZE
{
	single_data_header header;
	tid_t lottery_tid;			//彩票类型
	uint64_t prize_bind_money;		//获得金钱
	uint64_t prize_money;			//获得金钱
	int64_t prize_exp;			//获得经验
	unsigned char repu_count;
	struct repu_t
	{
		int index;
		int value;
	};
	//repu_t repus[repu_count];
	unsigned char item_count;		//获得物品数
	struct item_t
	{
		tid_t prize_item_tid;
		unsigned int  prize_item_count;
	};
	//item_t items[item_count];
};

struct self_vigor			//SELF_VIGOR
{
	single_data_header header;
	unsigned char index;
	float vigor;
	float offset;		//变化量，用于客户端显示变化值
};

struct world_params			//WORLD_PARAMS
{
	single_data_header header;
	unsigned char count;
	struct
	{
		int index;
		int value;
	} list[0];
};

struct world_param_change		//WORLD_PARAM_CHANGE
{
	single_data_header header;
	int index;
	int value;
};

struct instance_speak			//INSTANCE_SPEAK
{
	single_data_header header;
	unsigned short id;
};

struct pet_auto_pickup_flag		//PET_AUTO_PICKUP_FLAG
{
	single_data_header header;
	unsigned char pet_index;
	unsigned char flag;
};


struct scene_info_change
{
	single_data_header header;
	INFO::scene_info info;
};

struct help_mask_data			//HELP_MASK_DATA
{
	single_data_header header;
	unsigned short size;
	unsigned char data[0];
};

struct help_mask_change			//HELP_MASK_CHANGE
{
	single_data_header header;
	unsigned short index;
	unsigned char value;
};

struct home_info			//HOME_INFO
{
	single_data_header header;
	INFO::home_info info;
};

struct home_info_list			//HOME_INFO_LIST
{
	multi_data_header header;
	INFO::home_info list[0];
};

struct home_enter_scene			//HOME_ENTER_SCENE
{
	single_data_header header;
	INFO::home_info info;
};

//$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
struct player_skill_common_addon	//PLAYER_SKILL_COMMON_ADDON
{
	single_data_header header;
	int addon;
};

struct player_talent_notify	//PLAYER_TALENT_NOTIFY
{
	single_data_header header;
	int main_point;
	int sec_point;
};

struct skill_notify			//SKILL_NOTIFY
{
	single_data_header header;
	int param1;
	uint64_t param2;
};

struct player_change_spouse		//PLAYER_CHANGE_SPOUSE
{
	single_data_header header;
	ruid_t who;
	ruid_t id;
};

struct turret_leader_notify		//TURRET_LEADER_NOTIFY
{
	single_data_header header;
	ruid_t turret_id;
	ruid_t leader_id;
};


struct leave_convoy_warning_range	//LEAVE_CONVOY_WARNING_RANGE
{
	ruid_t group_id;			//组id
	int break_time;				//失败时间
};

struct enter_convoy_warning_range	//ENTER_CONVOY_WARNING_RANGE
{
	ruid_t group_id;			//组id
};

struct object_run_perform		//OBJECT_RUN_PERFORM
{
	single_data_header header;
	unsigned short tick;
	ruid_t object_id;
	unsigned short perform_id;		//执行的技能段id
	unsigned short ms_time;			//执行持续时间
};

struct other_pet_info			//OTHER_PET_INFO
{
	single_data_header header;
	ruid_t player_id;
	ruid_t pet_id;
	//数据解释,同宠物物品
	//tid_t tid;
	//int expire_date;
	//unsigned int proctype_state;
	//unsigned int count;
	//unsigned short buf_size;
	//char buf[buf_size];
};


struct bindthrow_start			// BINDTHROW_START
{
	single_data_header header;
	unsigned short tick;
	unsigned short skill_id;
	ruid_t id_thrower;
	ruid_t id_throwee;
};

struct bindthrow_stop			// BINDTHROW_STOP
{
	single_data_header header;
	unsigned short tick;
	unsigned short skill_id;
	ruid_t id_thrower;
	ruid_t id_throwee;
	A3DVECTOR3 pos;			//投技结束throwee将被扔到的位置
	unsigned short dir;
	unsigned char type;		//投技结束throwee将被扔开的姿态: 击飞/击溃/击倒
	unsigned short time;		//投技结束throwee将被扔开的持续时间: ms
};

struct self_mp				//SELF_MP
{
	single_data_header header;
	unsigned char mp_type;
	float cur_mp;
};

struct self_max_hp			//SELF_MAX_HP
{
	single_data_header header;
	float value;
};

struct self_max_mp			//SELF_MAX_MP
{
	single_data_header header;
	unsigned char mp_type;
	float value;
	float base2_value; //人物基础属性，只被人物本身加点修正(不含buff/addon加点)
};

struct self_hp_gen			//SELF_HP_GEN
{
	single_data_header header;
	unsigned char gen_type;
	float value;
};

struct self_mp_gen			//SELF_MP_GEN
{
	single_data_header header;
	unsigned char gen_type;
	unsigned char mp_type;
	float value;
};

struct self_ability_prop		//SELF_ABILITY_PROP
{
	single_data_header header;
	unsigned char ab_type;
	unsigned char prop_type;
	float value;
};

struct self_potential			//SELF_POTENTIAL
{
	single_data_header header;
	unsigned char pt_type;
	float value;
};

struct self_attr_damage			//SELF_ATTR_DAMAGE
{
	single_data_header header;
	unsigned char attr_type;
	float value;
};

struct self_attr_resist			//SELF_ATTR_RESIST
{
	single_data_header header;
	unsigned char attr_type;
	float value;
};

struct self_attr_damage_resist_ignore	//SELF_ATTR_DAMAGE_RESIST_IGNORE
{
	single_data_header header;
	unsigned char attr_type;
	float value;
};

struct self_attr_damage_pierce		//SELF_ATTR_DAMAGE_PIERCE
{
	single_data_header header;
	unsigned char attr_type;
	float value;
};

struct self_debuff_resist		//SELF_DEBUFF_RESIST
{
	single_data_header header;
	unsigned char debuff_type;
	float value;
};

struct self_judgement_evade		//SELF_JUDGEMENT_EVADE
{
	single_data_header header;
	unsigned char judgement_type;
	float value;
};

struct self_judgement_resist		//SELF_JUDGEMENT_RESIST
{
	single_data_header header;
	unsigned char judgement_type;
	float value;
};

struct self_walk_speed			//SELF_WALK_SPEED
{
	single_data_header header;
	float value;
};

struct self_run_speed			//SELF_RUN_SPEED
{
	single_data_header header;
	float value;
};

struct self_mount_speed			//SELF_MOUNT_SPEED
{
	single_data_header header;
	float value;
};

struct self_normal_attack_sight		//SELF_NORMAL_ATTACK_SIGHT
{
	single_data_header header;
	float value;
};

struct self_valid_attack_sight		//SELF_VALID_ATTACK_SIGHT
{
	single_data_header header;
	float value;
};

struct self_combo			//SELF_COMBO
{
	single_data_header header;
	unsigned char combo;
};

struct synthetize_item2_result		//SYNTHETIZE_ITEM2_RESULT
{
	single_data_header header;
	unsigned char retcode; //0: successful, >0: failed
	tid_t item_tid;
	unsigned short success_count;
	unsigned short failure_count;
};

struct bind_confirm_request		//BIND_CONFIRM_REQUEST
{
	single_data_header header;
	tid_t tid;
};

struct bind_confirm_start		//BIND_CONFIRM_START
{
	single_data_header header;
	tid_t tid;
	ruid_t roleid;
	unsigned char pos;

};

struct reinforce_equip_result 		//REINFORCE_EQUIP_RESULT
{
	single_data_header header;
	char retcode;     //0:success， >0:failure， <0:error   具体解释见 REINFORCE_RESULT
	char query_result; //查询结果
	int equip_tid;
	int next_level;
	int next_money;
	int next_cash;
	float next_prob;

};

struct produce_recipe_levelup  //PRODUCE_RECIPE_LEVELUP
{
	single_data_header header;
	unsigned char retcode;     //0 for success and >0 for failure
};

struct self_aptitude			//SELF_APTITUDE
{
	single_data_header header;
	unsigned char init;
	unsigned char active_count;
	struct aptitude_entry
	{
		unsigned int id;
		unsigned char level;
	};
	aptitude_entry data[0];
};

struct self_kill_monster		//SELF_KILL_MONSTER
{
	single_data_header header;
	int count;
};

struct player_tired			//PLAYER_TIRED
{
	single_data_header header;
	ruid_t roleid;
	unsigned char is_tired;		//0:正常 1:疲倦 2:疲劳
};

struct clear_buf_skill			//CLEAR_BUF_SKILL
{
	single_data_header header;
	int kungfu_id;
	unsigned char kungfu_index;
	unsigned short skill_id;
};

struct be_healed_mp			//BE_HEALED_MP
{
	single_data_header header;
	unsigned char mp_type;
	float mp;
};

/* 被SystemSpeak协议取代
struct speak_use_clientid		//SPEAK_USE_CLIENTID
{
	single_data_header header;
	ruid_t speaker_id;
	int id_speak;
};
*/

struct wedding_list			 //WEDDING_LIST
{
	single_data_header header;
	unsigned char count;
	int start_time[0];
};


struct support_spactate_instance_info	//SUPPORT_SPACTATE_INSTANCE_INFO
{
	struct instance_info
	{
		int id;					//副本id,唯一id,观战请求发这个
		unsigned short spactate_count;		//这个用short防止千人观战的大场面
		unsigned char paticipate_count;		//参与人的数量 <=6
		ruid_t player_list[0];			//玩家id列表
	};

	struct instance_info_group
	{
		tid_t tid;
		unsigned char count;
		instance_info list[0];
	};

	single_data_header header;
	ruid_t npc_id;
	unsigned char group_count;
	instance_info_group list[0];
};

struct self_compensate_exp		//SELF_COMPENSATE_EXP
{
	single_data_header header;
	uint64_t exp;
};

struct multi_exp_state			//MULTI_EXP_STATE
{
	single_data_header header;
	unsigned char is_active;		//双倍时间是否解冻
	unsigned char ungotten_system_hour;	//未领取系统双倍小时数
	int unused_system_sec;			//可用系统双倍秒数
	int unused_person_sec;			//可用个人多倍秒数
	unsigned char person_ratio;		//个人倍率
};

struct self_multi_exp			//SELF_MULTI_EXP
{
	single_data_header header;
	unsigned char is_personal;
	unsigned char ratio;
	int second;
};

struct safe_revive_guide	//SAFE_REVIVE_GUIDE
{
	single_data_header header;
	ruid_t player_id;
	ruid_t attacker_id;
	scene_tag_t scene_tag;
	A3DVECTOR3 pos;
};

struct transmit_flag  			//TRANSMIT_FLAG
{
	unsigned char index;		//特殊飞行棋使用的索引值 0 ~ 9
	unsigned short scene_tag;	// == 0 无效
	float x;
	float y;
	float z;
	unsigned char name_size;
	char name[0];
	unsigned char mirror_id;
};

struct transmit_death_pos  		//TRANSMIT_FLAG
{
	unsigned short scene_tag;	// == 0 无效
	unsigned char mirror_id;
	float x;
	float y;
	float z;
};

struct transmit_flag_table
{
	unsigned char tab_index;	//tab_index == 0xFF 使用default_flag ,否则使用normal_flag
	unsigned char max_count;
	unsigned char size;
	unsigned char name_size;
	char name[0];
	transmit_flag flags[0];
};

struct transmit_flag_changed		// TRANSMIT_FLAG_CHANGED
{
	single_data_header header;
	unsigned char type;		// 对应op操作
	unsigned char tab_index;	// tab index  tab == 0 为死亡坐标
	transmit_flag flag_pos;		// new flag pos
};

struct transmit_flag_info		//TRANSMIT_FLAG_INFO
{
	single_data_header header;
	transmit_death_pos death_pos;	// death pos
	unsigned char size;
	transmit_flag_table flag[0];
};

struct wedding_apply_re			//WEDDING_APPLY_RE
{
	single_data_header header;
	int start_time;
	unsigned char result;
};

struct self_max_vigor			//SELF_MAX_VIGOR
{
	single_data_header header;
	unsigned char index;
	float max_vigor;
};

struct social_msg			//SOCIAL_MSG
{
	single_data_header header;
	unsigned char msg_type;
	ruid_t src_id;
	unsigned char src_name_size;
	char src_name_buf[0];
	ruid_t dst_id;
	unsigned char dst_name_size;
	char dst_name_buf[0];
};

struct server_data_version_info		//SERVER_DATA_VERSION_INFO
{
	int lua_version;
	md5_t element_md5;
	md5_t skill_md5;
	md5_t task_md5;
	//md5_t gshop_md5;
};

struct battlefield_faction_info		//BATTLEFIELD_FACTION_INFO
{
	single_data_header header;
	tid_t inst_tid;
	int instid;
	char battle_state;
	ruid_t creator;
	char namesz;
	char name[0];
	int battleground_victory_count;	// < 0表示最后一局输掉了 绝对值表示连胜场数，只对天梯有效
	char victory_off_size;
	char victory_off[0];	// -1 平局  0 1 2 3 胜者阵营偏移
	char faction_num;
	struct faction
	{
		char num;
		struct
		{
			ruid_t id;
			char index;
			char gender;
			char prof;
			short level;
			int wulin;
			char state;
			//int transferom_id;
			unsigned char faceid;
			char namesz;
			char name[0];
		} memeber[0];
	} sides[0];
	short spectator_num;	//观看者数量
	struct
	{
		ruid_t id;
		char gender;
		char prof;
		short level;
		int wulin;
		unsigned char faceid;
		char namesz;
		char name[0];
	} spectators[0];
};

struct battlefield_faction_state		//BATTLEFIELD_FACTION_STATE
{
	single_data_header header;
	ruid_t id;
	char old_faction;
	char new_faction;	// 0 indicate to spectator, -1 indicate to leave world
	char state;		// 0 prepare, 1 ok
	char prof;
	char gender;
	short level;
	int wulin;
	char index;
	unsigned char faceid;
	int tid;		// id -> role tid -> transferom_id;
	char namesz;
	char name[0];
};

struct battlefield_count_down			//BATTLEFIELD_COUNT_DOWN
{
	single_data_header header;
	int inst_id;
	char type;		//0 游戏开始倒计时 1 单局倒计时 2 单局结束倒计时
	short time;		//count down seconds
	int cur_time_stamp;
};

struct battlefield_state_info			//BATTLEFIELD_STATE_INFO   战场状态信息
{
	single_data_header header;
	int inst_id;
	int state;
};

struct battlefield_faction_result		//BATTLEFIELD_FACTION_RESULT
{
	single_data_header header;
	int inst_id;
	char type;		//0 整场  1 单局
	char victory_off;	// -1 平局  0 1 2 3 胜者阵营偏移
};

struct depository_state				//DEPOSITORY_STATE
{
	single_data_header header;
	unsigned char location;
	unsigned char is_open;
};

struct instance_score				//INSTANCE_SCORE
{
	single_data_header header;
	int lottery_id;			//彩票ID
	char total_level;		//综合评价等级1-4
	int total_score;		//综合评价
	unsigned char score_level_count;//单个评价等级个数，最大值6
	char score_level[0];		//单个评价等级1-4
};

struct random_map				//RANDOM_MAP
{
	single_data_header header;
	unsigned char count;
	struct block_info
	{
		int board_id;
		int board_sid;
		char type_flag;
		int prop_val;
		unsigned char arrived;
	};
	block_info info[0];
};

struct self_family_call				//SELF_FAMILY_CALL
{
	single_data_header header;
	unsigned char count;
};

struct gift_notify		//GIFT_NOTIFY
{
	single_data_header header;
	int gift_id;		//礼物ID
	int time_left;		//领取礼物的剩余时间(秒)
};

struct player_change_object_state		//PLAYER_CHANGE_OBJECT_STATE
{
	single_data_header header;
	object_state_t object_state;
};

struct self_family_access			//SELF_FAMILY_ACCESS
{
	single_data_header header;
	int last_access_time;
};

struct luck_action_notify			//LUCK_ACTION_NOTIFY
{
	single_data_header header;
	int index;
	int begin_timestamp;
	int end_timestamp;
};

struct tpaward_notice				//TPAWARD_NOTICE
{
	single_data_header header;
	int awardnum;
	struct award_info
	{
		int tid;
		int rankid; //0表示第一名
		int taskid;
		int titleid;
		int retcode;
	};
	award_info info[0];
};

struct tpaward_result				//TPAWARD_RESULT
{
	single_data_header header;
	int resultnum;
	struct result_info
	{
		int tid;
		int taskid;
		int titleid;
		unsigned long retcode;
	};
	result_info info[0];
};

struct player_login_info			//PLAYER_LOGIN_INFO
{
	single_data_header header;
	int logout_time;
};

struct flourish_prize_notify			//FLOURISH_PRIZE_NOTIFY
{
	single_data_header header;
	unsigned char count;			//活跃度礼包个数
	int flourish_value[0];			//活跃度值
};

struct start_qinggong_re			//START_QINGGONG_RE
{
	single_data_header header;
	ruid_t object_id;
	unsigned char type;
	unsigned char success;
	//以下3个参数只有在type为 1-9 并且 sucess 为成功的时候带以下参数
	float vert_speed;
	float g1;
	float g2;
};

struct stop_qinggong				//STOP_QINGGONG
{
	single_data_header header;
	ruid_t object_id;
	unsigned char type;
};

struct job_info					//JOB_INFO
{
	single_data_header header;
	uint64_t job_exp;		//工作经验
	tid_t active_job;		//当前激活差事
	unsigned char job_count;
	struct job_t
	{
		tid_t tid;
		unsigned char base_level; //差事等级
		unsigned char extra_level;
	};
	job_t jobs[0];
};

struct cur_active_job_change			//CUR_ACTIVE_JOB_CHANGE
{
	single_data_header header;
	tid_t tid;
};

struct job_exp_change				//JOB_EXP_CHANGE
{
	single_data_header header;
	int offset;
	uint64_t cur_job_exp;
};

struct job_level_notify				//JOB_LEVEL_NOTIFY
{
	single_data_header header;
	tid_t tid;
	unsigned char base_level;
	unsigned char extra_level;
};

struct forget_job				//FORGET_JOB
{
	single_data_header header;
	tid_t tid;
};

struct self_hit					//SELF_HIT
{
	single_data_header header;
	int hit;				//当前连击数
	int time;				//连击有效时间(ms)
};

struct enter_weak				//ENTER_WEAK
{
	single_data_header header;
	ruid_t id;
	unsigned short time; //ms
};

struct practice_result				//PRACTICE_RESULT
{
	single_data_header header;
	int tid;
	char result; //0:success,非0失败
};

struct update_object_state			//UPDATE_OBJECT_STATE
{
	single_data_header header;
	ruid_t id;
	object_state_t object_state;
};

struct service_content				//SERVICE_CONTENT
{
	single_data_header header;
	ruid_t id;
	unsigned char service_id;
	char state;//0=自己打开，1=队长打开，2=队长关闭
	//以下为content内容,根据不同service_id解释不同,现在只有npc收购服务和求爱服务用
	char content[0];
};

struct player_active_job			//PLAYER_ACTIVE_JOB
{
	single_data_header header;
	ruid_t id;
	tid_t tid;
	unsigned char level;
};

struct mall_shopping_result     //MALL_SHOPPING_RESULT
{
	single_data_header header;
	unsigned char retcode;
	unsigned char count;
	struct result_t
	{
		unsigned char cart_index;
		unsigned char errcode;
	} results[0];
};

struct limit_purchase_end     //LIMIT_PURCHASE_END
{
	single_data_header header;
	ruid_t id;
};

struct transform_state		//TRANSFORM_STATE
{
	single_data_header header;
	ruid_t role_id;
	tid_t tid;
};

struct scene_params		//SCENE_PARAMS
{
	single_data_header header;
	unsigned char count;
	struct
	{
		int index;
		int value;
	} list[0];
};

struct scene_param_change	//SCENE_PARAM_CHANGE
{
	single_data_header header;
	int index;
	int value;
};

struct common_use_limit_data	//COMMON_USE_LIMIT_DATA
{
	single_data_header header;
	unsigned char count;
	struct node_t
	{
		tid_t tid;
		int refresh_timestamp;  //下次清空时间戳
		unsigned int count;	//当前次数
	};
	node_t node[0];
};

struct common_use_limit_change	//COMMON_USE_LIMIT_CHANGE
{
	single_data_header header;
	tid_t tid;
	int refresh_timestamp;  //下次清空时间戳
	unsigned int count;		//当前次数
};

struct alliance_war_result		//ALLIANCE_WAR_RESULT
{
	single_data_header header;
	short kill_num;		//杀人数
	short be_killed;	//被杀数

	struct player_info
	{
		ruid_t id;
		int family_id;
		short kill_num;
		unsigned char count;
		char name[0];
	};
	unsigned short player_count;
	struct player_info players[0];

	struct family_info
	{
		int family_id;
		unsigned char count;
		char name[0];
	};
	unsigned char families_count;
	struct family_info fmailies[0];
};

struct scene_special_object_info_1	//SCENE_SPECIAL_OBJECT_INFO_1
{
	enum
	{
		INFO_PLAYER = 1,
		INFO_NPC = 2,
		INFO_MATTER = 3
	};
	single_data_header header;
	unsigned char type;	//1，2，3  player/npc/matter
	//根据上面的类型按 player_definite_info/npc_definite_info/matter_info 解释
	//char buf[0];
};

struct scene_special_object_disappear	//SCENE_SPECIAL_OBJECT_DISAPPEAR
{
	single_data_header header;
	ruid_t object_id;
};

struct	alliance_war_apply_re		//ALLIANCE_WAR_APPLY_RE
{
	single_data_header header;
	int ret;
};

struct select_role_info			//SELECT_ROLE_INFO
{
	single_data_header header;
	struct data
	{
		ruid_t role;
		int trans_id;
		char select;		//选择 0放弃 1选择
	};
	unsigned char count;
	struct data info[0];
};

struct mafia_logistic_available		//MAFIA_LOGISTIC_AVAILABLE
{
	single_data_header header;
	struct item_info
	{
		int itemid;
		unsigned short count;
	};
	unsigned short count;
	struct item_info info[0];
};

struct mafia_logistic_allinfo		//MAFIA_LOGISTIC_INFO
{
	single_data_header header;
	struct logistic_info
	{
		int itemid;
		char type;
		char level;
		float ratio;
		unsigned short count;
	};
	unsigned short count;
	struct logistic_info info[0];
};

struct mafia_build_up_info		//MAFIA_BUILD_UP_INFO
{
	single_data_header header;
	int mid;
	int build_up_workinghours;
	struct item_str
	{
		int tid;
		unsigned short count;
	};
	unsigned char count;
	item_str items[0];
};

struct mafia_money		//MAFIA_MONEY
{
	single_data_header header;
	int retcode;
	int64_t mafia_money;	//retcode = 0时，为此时帮派资金值
};

struct inst_special_mode	//INST_SPECIAL_MODE
{
	single_data_header header;
	unsigned short mode;
};

struct sutra_data			//SUTRA_DATA
{
	single_data_header header;
	unsigned short kungfu_count;
	struct
	{
		int id;
		char pos;
	} kungfus[0/*kungfu_count*/];
	unsigned short skill_count;
	struct
	{
		int id;
		char pos;
	} skills[0/*skill_count*/];
};

struct sutra_edit_re			//SUTRA_EDIT_RE
{
	single_data_header header;
	unsigned char result;		//0:成功, !0: 失败
	char enable;			//0:disable, 1:enable
	char kungfu;			//0:skill, 1:kungfu
	char pos;
	int id;
};

struct player_pick_result		//PLAYER_PICK_RESULT
{
	single_data_header header;
	int player_id;
	int matter_id;
	unsigned char result;		//0:成功 其他:失败
};

struct del_diy_kungfu_re		//DEL_DIY_KUNGFU_RE
{
	single_data_header header;
	int kungfu_id;
	unsigned char result;		//0成功 1失败
};

struct active_equip_slot		//ACTIVE_EQUIP_SLOT
{
	single_data_header header;
	unsigned int mask;
};

struct equipment_exp			//EQUIPMENT_EXP
{
	single_data_header header;
	unsigned char location;
	unsigned short index;
	unsigned int exp;            //当前装备经验
	unsigned char upgrade_level; //当前装备成长等级
	unsigned char bind_state;    //当前装备绑定状态
};

struct soul_picked			//SOUL_PICKED
{
	single_data_header header;
	ruid_t matter_id;
	ruid_t player_id;
	int cost_time;			//毫秒
};

struct object_beattack_changed		//OBJECT_BEATTACK_CHANGED
{
	single_data_header header;
	ruid_t id;
	char can_not_be_attacked;
};

struct home_data			//HOME_DATA
{
	single_data_header header;
	ruid_t player_id;
	int last_rest_time; //上一次休息时间
};

struct home_attend_result //HOME_ATTEND_RESULT
{
	single_data_header header;
	ruid_t srcroleid;
	ruid_t dstroleid;
	unsigned int servant_tid;           //侍应模板ID
	unsigned int delta_vp_produce;      //生产点
	unsigned int delta_vp_social;       //社交点
	unsigned int delta_force;           //功力
	unsigned int delta_exp;             //经验
	unsigned int delta_compensate_exp;  //补偿经验
	unsigned int special_buff_skill_id; //特殊buff技能ID
};

struct battle_option_info		//BATTLE_OPTION_INFO
{
	single_data_header header;
	unsigned int option_mask;
	unsigned int round_num;
	unsigned int round_time;
	unsigned int scene_tid;
	unsigned char send_mask;	// 0x01  SYNC_INFO_ONLY
	unsigned char name_size;
	char name[0];
};

struct battlefield_faction_record		//BATTLEFIELD_FACTION_RECORD
{
	single_data_header header;
	char victory_off_size;
	char victory_off[0];	// -1 平局  0 1 2 3 胜者阵营偏移
};

struct instance_npc_score_ratio		//INSTANCE_NPC_SCORE_RATIO
{
	single_data_header header;
	int score_ratio;	// 积分倍率，默认为1
	int duration;		// 到期的时间戳，0代表无限
};

struct instance_award_exp	//INSTANCE_AWARD_EXP
{
	single_data_header header;
	int npc_exp;		// 杀怪总经验
	int team_exp;		// 组队奖励
	int score_exp;		// 评价奖励
};

struct scene_load_process	// SCENE_LOAD_PROCESS
{
	single_data_header header;
	ruid_t roleid;
	char load_percent;	// 0 - 100
};

struct player_set_signature	//PLAYER_SET_SIGNATURE
{
	single_data_header header;
	ruid_t player_id;
	unsigned char size;
	char signature[0];
};

struct record_struct				//玩家战绩类型
{
	unsigned int total_count;
	unsigned int victory_count;
	unsigned short winning_steak;
	unsigned char last_battle_sz;		//动态
	struct
	{
		int transfer_id;
		unsigned int mode;
		int time;
		char result;
	} last_battle_list[0];
};

struct player_record_info			//PLAYER_RECORD_INFO 玩家战绩信息
{
	single_data_header header;
	ruid_t player_id;
	unsigned char record_array_sz;
	record_struct recrod_array[0];
};

struct player_record_info_inc			//PLAYER_RECORD_INFO_INC 玩家战绩信息
{
	single_data_header header;
	ruid_t player_id;
	unsigned char record_type;
	record_struct recrod_array;
};

struct change_board_wait_time	//CHANGE_BOARD_WAIT_TIME
{
	single_data_header header;
	unsigned char joint_index;		//对应的节点索引
	char time_second;			//到计时秒数,为0表示取消
};

struct debug_hot_update_re	//DEBUG_HOT_UPDATA_RE
{
	single_data_header header;
	unsigned char type;
	int id;
	int param;
	int ret;				//返回值 0正确
};

struct player_appearance_info		//PLAYER_APPEARANCE_INFO
{
	single_data_header header;
	ruid_t id;
	unsigned short crc;
	unsigned char size;
	char content[0];
};

struct player_extend_data			//PLAYER_EXTEND_DATA 玩家的装备、角色自定义数据
{
	single_data_header header;
	ruid_t player_id;
	unsigned short flag;
	unsigned char mask; //refer to C2S::CMD::get_others_extend_data
	//equipment info if any
	unsigned short equip_crc;
	unsigned int equip_mask_add;
	unsigned int equip_mask_del;
	struct
	{
		int item_tid;
		unsigned char reinforce_level;
	} equip_data_add[0];//0 ~ 16
	//appearance info if any
	unsigned short appearance_crc;
	unsigned char appearance_size;
	char appearance_content[0];
};

struct reward_t
{
	unsigned short size;
	char data[0];
};

struct reward_info			//REWARD_INFO
{
	unsigned char count;
	reward_t rewards[0];
};

struct add_reward			//ADD_REWARD
{
	reward_t reward;
};

struct del_reward			//DEL_REWARD
{
	unsigned char index;
	unsigned char reason;	//0:领取 1:到期
};

struct gs_debug_query_ctrl_status_re	//GS_DEBUG_QUERY_CTRL_STATUS_RE
{
	single_data_header header;
	int ctrl_id;
	unsigned char is_active;
	unsigned char detach_after_spawn;
};

struct gs_debug_query_scene_param_re	//GS_DEBUG_QUERY_SCENE_PARAM_RE
{
	single_data_header header;
	int key;
	unsigned char in_use;
	int value;
};

struct gs_debug_query_npc_param_re	//GS_DEBUG_QUERY_NPC_PARAM_RE
{
	single_data_header header;
	ruid_t npc_id;
	int key;
	unsigned char in_use;
	int value;
};

struct self_crit_resist			//SELF_CRIT_RESIST
{
	single_data_header header;
	enum
	{
		CRIT = 0,
		MASTERY,
		RESIST,
		CRIT_DAMAGE,
		RESIST_DAMAGE,
	};
	unsigned char index;
	float value;
};

struct bind_hijack_npc_info	//BIND_HIJACK_NPC_INFO
{
	single_data_header header;
	ruid_t hijacker;
	int npc_tid;
};

struct be_hijacked_transinfo	//BE_HIJACKED_TRANSINFO
{
	single_data_header header;
	unsigned short scence;
	A3DVECTOR3 pos;
	unsigned char last_time;
};

struct escort_trap_info		//ESCORT_TRAP_INFO
{
	single_data_header header;
	ruid_t npc_id;
	unsigned char is_active;
	tid_t trap_tid;
};

struct gather_info_req_result	//GATHER_INFO_REQ_RESULT
{
	single_data_header header;
	char oper_type;
	char gather_type;
	int retcode;
};

struct guessgame_needmsg	//GUESSGAME_NEEDMSG
{
	single_data_header header;
	int gameid;
	int activityid;
	struct stage_t
	{
		int conclusion;
		int msgs[3];
	};
	unsigned char count;
	struct stage_t stages[0];
};

struct guessgame_result		//GUESSGAME_RESULT
{
	single_data_header header;
	int gameid;
	int retcode;
};

struct juewei_task_publish_re	//JUEWEI_TASK_PUBLISH_RE
{
	single_data_header header;
	int ret_code;			//0:成功
	int price;
	int count;
};

struct juewei_task_settle_re	//JUEWEI_TASK_SETTLE_RE
{
	single_data_header header;
	int ret_code;			//0:成功
	int price;
	int total_count;
	int left_count;
};

struct juewei_task_deliver_re	//JUEWEI_TASK_DELIVER_RE
{
	single_data_header header;
	int ret_code;			//0:成功
	ruid_t from;
	int price;
};

struct temp_enemy_relation	//TEMP_ENEMY_RELATION
{
	single_data_header header;
	int relation_id;
};

struct mainfaction_oper_result //MAINFACTION_OPER_RESULT
{
	single_data_header header;
	int retcode;
};

struct mafia_treasure_trap_info //MAFIA_TREASURE_TRAP_INFO
{
	single_data_header header;
	unsigned short trap_count;
	unsigned short size;
	char trap_info[0];
	unsigned char log_size;
	struct log_t
	{
		int steal_time;
		int item_id;
		unsigned char item_level;
		int64_t thief_id;
		unsigned char name_size;
		char name_buf[0];
	};
	log_t logs[0];
};

struct mafia_treasure_buff_info //MAFIA_TREASURE_BUFF_INFO
{
	single_data_header header;
	unsigned char treasure_buff_count;
	unsigned char treasure_size;
	struct treasure_info_t
	{
		int tid;
		unsigned char level;
	};
	struct treasure_info_t treasure_info[0];
};

struct mafia_activity_info //MAFIA_ACTIVITY_INFO
{
	single_data_header header;
	int cur_tid;
	int lock_tid;
	unsigned char left_time;
	unsigned char player_count;
	unsigned char system_count;
	struct info_t
	{
		int tid;
		unsigned char count;
	};
	struct info_t infos[0];
};

struct player_alias		//PLAYER_ALIAS
{
	single_data_header header;
	ruid_t roleid;
	unsigned char is_alias;
	unsigned char name_size;
	char name_buf[0];
};

struct mafia_activity_openresult //MAFIA_ACTIVITY_OPENRESULT
{
	single_data_header header;
	int tid;
	char type;
	int retcode;
};

struct mafia_cur_activity //MAFIA_CUR_ACTIVITY
{
	single_data_header header;
	int cur_actid;
};

struct mafia_trapoper_result
{
	single_data_header header;
	unsigned char page;
	unsigned char h;
	unsigned char v;
	unsigned char eid;
	int retcode;
};

struct mingxing_list			//MINGXING_LIST
{
	single_data_header header;
	unsigned char count;
	int mingxing_id_list[0];
};

struct add_mingxing			//ADD_MINGXING
{
	single_data_header header;
	int mingxing_id;
};

struct del_mingxing			//DEL_MINGXING
{
	single_data_header header;
	int mingxing_id;
};

struct npc_woo_owner_change		//NPC_WOO_OWNER_CHANGE
{
	single_data_header header;
	tid_t npc_tid;
	ruid_t npc_id;
	ruid_t owner_id;
	unsigned char owner_name_size;
	char owner_name[0];
};

struct mafia_submitmsg_result		//MAFIA_SUBMITMSG_RESULT
{
	single_data_header header;
	int retcode;
};

struct npc_response_skill		//NPC_RESPONSE_SKILL
{
	single_data_header header;
	ruid_t player_id;
	ruid_t npc_id;
	int skill_id;
	unsigned char result; //0:成功 非0:失败
};

struct badge_list			//BADGE_LIST
{
	struct badge_info_t
	{
		int badge_id;
		unsigned char badge_level;
		int achieve_time;
	};
	single_data_header header;
	unsigned short equip_count;
	badge_info_t equip_list[0];
	unsigned short store_count;
	badge_info_t store_list[0];
};

struct mafia_welfare_get //MAFIA_WELFARE_GET
{
	single_data_header header;
	unsigned char type;	// 1:周薪 2:供奉 3:福利
};

struct mafia_activity_end_info	//MAFIA_ACTIVITY_END_INFO
{
	single_data_header header;
	int tid;
	int value;
};

struct mafia_player_changedata //MAFIA_PLAYER_CHANGEDATA
{
	single_data_header header;
	char type;
	int value;
};

struct self_escort_count //SELF_ESCORT_COUNT
{
	single_data_header header;
	unsigned char left_count;
};

struct escort_state //ESCORT_STATE
{
	single_data_header header;
	enum
	{
		STATE_REST = 0,
		STATE_TRAP,
		STATE_ADVENTURE,
		STATE_POISON,
		STATE_DETECT,
	};
	unsigned char state_type;
	unsigned short left_time;
	int param;
};

struct gather_info_list	//GATHER_INFO_LIST
{
	single_data_header header;
	struct info_t
	{
		unsigned short scene_id;
		unsigned char count;
	};
	unsigned short gather_count;
	info_t infos[0];
};

struct pet_trans_force_result //PET_TRANS_FORCE_RESULT
{
	single_data_header header;
	enum
	{
		PLUS_1 = 0,	//升一级
		MINUS_1,	//降一级
		NO_CHANGE,	//无变化
		INSANE,		//走火入魔
	};
	unsigned char pet_index;
	unsigned char result;
	unsigned char new_level;
	unsigned short new_factor;
};

struct pet_learn_skill_result //PET_LEARN_SKILL_RESULT
{
	single_data_header header;
	enum
	{
		OPEN_NEW = 0,	//开新技能格
		LEVEL_UP,	//已有技能升级
		COVER_OLD,	//覆盖已有技能
		FAIL,		//失败
	};
	unsigned char pet_index;
	unsigned char result;
	unsigned char skill_index;
	unsigned short skill_id;
	unsigned char skill_level;
};

struct pet_reborn_result //PET_REBORN_RESULT
{
	single_data_header header;
	unsigned char pet_index;
	unsigned char new_quality;
};
struct produce_skill_upgrade_result //PRODUCE_SKILL_UPGRADE_RESULT
{
	single_data_header header;
	int produce_skill_id;
	unsigned char new_level;
};

struct badge_swap	//BADGE_SWAP
{
	single_data_header header;
	unsigned short index1;
	unsigned short index2;
	unsigned char type;
	unsigned char retcode;
};

struct battle_stat_panel			//BATTLE_STAT_PANEL
{
	single_data_header header;
	struct body_t
	{
		ruid_t id;			//被杀者ID 0:普通NPC;1:塔;2:小兵
		unsigned short death_count;	//被杀次数
	};
	struct player_stat
	{
		ruid_t roleid;			//当前玩家roleid
		unsigned short death_count;	//被杀次数
		unsigned short secondary_attack;//助攻数
		unsigned char size;
		body_t loser[0];		//被当前玩家击杀的人
	};
	unsigned char size;
	player_stat players[0];
};

struct battle_stat_panel_inc			//BATTLE_STAT_PANEL_INC
{
	single_data_header header;
	ruid_t killer;
	ruid_t loser;				//ID 0:普通NPC;1:塔;2:小兵
	int time;
	unsigned char size;
	ruid_t secondary_attack[0];		//助攻者
};

struct other_badge_detail //OTHER_BADGE_DETAIL
{
	struct badge_info_t
	{
		int badge_id;
		unsigned char badge_level;
		int achieve_time;
	};
	single_data_header header;
	ruid_t roleid;
	unsigned short equip_count;
	badge_info_t equip_list[0];
	unsigned short store_count;
	badge_info_t store_list[0];
};

struct fire_in_the_hole		// FIRE_IN_THE_HOLE
{
	single_data_header header;
	char index;		// 1~n
	char type;
	float x;
	float z;
};

struct player_aptitude			//PLAYER_APTITUDE
{
	single_data_header header;
	enum
	{
		LOGIN			= 0,	//上线初始化
		SOUL_UPGRADE		= 1,	//心法升级
		SOUL_RESET		= 2,	//心法重置
		BASIC_UPGRADE		= 3,	//基础资质升级
		ADVANCED_UPGRADE	= 4,	//进阶资质升级
	};
	unsigned char type;
	unsigned short soul_level;	//心法等级
	unsigned int left_point;	//剩余资质点
	unsigned int used_point;	//已用资质点，包括基础资质和进阶资质的
	struct aptitude_entry
	{
		unsigned int id;
		unsigned short level;
	};
	unsigned short basic_count;	//基础资质个数
	aptitude_entry basic_data[0];
	unsigned short advanced_count;	//进阶资质个数
	aptitude_entry advanced_data[0];
};

struct badge_add	//BADGE_ADD
{
	single_data_header header;
	int badge_id;
	unsigned char badge_level;
	int achieve_time;
};

struct badge_del	//BADGE_DEL
{
	single_data_header header;
	int badge_id;
};

struct badge_update	//BADGE_UPDATE
{
	single_data_header header;
	int badge_id;
	unsigned char badge_level;
	int achieve_time;
};

struct npc_tizi_info_change	//NPC_TIZI_INFO_CHANGE
{
	single_data_header header;
	ruid_t npc_id;
	unsigned char tizi_size;
	char tizi_data[0];
};

struct player_tizi_result	//PLAYER_TIZI_RESULT
{
	single_data_header header;
	int board_id;
	int retcode;
};

struct clue_to_clue_result	//CLUE_TO_CLUE_RESULT
{
	single_data_header header;
	int tid;
	int retcode;
};

struct clue_to_final_result	//CLUE_TO_FINAL_RESULT
{
	single_data_header header;
	int tid;
	int retcode;
};

struct battlefield_player_buy_re //BATTLEFIELD_PLAYER_BUY_RE
{
	single_data_header header;
	int errcode;
	int data;
};

struct battlefield_player_sell_re //BATTLEFIELD_PLAYER_SELL_RE
{
	single_data_header header;
	int errcode;
	int data;
};

struct battlefield_shop_sync //BATTLEFIELD_SHOP_SYNC
{
	single_data_header header;
	int world_id;
	unsigned short count;
	struct item_t
	{
		unsigned char region_id;
		int item_tid;
		unsigned char open_for_sale; // 0/1
		short num_for_sale;  // -1 for no limit
	} items[0];
};

struct wuxue_data		//WUXUE_DATA
{
	single_data_header header;
	unsigned char user_wuxue_max;
	unsigned char jueji_max;
	unsigned int cur_wuxue;
	unsigned char count;
	struct wuxue_t
	{
		unsigned int index;
		unsigned char name_len;
		char name[0];
		unsigned int icon;
		int zhaoshi;
		unsigned char jueji_count;
		int jueji[0];
	};
	wuxue_t wuxue_list[0];
};

struct wuxue_update		//WUXUE_UPDATE
{
	single_data_header header;
	unsigned int index;
	unsigned char name_len;
	char name[0];
	unsigned int icon;
	int zhaoshi;
	unsigned char jueji_count;
	int jueji[0];
};

struct wuxue_select		//WUXUE_SELECT
{
	single_data_header header;
	unsigned int cur_wuxue;
};

struct wuxue_user_wuxue_max	//WUXUE_USER_WUXUE_MAX
{
	single_data_header header;
	unsigned char max;
};

struct wuxue_jueji_max		//WUXUE_JUEJI_MAX
{
	single_data_header header;
	unsigned char max;
};

struct battlefield_skill_level	//BATTLEFIELD_SKILL_LEVEL
{
	single_data_header header;
	unsigned char can_use_point;
	unsigned char skill_count;
	struct skill_t
	{
		int id;
		unsigned char level;
	};
	skill_t skill_list[0];
};

struct battlefield_player_level	//BATTLEFIELD_PLAYER_LEVEL
{
	single_data_header header;
	ruid_t player_id;
	unsigned char player_level;
	uint64_t exp;
};

struct security_password_control_state	//SECURITY_PASSWORD_CONTROL_STATE
{
	single_data_header header;
	unsigned char locked;
	unsigned char location;
	unsigned short index;
};

struct spaw_controller_state		//SPAW_CONTROLLER_STATE
{
	single_data_header header;
	int ctrl_id;
	char state;		// 0 unactive 1 active
	//float x, y, z;
};

struct player_extend_property		//PLAYER_EXTEND_PROPERTY
{
	single_data_header header;
	ruid_t roleid;
	//extend_prop	prop;			//不想引用头文件,所以注释掉了
};

struct jiachi_info_t
{
	unsigned char index;
	tid_t addon_tid;
	unsigned char rand;
};

struct jiachi_info		//JIACHI_INFO
{
	single_data_header header;
	jiachi_info_t info;
};

struct jiachi_data		//JIACHI_DATA
{
	single_data_header header;
	level_t level;
	unsigned char count;		//0表示没有或者没开启功能,是否开启显示按钮或面板,需要客户端也进行下逻辑判断
	//jiachi_info_t info[0];
};

struct jiachi_level_up		//JIACHI_LEVEL_UP
{
	single_data_header header;
	level_t level;
};

struct mafia_party_count	//MAFIA_PARTY_COUNT
{
	single_data_header header;
	int count_by_money;
	int count_by_item;
};

struct mafia_gft_info		//MAFIA_GFT_INFO
{
	single_data_header header;
	int mafia_total;
	int mafia_count;
	int self_count;
};

struct self_trans_force_count	//SELF_TRANS_FORCE_COUNT
{
	single_data_header header;
	unsigned char left_count;
};

struct notify_object_pos	//NOTIFY_OBJECT_POS
{
	single_data_header header;
	ruid_t object_id;
	unsigned short scene_tag;
	A3DVECTOR3 pos;
	char reason;//0=未定义，1=吃鸡脱离卡死
};

struct player_property_next	//PLAYER_PROPERTY_NEXT
{
	single_data_header header;
	short hero_location;		// <= 0 代表玩家自身，否则代表名人的location
	ruid_t object_id;	//本结构就可以用来发送自己的数据，也可以发送别人的
	//data_ClientData       结构，PB::data_ClientData
};

struct other_property_next	//OTHER_PROPERTY_NEXT
{
	single_data_header header;
	char prof;
	char faceid;
	unsigned short hairid;
	unsigned short clothesid;
	unsigned char body_size;
	unsigned char level;
	unsigned char prof_level;
	short hero_location;		// <= 0 代表玩家自身，否则代表名人的location
	ruid_t object_id;	//本结构就可以用来发送自己的数据，也可以发送别人的
	//data_ClientData       结构，PB::data_ClientData
};

/*(已废弃，通过pb同步，用PLAYER_PROPERTY_NEXT)
struct self_property_delta                      //SELF_PROPERTY_DELTA
{
	single_data_header header;
	short hero_location;		// <= 0 代表玩家自身，否则代表名人的location
	unsigned short count;   //有多少个条目
	struct
	{
		unsigned short index;           // 对应data_ClientData里的索引值，地址偏移为 index * sizeof(int)
		int value;                      // index里对应的值， 这个value只是用于标记内存，实际上可以保存的是浮点
	}data[0];       //count 个条目
	//data_ClientData       结构，这里在gproperty.h里有，需要拷贝给客户端完成，由于是生成的代码，不直接在这里放结构了
};
*/

struct server_pong				//SERVER_PONG
{
	single_data_header header;
	unsigned int cli_seq;
	int timestamp;
};

struct server_ping				//SERVER_PING
{
	single_data_header header;
	unsigned int seq;
	int svr_lantency;			//ms
};

struct stage_start				//STAGE_START
{
	single_data_header header;
	int world_tag;
	int stage_id;
	char first_stage;
};

struct stage_close				//STAGE_CLOSE
{
	single_data_header header;
	int world_tag;
	int stage_id;
	char is_clear;		//是否清空， 布尔值，如果不是清空，则代表版面失败
	char level_clear;	//如果如果is_clear 为true,且level_clear为true，则代表通关 。 is_clear为false时，此值无意义
};

struct enter_dying			//ENTER_DYING
{
	single_data_header header;
	int new_id;
};

struct start_cg				//START_CG
{
	single_data_header header;
	int dialog_id;
};

struct stop_cg				//STOP_CG
{
	single_data_header header;
	char is_complete;
};

struct pickup_drop_matter		//PICKUP_DROP_MATTER
{
	single_data_header header;
	int drop_matter_tid;
	int drop_matter_type;	//掉落物类型:
	int money;		//钱
	int score;		//分数
	int skill_id;		//技能id
	int chest_lvl;	//箱子等级
};

struct scene_stage_reward		//SCENE_STAGE_REWARD
{
	single_data_header header;
	int rank;			//评价（C:0, B:1,A:2, S:3）
	int score;			//这个版面的战斗评分
	int money;			//金钱
	int exp;			//经验
	int prof_exp;			//功力
	int chest_lvl;			//宝箱等级(0~5)
	int use_vp;			//是否消耗精力
};

struct scene_level_reward		//SCENE_LEVEL_REWARD
{
	single_data_header header;
	int rank;			//评价（C:0, B:1,A:2, S:3）
	int money;			//金钱
	int exp;			//经验
	int prof_exp;			//功力
	int chest_lvl;			//宝箱等级(0~5)
	int first_money;		//首通金钱
	int first_exp;			//首通经验
};

struct scene_level_score		//SCENE_LEVEL_SCORE
{
	single_data_header header;
	int score_add;			//增加了多少分数
	int cur_score;			//现在有多少关卡分数
};

struct scene_level_countdown	// SCENE_LEVEL_COUNTDOWN,
{
	single_data_header header;
	int op_type;			//0 设置倒计时 1 取消倒计时 2 暂停倒计时 3 恢复倒计时 4 修改倒计时
	int countdown_type;		//倒计时类型 1、下一波；2、空；3、副本结束；4、活动结束
	int countdown_timeout;		//倒计时目标的时间戳 aop_type为0 3时这个值才有效
	short modify_delta1;		//修改时修改的数值 只有修改倒计时时此值才非0
	short modify_delta2;		//修改时修改的数值 只有修改倒计时时此值才非0

};

struct npc_animation
{
	single_data_header header;
	int npcid;
	int animation_id;		//0 挑衅
};

struct npc_animation_move		// NPC_ANIMATION_MOVE
{
	single_data_header header;
	int npcid;
	int animation_id;
	int use_time;			// ms
	float ct1;
	float ct2;
	A3DVECTOR3 target_pos;
};

struct use_elixir_result 		//USE_ELIXIR_RESULT
{
	single_data_header header;
	char rst;			//-1 表示不能使用， 0 表示失败 	1表示成功
	char final;			//1表示是多次使用丹药中的最后一次使用，用于控制界面变化
	int tid;			//所吃丹药模板id
	int prof_exp;			//失败情况下，有多少经验获得
	int value;			//成功情况下，给了多少属性
	int prop_index;			//属性的索引值，和属性更新的方式一致
	char hero_index;		//名人的索引，如果是-1则为玩家，否则为名人，0~7分别对应8个激活的名人
};

struct stage_hidden_treasure_list		//HIDDEN_TREASURE_LIST
{
	single_data_header header;
	unsigned short count;
	struct
	{
		A3DVECTOR3 pos;
		char enable;
		char rare;
	} list[0];
};

struct discover_hidden_treasure			//DISCOVER_HIDDEN_TREASURE
{
	single_data_header header;
	short rst;	//0 成功 ， 1 成功，但这个隐藏物品应该随之消失  -1 失败，这个隐藏物品不存在（应该直接让它消失）
	unsigned short index;
	int digger_newid;
	int tid;		//产出的掉落物ID
};

struct combine_elixir_result			//COMBINE_ELIXIR_RESULT
{
	single_data_header header;
	short rst;	//0成功  <0  失败，不能合成
	int result_tid;	//合成出来的丹药模板id
};

struct stage_block_list				//STAGE_BLOCK_LIST
{
	single_data_header header;
	short count;
	A3DVECTOR3 list[0];
};

struct instance_ac_list				//INSTANCE_AC_LIST
{
	single_data_header header;
	short count;				//后面有多少个条目
	struct
	{
		int ins_id;			//副本模板id
		char difficulty_mask;		//难度的mask组合， 难度n对应的位置1代表该难度完成
	} list[0];
};

struct instance_accomplished			//INSTANCE_ACCOMPLISHED
{
	single_data_header header;
	int ins_id;
	char difficulty;			//难度值，取值0，1，2等
};

struct level_attribute				//LEVEL_ATTRIBUTE  481号
{
	single_data_header header;
	char partial;				//是否部分数据，如果是部分，表示后面的数据需要覆盖到当前属性中，否则这些就是所有属性数据
	unsigned char count;			//char代表最多只有255个属性（如果超过，以后再修改）
	struct
	{
		int key;			//属性的key
		int value;			//这个key对应的值
	} data[0];
};

struct refine_equip_result 			//REFINE_EQUIP_RESULT
{
	single_data_header header;
	tid_t old_equip; 		//升阶前的装备 tid
	tid_t new_equip;		//升阶后的装备tid
};

struct loading_progress				//LOADING_PROGRESS
{
	single_data_header header;
	int status;			// 0 等待进入游戏， 1： 版面开始，可以开版了，目前每次版面开始都会发送
};

struct super_hero_list			//SUPER_HERO_LIST
{
	enum
	{
		TYPE_LIST = 0,
		TYPE_NEW = 1,
	};

	single_data_header header;
	unsigned short cmd_type;	//这个协议标记的类型：0：列表，1：加入新名人
	unsigned short hero_total_count;//名人总数量，不是后面的数量
	unsigned short hero_count;	//后面名人数量 （部分更新名人的协议也是这个协议）
	struct
	{
		unsigned short index;		//名人的位置索引
		int tid;			//名人的模板ID
		int prof_exp;			//名人的职业经验
		unsigned short prof_level;	//名人的职业等级

		int goodwill;			//好感度
		short goodwill_level;		//好感度等级
		short summon_count;		//当前召唤次数

		unsigned short skill_count;	//所会技能数量
		struct
		{
			short skill;
			short level;
		} skills[0];

		//property_template::data_SHeroCliProp prop_data;		//名人属性摘要 为了方便编译，这里注释掉而已

	} list[0];
};

struct active_hero_list			//ACTIVE_HERO_LIST
{
	enum
	{
		TYPE_LIST = 0,
		TYPE_ACTIVATE = 1,
		TYPE_DEACTIVATE = 2,
	};
	single_data_header header;
	short cmd_type;			//引起协议的类型，0：激活名人的所有列表  1：激活某个名人产生的数据变化  2：取消激活某个名人产生的数据变化
	short count;			//后面有多少项数据
	struct
	{
		short slot_index;	//第几个激活槽位
		short active_index;	//小于0代表这个槽位为空
		//property_template::data_ClientData data;	//激活属性，active_index大于等于0时才有这个属性  这里注释掉方便编译
	} list[0];	//这是变长结构，可以一直读取到没有数据为止

};

struct activate_hero
{
	single_data_header header;
};

struct add_new_super_hero
{
	single_data_header header;
};

struct level_chest_op_result		//LEVEL_CHEST_OP_RESULT
{
	single_data_header header;
	int op;                 // 0 升阶  1 打开包裹
	unsigned int index;     //包裹索引
	int result;		//0 成功 非0 失败
};

struct level_arena_result		//LEVEL_ARENA_RESULT
{
	single_data_header header;
	short result;		// 1 战斗胜利，0：失败
	int reward_exp;
	int reward_prof_exp;
	int reward_money;
};

struct level_squad_list			//LEVEL_SQUAD_LIST
{
	single_data_header header;
	unsigned short squad_count;
	struct
	{
		char state;		// 0: ready 1:等待召唤中或其它不可用状态 ，此时newid无效，只是占位而已 fighting_capacity也一样
		int  newid;		// 如果 state 为0 (ready)， 则是这个小队成员的短ID
		int  fighting_capacity;	// 如果 state 为0 (ready)， 则是这个小队成员的战力
	} squad_list[0];
};


struct squad_open_chest			//SQUAD_OPEN_CHEST
{
	single_data_header header;
	int who;
	int item_id;		//-1代表打开失败
	int item_count;		//物品数量
	char is_levelup;	//箱子有无升阶
};

struct script_common_data 	//SCRIPT_COMMON_DATA 脚本发给客户端的通用数据
{
	single_data_header header;
	unsigned short count;	//后面有多少个参数
	int data[/*count*/];
};

struct friend_summon_info	//FRIEND_SUMMON_INFO
{
	single_data_header header;
	unsigned short total_count;	//本日召唤的总次数
	short entry_count;		//若此数值小于0，代表本操作需清空所有的朋友召唤记录
	struct
	{
		ruid_t friend_id;
		unsigned short summon_count;	//这个好友的本日已经召唤的次数，不在这个列表的，表示本日没有召唤过
	} list[/*entry_count*/];
};

struct hero_prof_exp_modify	//HERO_PROF_EXP_MODIFY
{
	single_data_header header;
	unsigned short type;		//0 增加职业经验， 1 减少职业经验
	int hero_index;			//名人索引（不是激活名人槽位）
	int prof_exp_modify;		//职业经验变化值，均为正数，增加时代表加值，减少时代表减值
	int cur_prof_exp;			//当前剩余职业经验
};

struct hero_prof_level_up	//HERO_PROF_LEVEL_UP
{
	single_data_header header;
	short new_prof_level;
	int hero_index;			//名人索引（不是激活名人槽位）
};

struct training_success		//TRAINIG_SUCCESS
{
	single_data_header header;
	int type;
	int active_slot;
};

struct find_way_re		//FIND_WAY_RE
{
	single_data_header header;
	unsigned char reason;
	unsigned char return_code;
	unsigned char count;		//path中路点个数
	A3DVECTOR3 path[0];	//路点序列 最后一个是目标点，不是路点
};

struct skill_bytes_mode		//SKILL_BYTES_MODE
{
	single_data_header header;
};

struct cast_skill_re		//CAST_SKILL_RE
{
	single_data_header header;
	unsigned char skill_sn;	//客户端传递过来的序号
};

struct attack_loop_start	//ATTACK_LOOP_START
{
	single_data_header header;
	int src_new_id;
	short skill_id;
	int target_newid;
};

struct attack_loop_stop		//ATTACK_LOOP_STOP
{
	single_data_header header;
	int src_new_id;
};

struct  other_player_reputation 	//OTHER_PLAYER_REPUTATION
{
	single_data_header header;
	short repu_count;
	struct
	{
		int index;
		int value;
	} rep_entry;
};

struct  auto_reward_list 	//AUTO_REWARD_LIST
{
	single_data_header header;
	short count;
	struct
	{
		char type; 	//1:签到礼包 2：等级礼包 3：在线时长礼包 4：月卡礼包
		int reward; 	//奖励内容, 通用发奖模板的 tid
		//变长
		int data;
		//type 1:  int 代表已经签到次数
		//type 2:  int 代表已经领奖的等级
		//type 3:  int 代表还有多少时间可以领下一个奖(reward < 0有效，否则无效)
		//type 4:  int 代表还有多少秒数月卡到期

	} entry[0];
};

struct  direct_lottery_seek_result 		//DIRECT_LOTTERY_SEEK_RESULT
{
	single_data_header header;
	short type;
	short repeat_count;
	int npc_dialog;
	short item_count;
	int items[0];
};

struct pata_data				//PATA_DATA
{
	single_data_header header;
	unsigned char count;
	struct
	{
		int instance_tid;
		unsigned char win_count;
		int cur_score;
	} list[0];
};


struct hero_recv_exp	//HERO_RECV_EXP
{
	single_data_header header;
	short hero_index;			//名人索引（不是激活名人槽位）
	int exp;
	int64_t cur_exp;		//升级的话，这个值可能会降低
	unsigned char levelup;		//非零代表升级了
	unsigned char cur_level;	//获得经验后的等级（可能升级）
};

struct car_race_inner	//GP_CAR_RACE_INNER
{
	single_data_header header;
	struct car_race_inner_header
	{
		unsigned short cmd;
		char content[];
	} inner_header;
};

struct PBS2C
{
	single_data_header header;
	unsigned char pbdata[0];
};


//add protocol structure here ----------------
}

enum
{
//0
	ERR_SUCCESS,
	ERR_INVALID_TARGET,
	ERR_OBJECT_IS_COOLING,
	ERR_ITEM_NOT_IN_INVENTORY,
	ERR_OUT_OF_RANGE,
	ERR_INVENTORY_IS_FULL,
	ERR_SERVICE_UNAVILABLE,
	ERR_SERVICE_ERR_REQUEST,
	ERR_OUT_OF_FUND,
	ERR_OUT_OF_EXPLOIT,
//10
	ERR_NOT_ENOUGH_MATERIAL,
	ERR_CAN_NOT_LOGOUT,
	ERR_ITEM_CAN_NOT_BE_DESTORYED,
	ERR_ITEM_CAN_NOT_EQUIP,
	ERR_LOOT_MATTER_ACCESS_DENIED,
	ERR_IN_OTHER_ACTION,
	ERR_IN_MOVING,
	ERR_TRADE_INVITE_LIST_FULL,              // 邀请列表满了
	ERR_TRADE_ALREADY_INVITE,                // 已经邀请过了
	ERR_TRADE_REQUEST_LIST_FULL,             // 申请列表满了
//20
	ERR_TRADE_ALREADY_REQUEST,               // 已经申请过了
	ERR_TASK_NOT_AVAILABLE,
	ERR_LEVEL_NOT_MATCH,
	ERR_PROF_NOT_MATCH,
	ERR_PROF_LVL_NOT_MATCH,
	ERR_ITEM_CAN_NOT_USE,
	ERR_ACTION_CAN_NOT_STOP,
	ERR_CAN_NOT_LEARN_SKILL,
	ERR_GATHER_NO_TOOL,
	ERR_NO_SKILL,
//30
	ERR_MINE_HAS_BEEN_LOCKED,
	ERR_MINE_GATHER_FAILED,
	ERR_FACTION_NOT_MATCH,
	ERR_SKILL_NOT_AVAILABLE,
	ERR_EMBEDED_FAILED,
	ERR_OPERATION,
	ERR_ADDITION_FAILED,
	ERR_NOT_ENOUGH_EXP,
	ERR_CAN_NOT_LEARN_TALENT,
	ERR_ENCHANT_FAILED,
//40
	ERR_UPGRADE_ITEM_FAILED,
	ERR_RESET_UPGRADE_ITEM_FAILED,
	ERR_MAX_LEVEL,
	ERR_MAX_PROF_LEVEL,
	ERR_MAX_PROF_PHASE_LEVEL,
	ERR_PROF_LVLUP_FAILED,
	ERR_EQUIP_CAN_NOT_STRICT_BIND,
	ERR_EQUIP_CAN_NOT_UN_STRICT_BIND,
	ERR_MAKE_SLOT_ITEM_FAILED,
	ERR_LEARN_RECIPE_FAILED,
//50
	ERR_PRODUCE,
	ERR_CAN_NOT_DECOMPOSE,
	ERR_DECOMPOSE_FAILED,
	ERR_OUT_OF_ANGLE,
	ERR_NOT_ENOUGH_PRODUCE_POINT,
	ERR_FAILED_DUE_TO_STRICT_BIND,
	ERR_FORBIDDED_OPERATION,
	ERR_INVALID_PASSWD_FORMAT,
	ERR_PASSWD_NOT_MATCH,
	ERR_INVALID_ITEM,
//60
	ERR_INVALID_TRADE_ITEM,
	ERR_OPERATION_IS_COOLING,
	ERR_CAN_NOT_OPEN_MARKET,
	ERR_DIET_MODE,
	ERR_SILENT_MODE,
	ERR_IDLE_MODE,
	ERR_CANNOT_QUERY_ENEMY_EQUIP,
	ERR_GSHOP_INVALID_REQUEST,
	ERR_CANNOT_DUEL_TWICE,
	ERR_HERE_CAN_NOT_DUEL,
//70
	ERR_INVALID_OPERATION_IN_COMBAT,
	ERR_CREATE_DUEL_FAILED,
	ERR_BIND_INVITE_LIST_FULL,
	ERR_BIND_ALREADY_INVITE,
	ERR_BIND_REQUEST_LIST_FULL,
	ERR_BIND_ALREADY_REQUEST,
	ERR_INVALID_BIND_REQUEST,
	ERR_CANNOT_BIND_HERE,
	ERR_FORBIDDED_OPERATION_IN_SAFE_LOCK,
	ERR_USE_ITEM_FAILED,
//80
	ERR_INVALID_GENDER,
	ERR_BIND_FAILED,
	ERR_ITEM_NEED_BATTLEGROUND,
	ERR_CONTROL_TOO_MANY_TURRETS,
	ERR_OBJECT_TOO_FAR,
	ERR_CAN_NOT_TRANSMIT_IN,
	ERR_CAN_TRANSMIT_OUT,
	ERR_CAN_NOT_FIND_SPOUSE,
	ERR_CAN_TRANSMIT_IN,
	ERR_NOT_ENOUGH_REPUTATION,
//90
	ERR_LIMITED_OP_NEED_BIND_ITEM,
	ERR_MOUNT_SEAL,
	ERR_INSTALL_HARNESS_SUCCESS,	//骑乘道具装配成功
	ERR_INSTALL_HARNESS_FAIL,	//骑乘道具装配失败
	ERR_TRANSFER_HARNESS_SUCCESS,	//骑乘道具转移成功
	ERR_INSTALL_HARNESS_EXIST_FAIL,	//已有该类属性组无法附加
	ERR_NEED_PET_BEDGE,
	ERR_PET_SUMMON_TOO_MUCH,
	ERR_PET_IS_ALEARY_SUMMON,
	PET_ERR_NOT_ENGAGED,
//100
	PET_ERR_NOT_ENOUGH_LEVEL,
	PET_ERR_LEVEL_DIFFERENCE_TOO_MUCH,
	PET_ERR_NOT_ENOUGH_REPUTATION,
	PET_ERR_DEAD,
	ERR_SUMMON_PET_INVALID_POS,
	PET_ERR_NOT_ENOUGH_ENERGY,
	ERR_FATAL_ERR,
	ERR_PET_NOT_SUMMON,
	ERR_PET_COMBINE_TOO_MUCH,
	ERR_PET_IS_ALEARY_COMBINE,
//110
	ERR_PET_NOT_COMBINE,
	ERR_PET_IS_NOT_READY,
	ERR_CAN_NOT_SUMMON_PET_IN_TRAINING,
	ERR_CAN_NOT_SUMMON_NO_LIFE_PET,
	ERR_CREATE_TRADE_FAILD,
	ERR_NOT_IN_TEAM,
	ERR_NOT_TEAM_LEADER,
	ERR_TEAM_MEMBER_LEVEL_NOT_MATCH,
	ERR_TEAM_MEMBER_PROF_NOT_MATCH,
	ERR_NOT_ENOUGH_MEMBER,
//120
	ERR_MAX_ENTER_INSTANCE_TIMES,
	ERR_PET_ALREADY_HAS_IDENTITY,
	ERR_MAX_PET_LAYER,
	ERR_NOT_ENOUGH_EVALUATION,
	ERR_OBJECT_TOO_CLOSE,	// 与目标距离太近
	ERR_OBJECT_MIN_HP_LIMIT,	// 目标hp太低
	ERR_OBJECT_MAX_HP_LIMIT,	// 目标hp太高
	ERR_WRONG_SCENE,	// 所处场景不对
	ERR_WRONG_INSTANCE,	// 所处副本不对
	ERR_MIN_POS_LIMIT,	// 所处位置太小
//130
	ERR_MAX_POS_LIMIT,	// 所处位置太大
	ERR_FIGHT_UNUSABLE,	// 不能在战斗状态下使用
	ERR_NOT_ENOUGH_MONEY,	// 金钱不够(这个现在金币、钻石、点券不足都用这个！！！)
	ERR_NOT_ENOUGH_MT_BIND,	// 金币不足
	ERR_OUT_OF_RANGE_FOR_TEAM_FOLLOW,
	ERR_INVALID_STATE_FOR_TEAM_FOLLOW,
	ERR_NOT_BIND_LEADER,
	ERR_PRODUCE_1,
	ERR_PRODUCE_2,
	ERR_PRODUCE_3,
//140
	ERR_PRODUCE_4,
	ERR_PRODUCE_5,
	ERR_PET_WEAK,
	ERR_NOT_ENOUGH_SOCIAL_POINT,		//社交点不足
	ERR_GRADE_CHAT_COOLDOWN,		//同等级聊天冷却未到
	ERR_GRADE_CHAT_LEVEL,			//未加入同等级频道
	ERR_EQUIP_REINFORCE_FAILED,		//装备强化失败
	ERR_CATCH_PET_FAILED,			//抓宠失败
	ERR_INVENTORY_BIND_MONEY_LIMIT,		//银票携带数已达上限
	ERR_INVENTORY_TRADE_MONEY_LIMIT,	//银子携带数已达上限
//150
	ERR_OUT_OF_TRADE_MONEY,		//银子不足
	ERR_NOT_MIRROR_SCENE,		//不是镜像场景
	ERR_NO_MIRROR,			//镜像不存在
	ERR_MIRROR_FULL,		//镜像承载人数过多
	ERR_INVALID_POSITION,		//无效的地图坐标
	ERR_TOO_MANY_BIND_MEMBERS,	//绑定了太多的成员无法完成操作
	ERR_UNREACHE_SCENE,		//玩家没有到过此地图不能完成操作
	ERR_NOT_MARRIED,		//玩家未婚
	ERR_SPOUSE_IN_INSTANCE,		//配偶处于副本中
	ERR_CANNOT_CHANGE_SCENE,	//处于不能切换场景的状态
//160
	ERR_ROLE_OFFLINE,		//玩家已经下线
	ERR_OPERATION_TIMEOUT,		//操作超时
	ERR_LINE_NOT_FOUND,		//指定地图暂时无法服务
	ERR_TEAM_INSTANCE,		//队伍不满足开启副本条件
	ERR_TEAM_IN_INSTANCE,		//队伍已经开启了一个副本
	ERR_TEAM_MEMBERS_NOT_ONLINE,	//队伍成员有人不在线
	ERR_TEAM_LEADER_TOO_FAR,	//距离队长太远
	ERR_MARKET_TOO_NEAR,		//距离其他摆摊玩家过近
	ERR_MULTI_BIND_JUMP_STATE,	//多人绑定传送中有人不能进行传送
	ERR_USE_ITEM_IN_INST,		//此物品不能在副本中使用
//170
	ERR_NO_POSTHOUSE,		//当前地图没有驿站
	ERR_NOT_READY,			//有玩家状态不对
	ERR_NAME_USED,			//名字已经被使用
	ERR_INVALID_NAME,		//名字包含非法字符
	ERR_FACTION_FULL,		//阵营人数已满
	ERR_PRODUCE_BIND_IDLE,		//空占生产载具时间过长
	ERR_INSTANCE_MODE_NO_ACHIEVE,	//副本模式成就不满足
	ERR_TASK_NOTFINISH,		//前提任务未完成
	ERR_FORCE_NOTENOUGH,		//功力不足,无法修炼
	ERR_SKILL_LEARNED,		//该技能已学
//180
	ERR_ITEM_CAN_NOT_TAKEOUT,	//物品不能取下
	ERR_CANNOT_PRACTICE_REGION,	//该区域不能修炼
	ERR_CANNOT_PRACTICE_STATE,	//当前状态下无法进行修炼
	ERR_NO_FOUCS_PLAYER,		//没有可关注的人
	ERR_PURCHASE_FAIL,		//收购失败
	ERR_NO_MAFIA,			//玩家没有帮派
	ERR_NO_MAFIA_BASE,		//玩家不在帮派基地中
	ERR_NO_MAFIA_CONRIBUATION,	//玩家当日未完成帮派帮贡
	ERR_NO_MAFIA_PERMISSION,	//没有权限
	ERR_MAFIA_WELFARE,		//已经领取过福利，或者不存在
//190
	ERR_NO_BUILDING_UPGRADING,	//没有建筑正在升级
	ERR_BUILDING_UPGRADING,		//有建筑正在升级
	ERR_MAFIA_UPGRADING_ITEM,	//帮派升级物品错误
	ERR_MAFIA_ITEM_FULL,		//指定升级物品已经满足
	ERR_MAFIA_BUILDING_MAX_LEVEL,	//建筑已经到达最高级
	ERR_NO_FAMILY,			//未结义
	ERR_ALLIANCE_STATE,		//当前盟主战阶段不支持此服务
	ERR_ALLIANCE_MAX,		//盟主战结义数量已满
	ERR_CANNOT_JUMP_IN_WITH_ITEM,	//目标场景不能使用物品进入
	ERR_NOT_JOIN_WAR,		//玩家没有参战
//200
	ERR_CANNT_JOIN_WAR,		//玩家不能进入盟主战
	ERR_NOT_LEAGUE,			//不是盟主没有权限
	ERR_ALLIANCE_RATE_CHANGE,	//盟主销售服务税率改变警告
	ERR_ALLIANCE_UNACTIVE,		//盟主服务未激活
	ERR_MAFIA_BASE_CLOSED,		//帮派基地关闭
	ERR_LEAGUE_FAMILY,		//盟主所在结义，无须报名
	ERR_MAFIA_MAX_SUB,		//帮派分舵数已满
	ERR_MAFIA_NO_BUILD,		//帮派没有此建筑
	ERR_MAFIA_LOG_PUB_SUCCESS,	//发布招工信息成功
	ERR_MAFIA_LOG_PUB_NOMONEY,	//发布招工信息钱不够
//210
	ERR_MAFIA_LOG_PUB_NOCOUNT,	//发布招工信息额度不够
	ERR_MAFIA_LOG_PUB_INVALIDREQ,	//发布招工信息请求信息非法
	ERR_MAFIA_LOG_PUB_EXIST,	//招工信息已经存在
	ERR_MAFIA_TEMPLATE,		//帮派相关模板错误
	ERR_MAFIA_STORE_NOSPACE,	//帮派仓库没有足够空位
	ERR_MAFIA_SUB_MAFIA,		//已经在这个帮建立了分舵
	ERR_MAFIA_APPLIED,		//已发送分舵申请
	ERR_NO_MAFIA_BASE2,		//开宗立派后拥有驻地的帮派才能申请建立分舵
	ERR_OTHER_MAFIA_BASE_CLOSED,	//对方基地已经关闭
	ERR_NO_SUB_MAFIA,		//没有对应分舵
//220
	ERR_TOO_MANY_TEAM_MEMBERS,	//组队人数超过所限人数的上限
	ERR_NO_MAFIA_WELFARE,		//没有帮派福利可以领取
	ERR_NO_MAFIA_TITLE,		//帮派身份条件不足
	ERR_MAFIA_SALARY_GOT,		//已经领取薪水
	ERR_MAFIA_BONUS_GOT,		//已经领取供奉
	ERR_HOME_TRANSFER,		//家园传送失败
	ERR_MAFIA_LOG_TYPE,		//帮派招工信息类型错误
	ERR_MAFIA_ACT_OPEN_SUCCESS,	//帮派活动打开成功
	ERR_MAFIA_ACT_OPEN_FAILED,	//帮派活动打开失败
	ERR_MAFIA_ACT_OPEN_NOTALLOW,	//权限不够，不能开启帮派活动
//230
	ERR_MAFIA_ACT_OPEN_VALUELESS,	//因为活跃度，协作值或者温暖度不够，不能开启活动
	ERR_SCENE_NOT_SUPPORT,		//当前场景不支持
	ERR_CAN_NOT_EQUIP_REGION,	//当前区域不能更换武器
	ERR_MAFIA_NOT_ENOUGH_MONEY_SALARY,	//帮派资金不足无法发放薪水
	ERR_MAFIA_NOT_ENOUGH_MONEY_BONUS,	//帮派资金不足无法发放供奉
	ERR_TEAM_MEMBER_TOO_FAR,	//队伍成员必须都在附近
	ERR_EXIST_ESCORT,		//有尚未完成的运镖/劫镖
	ERR_EXIST_ESCORT_GOODS,		//队员有尚未兑换的镖货
	ERR_ESCORT_MAX_TIMES,		//有队员今日次数已达上限
	ERR_GINFO_NO_ITEMS,		//收集信息需要的物品不够
//240
	ERR_GINFO_NO_REP,		//收集信息需要的声望不够
	ERR_GINFO_NO_TIMES,		//收集信息次数已超上限
	ERR_GINFO_NO_SPACE,		//收集信息需要的包裹空间不够
	ERR_GINFO_ODDS_FAILED,		//收集信息时随机失败
	ERR_GUESS_NOITEM,		//客户端请求的推理游戏不存在
	ERR_MAFIA_MSG_NEEDARRAGE,	//消息仓库需要整理
	ERR_MAFIA_HOSTAGE_NUMLIMIT,	//场景中肉票已达上限
	ERR_MAFIA_HOSTAGE_BIND,		//缴纳肉票时不处于绑定状态
	ERR_NOT_ENOUGH_VIGOR_ACTIVITY,	//精力值不足
	ERR_GUESS_STOREFULL,		//帮派活动数已达上限
//250
	ERR_FCITY_MAINOPER_SUCCESS,	//帮派搬迁成功
	ERR_FCITY_MAINOPER_FAILED,	//帮派搬迁失败
	ERR_AUCTION_OPENED,		//拍卖已经开启
	ERR_MAFIA_MONEY_FULL,		//帮派金钱已满
	ERR_MAFIA_SYSTEM,		//帮派基地服务器内部错误
	ERR_MAFIA_BUFF_MAX,		//玩家身上藏宝阁buff数目已达上限
	ERR_MAFIA_BUFF_SAMECLASS,	//玩家身上存在该类藏宝阁buff
	ERR_MAFIA_BUFF_NOTSUPPORT,	//藏宝阁当前不能发放该buff
	ERR_MAFIA_BUFF_CONLESS,		//领取buff所要的帮贡不够
	ERR_MAFIA_BUFF_DELNOTEXIST,	//玩家希望被替换(覆盖)的buff不存在
//260
	ERR_MAFIA_TREASURE_MAXLEVEL,	//藏宝阁把购物已经是最高级别了
	ERR_MAFIA_TREASURE_ADVANCEFAILED,//藏宝阁宝物升级失败
	ERR_MAFIA_TRAP_MAX,		//藏宝阁机关数目已达上限
	ERR_MAFIA_TRAP_EXIST,		//藏宝阁该位置已经有机关存在了
	ERR_MAFIA_NO_TREASURE,		//藏宝阁该位置没有宝物
	ERR_MAFIA_TREASURE_STEALSELF,	//不能监守自盗
	ERR_MAFIA_TRAMSSTATE,		//盗宝需要变身
	ERR_MAFIA_TREASUREMAP_LESS,	//盗宝需要的地图碎片不够
	ERR_MAFIA_STEAL_DEBUFF,		//盗宝中机关加DEBUFF
	ERR_MAFIA_STEAL_RANDOMFAILED,	//盗宝随机失败
//270
	ERR_SERVICE_ALREADY,		//不能重复使用服务
	ERR_PRODUCE_MAXLEVEL,		//该生产技能已经是最高等级了
	ERR_PRODUCE_LVITEM_NOTMATCH,	//生成技能物品和待升级技能等级不符合
	ERR_PRODUCE_NOLVITEM,		//没有升级生成技能的等级的物品
	ERR_MAFIA_PARTY_HASOPEN,	//已经有宴会开启了
	ERR_MAFIA_PARTY_NOMONEY,	//开启宴会需要的帮派资金不够
	ERR_MAFIA_PARTY_NOITEM,		//开启宴会需要的物品不够
	ERR_MAFIA_ACTIVITY_RUNNING,	//希望删除的活动正在被开启
	ERR_ESCORT_ALREADY_POISON,	//278 目标镖车在本驿站已经被下过毒了
	ERR_MAFIA_GATHERINFO_NOCITY,	//贵帮还没有在这里建立分舵，不能采集
//280
	ERR_MAFIA_GATHERINFO_ONEBYONE,	//已经有人在拷打了，稍等会吧
	ERR_ESCORT_REWARD_MISS,		//281 离队长过远，没收到运镖奖励
	ERR_SKILL_MAX_MONEY,		//282 技能得钱达到上限
	ERR_MAFIA_BONUS_NOTINTIME,	//现在还不能领供奉
	ERR_MAFIA_SALART_NOTINTIME,	//现在还不能领薪水
	ERR_NOT_ENOUGH_TEAM_MEMBER,	//队伍人数不足，无法开启副本
	ERR_BADGE_HAS_EXIST,		//徽章已经存在
	ERR_WRONG_ZONE_ID,		//选择了错误的服务区
	ERR_CANNOT_CHANGE_ZONE,		//不能连续跨服，处在不能跨服状态
	ERR_TIZI_HASEXIST,		//该处已有题字，请先擦掉题字
//290
	ERR_TIZI_NOTEXIST,		//该处是空白的，不需要擦拭
	ERR_PLAYER_LOCK_TIMEOUT,	//切换地图超时
	ERR_SPECTATOR_FAILED,		//成为观看者失败
	ERR_FROM_MAFIA_BASE,		//只能在帮派基地使用此服务
	ERR_FROM_SELF_MAFIA_BASE,	//只能在自己的帮派基地使用
//295
	ERR_WX_IN_COMBAT,		//战斗状态操作武学
	ERR_WX_IN_COOLTIME,		//武学操作冷却中
	ERR_WX_JUEJI_TOO_MUCH,		//武学包含绝技太多
	ERR_WX_WRONG_INDEX,		//武学不存在
	ERR_WX_WRONG_ZHAOSHI,		//武学招式错误
//300
	ERR_WX_WRONG_JUEJI,		//武学绝技错误
	ERR_ROAM_TIME_NOT_ENOUGH,	//跨服时间不足
	ERR_MAFIA_NO_PERMISSION,	//帮派操作权限不够
	ERR_MAFIA_TIZINOTTIGUAN,	//贵帮不是踢馆发起者，不能题字
	ERR_DX_NOT_MINGXING,		//雕像: 不是对应明星
//305
	ERR_DX_ITEM,			//雕像: 缺少物品
	ERR_DX_STATUS,			//雕像: 正在进行其他物品操作中
	ERR_MAFIA_TIGUANING,		//目标基地正在被踢馆中
	ERR_MATERIAL_INVENTORY_IS_FULL, //材料包裹已满
	ERR_TASK_INVENTORY_IS_FULL, 	//任务包裹已满
//310
	ERR_SECURITY_LOCKED,		//安全锁定中
	ERR_MULTILOGIN,			//玩家已经处于登入状态
	ERR_INST_TEAM_LEADER_TOO_FAR,	//距离队长过远，副本无法开启
	ERR_FLAG_POS_ERR,		//飞行棋位置配置错误或失效
	ERR_GINFO_EXCLUDE_BUFF_EXIST,	//收集信息前提禁止BUFF存在，不允许收集
//315
	ERR_MAFIA_PARTY_MONEY_OPEN_MAX,	//帮派资金开启宴会次数到达上限
	ERR_MAFIA_PARTY_ITEM_OPEN_MAX,	//道具开启宴会次数到达上限
	ERR_MAFIA_GFT_NOTASK,		//帮派已经没有发粮任务了
	ERR_MAFIA_GFT_SELFMAX,		//个人发粮任务可用次数已经用完
	ERR_NEED_ROAM_STATE,		//只有跨服才能使用本服务
//320
	ERR_PEER_FORCE_MAX_TIMES,	//对方传功次数不足
	ERR_PEER_FORCE_NOT_ENOUGH,	//对方功力不足
	ERR_KASI_RST_IN_PVP_STATE,	//pvp状态不能脱离卡死
	ERR_KASI_RST_IN_COOLTIME,	//脱离卡死冷却中
	ERR_KASI_RST_WRONG_POS,		//脱离卡死目标位置有误
//325
	ERR_MAFIA_GFT_PERMISSION,	//该玩家没有领取发粮任务权限，他是挂名成员
	ERR_NO_ACTIVITY_POINT_FOR_EXP,	//精力值为0，无法获得地宫怪经验
	ERR_SUICIDE_IN_COOLTIME,	//自杀操作冷却中
	ERR_GIFT_INVENTORY_IS_FULL,	//礼物包裹已满
	ERR_PET_INVENTORY_IS_FULL,	//门徒栏已满(宠物栏)
//330
	ERR_TRANSACTION_PENDING,        //角色数据处于事务状态中，暂时不能发起新交易
	ERR_OUT_OF_CASH,                //元宝不够
	ERR_INSTANCE_REQUIREMENT,	//副本前提条件不对（没有完成前提副本）
	ERR_RAND_SELECT_INSTANCE_NOT_EXIST,	//随机选择副本不存在
	ERR_RAND_SELECT_INSTANCE_COOLTIME,	//随机选择副本不存在
//335
	ERR_IS_NOT_SCENE_VIP,
	ERR_PEER_DEC_TRADE_ITEM_OR_MONEY,	//交易时点锁定需要几秒钟的等待期，否则就报这个错误
	ERR_PROF_EXP_NOT_ENOUGH,	//职业经验（功力）不足，无法修炼
	ERR_TRAINING_FAILED,		//无法修炼，修炼类型错误
	ERR_EQUIP_FUSION_FAILED,
//340
	ERR_EQUIP_FUSION_COUNT_MAX,
	ERR_EQUIP_FUSION_TYPE_FAILED,
	ERR_FUSION_INDEX_ERROR,
	ERR_INSTANCE_NEED_TMP_BACK_EMPTY,	//包裹非空
	ERR_ROLE_DELETED,		//玩家角色已经删除
//345
	ERR_CAN_NOT_SUMMON_FRIEND,	//无法邀请此好友
	ERR_CAN_NOT_SUMMON_FRIEND_DELETE,//无法邀请此好友 ，好友已经删除
	ERR_TMP_BACK_HAS_REWARD_NOW,	//临时包裹里有尚未领取的奖励
	ERR_TRADE_INVITE_TOO_FAR,	//邀请目标不在同一地图
	ERR_DL_WAIT_COOLDOWN,
//350
	ERR_DL_COMMON_LIMIT,
	ERR_DL_SERVICE_ERR,
	ERR_AUTO_REWARD_OUT_OF_TIME,
	ERR_EQUIP_FUSION_REMOVE_FAILED,
	ERR_PATA_WRONG_INSTANCE,	//非爬塔副本
//355
	ERR_PATA_NO_MORE_LEVEL,		//已经通关了，没法再继续
	ERR_PATA_NO_RESET_CHANCE,	//重置机会已经用完了
	ERR_SUPER_HERO_NEED_REP,
	ERR_SUPER_HERO_NEED_MATERIAL,
	ERR_AUTO_REWARD_INVALID_OP,
//360
	ERR_NATION_IN_WAR,		//指定国家正在国战
	ERR_HERO_NONE,			//没有找到指定的目标
	ERR_NO_FORCE,			//没有体力了
	ERR_VIP_LIMIT,			//没有相应的VIP权限
	ERR_INVENTORY_SIZE,		//包裹扩展失败
//365
	ERR_OPERATION_LIMIT,		//没有操作权限
	ERR_WAITING_MIDAS,		//异步等待米大师返回(不是错误，只是内部通知)
	ERR_PLANT_NO_SEED,		//没有配置
	ERR_PLANT_NO_ENOUGH_TIME,	//使用次数不足
	ERR_PLANT_HAS_NO_SOWED,		//没种植过该种子
//370
	ERR_PLANT_NO_RIPE,		//没成熟
	ERR_NATION_LIMIT,		//当前操作只能在本国进行
	ERR_MIDAS_BUSY,			//米大师反满
	ERR_NATIONWAR_ITEM,		//国战期间不能使用
	ERR_NOT_HAVE_RETRIEVE_COUNT,	//没有找回的次数
//375
	ERR_NO_PLAYER_FOUND,		//周围没有玩家
	ERR_FASHION_INVENTORY_SIZE,	//时装包裹没有足够的空间
	ERR_RED_PACKAGE_NORIGHT,	//没权限领取红包
	ERR_VALUE_IS_MAX,		//双倍时间超过上限了
	ERR_ESCORT_STATE,		//镖车状态禁止的操作
//380
	ERR_INVENTORY_IS_FULL_NOTIFY,   //背包已满，提示奖励放到临时背包，
	ERR_NOT_FIND_UPGRADEING_CHARIOT,//没有找到要升级的战车
	ERR_JOIN_TIME_NOT_ENOUGH,	//帮众加入帮会不足24小时
	ERR_UP_CHARIOT_REPU_NOT_ENOUGH, //升级帮会战车声望不足
	ERR_NOT_FIND_RENT_CHARIOT,	//没有找到要领取的战车
//385
	ERR_IS_RENTING_CHARIOT,		//正在领取战车
	ERR_RENTED_CHARIOT,		//已领取战车
	ERR_RENTED_CHA_REPU_NOT_ENOUGH, //领取战车帮贡不足
	ERR_CANNOT_USE_IN_CURRENT_SCENE,//不能在当前场景使用该物品
	ERR_ALREADY_HAVE_THIS_RIDE,	//已经拥有改坐骑
//390
	ERR_OPEN_MINE_LACK_TIMES,       //天降宝箱活动期间次数已用完
	ERR_PET_EXP_FULL,		//宠物经验满
	ERR_INSTANCE_PRE_TASK_WRONG,	//副本前提任务不满足
	ERR_PET_REBORN_TOOL_WRONG,	//宠物洗髓物品错误
	ERR_PET_REBORN_ERROR,		//宠物洗髓错误
//395
	ERR_PET_MIXIS_NO_OTHER_PET,	//宠物融合缺另一只
	ERR_PET_MIXIS_ERROR,		//宠物融合错误
	ERR_RETINUE_LEVEL_LIMIT,	//等级限制不能激活随从阵法
	ERR_RETINUE_POWER_LIMIT,	//战力限制不能激活随从阵法
	ERR_PRACTICE_LEVEL_LIMIT,	//修炼已经到达最大等级
//400
	ERR_NPC_IS_BUSY,		//npc功能正在被人使用
	ERR_EXP_LOTTERY_CANNOT_FREE,	//客栈打听经验无法免费打听
	ERR_EXP_LOTTERY_ALREADY_GET,	//已经领取客栈经验
	ERR_TF_CANNOT_FOLLOW_LEADER,	//无法跟随队长
	ERR_TF_CHANGE_GS_SYNC,		//队长切换地图临时发送队员离队，因为可能会有队员无法切换，所以先离开，在进入
//405
	ERR_TF_CHANGE_LEADER,		//切换队长
	ERR_TF_SCENE_CANNOT_FOLLOW,	//队长所在场景无法组队跟随
	ERR_TF_CLIENT_REQUEST,		//客户端请求
	ERR_IN_TEAM_FOLLOW,		//组队跟随状态禁止
	ERR_STORE_EXP_LIMIT,		//储存经验达到上限
//410
	ERR_CTF_INVALID_PARAM,		//假跟随状态参数错误
	ERR_CTF_INVALID_TEAM,		//假跟随状态队伍信息错误
	ERR_RETINUE_HAS_TASK,		//随从在执行任务
	ERR_RETINUE_IN_FORMATION,	//随从在出阵
	ERR_RETINUE_TASK_FAILED,	//接取随从任务失败
//415
	ERR_RETINUE_RECV_TASK_AWARD_FAILED,	//领取随从任务奖励失败
	ERR_TARGET_OFFLINE,			//目标离线
	ERR_REVENGE_BATTLE_MONEY,		//约战钱不够
	ERR_REVENGE_BATTLE_SOURCE_LEVEL,	//约战自己等级不足
	ERR_REVENGE_BATTLE_TARGET_LEVEL,	//约战对方等级不足
//420
	ERR_REVENGE_BATTLE_IN_INSTANCE,		//约战对方在副本
	ERR_REVENGE_BATTLE_IN_TRUSTEESHIP,	//约战对方处于托管状态
	ERR_REVENGE_BATTLE_IN_BATTLE,		//约战对方已经处于约战中
	ERR_REVENGE_BATTLE_IN_SHIELD,		//约战对方处于护盾之中
	ERR_REVENGE_BATTLE_LIMIT,		//约战次数限制
//425
	ERR_REVENGE_BATTLE_OFFLINE,		//约战对方不在线
	ERR_FORTUNE_CAT_MONEY,			//招财猫金钱不足
	ERR_FORTUNE_CAT_LIMIT,			//招财猫达到次数上线
	ERR_RETRIEVE_NO_DOUBLE_POINT,   //奖励找回双倍点数不足
	ERR_RETRIEVE_NO_LIMIT,          //奖励找回次数不足
//430
	ERR_REVENGE_BATTLE_CREATE,		//约战副本创建失败
	ERR_SECT_POSITION,			//带徒拜师位置错误
	ERR_SECT_TEAM,				//带徒拜师队伍错误
	ERR_SECT_LEVEL,				//带徒拜师等级错误
	ERR_PACKAGE_LESS_SLOT,			//背包栏位不足
//435
	ERR_WEDDING_CLOSE,			//婚礼时间没有开启
	ERR_WEDDING_SCENE,			//婚礼错误的场景
	ERR_WEDDING_SPOUSE_TOO_FAR,		//婚礼结婚对象距离过远
	ERR_IN_ROAM_STATE,			//跨服状态禁止
	ERR_SUR_COLOR_WRONG_ITEM,
//440
	ERR_SUR_COLOR_OPEN_ALL,
	ERR_SUR_COLOR_LESS_ITEM,
	ERR_SUC_COLOR_WRONG_PART,
	ERR_INVALID_REWARD,			//不可用的奖励
	ERR_RENAME_CONFIG_INVALID,      	// 改名配置不合法
//445
	ERR_RENAME_NO_ITEM,     		// 缺少改名卡道具
	ERR_RENAME_REMOVE_ITEM_FAILED,  	// 扣除改名卡道具失败
	ERR_RENAME_SERVER_NETWORK,      	// 改名时服务器通信错误
	ERR_RENAME_FAILED,      		// 改名一般错误
	ERR_RENAME_ACTIVITY_CLOSED,     	// 改名活动未开启
//450
	ERR_EASY_MALL_TIME,			// EasyMall服务没有到开启时间
	ERR_PET_READAN_INVALID_PARAM,		// 宠物内丹错误的参数
	ERR_PET_READAN_LACK_OF_ITEM,		// 宠物内丹缺少材料
	ERR_PET_READAN_MAX_LEVEL,		// 宠物内丹等级已满
	ERR_PET_READAN_INV_FULL,		// 宠物内丹包裹已满
//455
	ERR_PET_READAN_INACTIVE,		// 宠物内丹未激活
	ERR_HOMETOWN_COOLDOWN,			// 访问家园冷却中
	ERR_HOMETOWN_UNAVALIABLE,		// 对方没有家园
	ERR_HOMETOWN_NO_PERMIT,			// 访问家园没有权限（不在排行榜，或不是好友）
	ERR_HOMETOWN_FULL,			// 对方家园人数已满
//460
	ERR_WEAPON_CANNOT_BE_CHANGED,		//武器不可以转换
	ERR_WEAPON_NOT_EXIST,			//武器不存在
	ERR_ON_CHANCE_CHANGE_WEAPON,		//没有转换武器的机会
	ERR_CHANGE_PROF_COOLDOWN,		//转职CD
	ERR_CHANGE_PROF_WEAPON,			//指定武器错误
//465
	ERR_WRONG_PROF,				//职业不符
	ERR_ROAMOUT,				//玩家处于跨服中
	ERR_PARADING_SPOUSE_TOO_FAR,		//婚礼巡游对象距离过远
	ERR_PARADING_WRONG_SPOUSE,		//婚礼巡游对象错误
	ERR_IN_PARADING,			//处于婚礼巡游状态
//470
	ERR_PET_SKILL_LOCK_INVALID_PARAM,	//宠物技能锁定参数错误
	ERR_PET_SKILL_LOCK_STATE_ERROR,		//宠物技能锁定状态错误
	ERR_PET_SKILL_LOCKED_FULL,		//宠物技能锁定已满
	ERR_PET_SKILL_LOCK_LACK_OF_ITEM,	//宠物技能锁定缺少材料
	ERR_PET_SKILL_LOCK_FAILED,		//宠物技能锁定失败
//475
	ERR_SNATCH_LEVEL_LIMIT,			//抢亲等级不足
	ERR_SNATCH_LACK_OF_ITEM,		//抢亲所需物品不足
	ERR_SNATCH_TARGET_NOT_IN_CEREMONY,	//抢亲对象没有处于婚礼或者巡游状态
	ERR_SNATCH_TARGET_PROTECTED_TIME,	//抢亲对象处于保护时间内
	ERR_SNATCH_TARGET_BUSY,			//抢亲对象处理其他抢亲事物
//480
	ERR_SNATCH_TASK_LIMIT,			//抢亲任务限制
	ERR_SNATCH_IN_TEAM,			//组队不可以抢亲
	ERR_PARADING_IS_BUSY,			//婚礼巡游忙碌中
	ERR_PARADING_LIMIT_IN_TEAM_FOLLOW,	//组队跟随状态禁止婚礼巡游
	ERR_PARADING_LIMIT_IN_RELATION,		//工程相依相偎状态禁止婚礼巡游
//485
	ERR_PET_CANNOT_UPGRADE,			//宠物不能升阶
	ERR_PET_UPGRADE_LACK_OF_ITEM,		//宠物升阶缺少材料
	ERR_REVENGE_BATTLE_IN_PARADING,		//巡游中禁止约战
	ERR_CHANGE_ZONE_LIMIT_PARADING,		//巡游禁止跨服
	ERR_PARADING_LIMIT_COMBAT,		//战斗状态禁止巡游
//490
	ERR_PARADING_LIMIT_ESCORT,		//护送状态禁止巡游
	ERR_SNATCH_WRONG_STATE,			//错误的抢亲状态
	ERR_PARADING_WRONG_STATE,		//错误的巡游状态
	ERR_PARADING_LIMIT_DUEL,		//巡游状态禁止决斗
	ERR_BE_MARRIED,				//已结婚或者怀孕
//495
	ERR_NEED_CHILD_BEDGE,			//孩子不存在
	ERR_CHILD_REBORN_ERROR,			//孩子洗髓错误
	ERR_CHILD_REBORN_TOOL_WRONG,		//孩子洗髓物品错误
	ERR_SNATCH_WRONG_WEDDING_OWNNER,	//抢亲对象不是婚礼主人
	ERR_SNATCH_WEDDING_CLOSE,		//婚礼抢亲婚礼没有开始
//500
	ERR_CHILD_IS_ALREADY_SUMMON,		//孩子已经被召唤
	ERR_CHILD_EXP_FULL,			//孩子经验满
	ERR_CHILD_SKILL_EXIST,			//孩子技能已经存在
	ERR_CHILD_IS_NOT_READY,			//孩子在CD
	ERR_CHILD_INV_FULL,			//孩子背包
//505
	ERR_SNATCH_WEDDING_ALREADY,		//不能重复抢亲
	ERR_SNATCH_CANNOT_REVENGE_BATTLE,	//婚礼抢亲中不能进行约战
	ERR_SNATCH_TARGET_NOT_FOUND,		//抢亲对象不存在
	ERR_DUEL_LIMIT_XYXW,			//决斗状态禁止发起相依相偎
	ERR_DUEL_TARGET_LIMIT_XYXW,		//对方决斗状态禁止发起相依相偎
//510
	ERR_CANNOT_SUMMON_CHILD,		//孩子不能召唤
	ERR_REVENGE_BATTLE_IN_DUEL,		//决斗禁止约战
	ERR_SNATCH_TARGET_TOO_FAR,		//抢亲对象过远
	ERR_SLEEP_SUCCESS,				//睡觉成功
	ERR_WAKEUP_SUCCESS,				//唤醒成功

//515
	ERR_CHILD_DEPO_FULL,			//孩子仓库已满
	ERR_DEVOUR_DESTINY,				//天命吞噬错误
	ERR_DESTINY_NOT_EXIST,				//天命不存在
	ERR_DESTINY_OP_INDEX_ERROR,			//天命操作校验失败
	ERR_HAS_SAME_DESTINY,			//有相同的天命
//520
	ERR_DESTINY_PACK_FULL,			//天命包裹已满
	ERR_PARADING_WRONG_SCENE,		//错误的巡游场景
	ERR_FROM_HOMETOWN,			//第二家园需要从第一家园跳入
	ERR_SECOND_HOMETOWN_COOLDOWN,			//第二家园冷却中
	ERR_SECOND_HOMETOWN_NOT_OPEN,			//第二家园GMT关闭
//525
	ERR_SECOND_HOMETOWN_NOT_ENABLED,		//第二家园未激活
	ERR_WRONG_SNATCH_SCENE,			//错误的抢亲场景
	ERR_VOWS_SPOUSE_TOO_FAR,		//宣誓对象距离过远
	ERR_CORPS_CENTER_BATTLE_AWARD_COOLDOWN,	//跨服帮派领奖冷却中
	ERR_CORPS_CENTER_BATTLE_AWARD_LIMIT,	//跨服帮派奖励已经领过了
//530
	ERR_CORPS_CENTER_BATTLE_AWARD_JOIN_TIME_LIMIT,	//玩家是学徒或入帮不到两天
	ERR_CORPS_CENTER_BATTLE_AWARD_BATTLE_NOT_END,	//跨服帮派竞赛未结束
	ERR_CHILD_TALK_CD,              //孩子对话处于cd中
	ERR_CORPS_CENTER_BATTLE_AWARD_BATTLE_NOT_APPLY,	//跨服帮派竞赛未报名
	ERR_CORPS_CENTER_BATTLE_AWARD_BATTLE_NOT_MATCH,	//跨服帮派竞赛未开始匹配或未匹配上
//535
	ERR_CHILD_MARRY_NOT_READY,		//童婚尚为开始
	ERR_CHILD_MARRY_WRONG_PACKAGE,		//童婚错误的背包
	ERR_CHILD_MARRY_CHILD_NOTFOUND,		//童婚没有找到孩子
	ERR_CHILD_MARRY_WRONG_STATE,		//童婚状态错误
	ERR_CHILD_MARRY_CANNOT_MARRY,		//孩子无法结婚
//540
	ERR_CHILD_MARRY_SAME_GENDER,		//孩子具有相同的性别
	ERR_CHILD_MARRY_BLOOD_RELATIONSHIP,	//孩子具有血缘关系
	ERR_CHILD_MARRY_LESS_MONEY,		//童婚钱不够
	ERR_CHILD_MARRY_FAILED,			//童婚失败
	ERR_CHILD_MARRY_AMITY,			//童婚好友度不足
//545
	ERR_CHILD_WRONG_STAGE = 545,
	ERR_CHILD_IS_TOURING = 546,
	ERR_NO_TOUR_COUNT = 547,
	ERR_NO_TOUR_AWARD = 548,
	ERR_CHILD_IS_LOCKED = 549,
//550
	ERR_RETINUE_CHAT_MSG_ERROR	= 550,		//伙伴对话msgid错误
	ERR_CHILD_DIVORCE_LESS_MONEY	= 551,	//童婚离婚钱不够
	ERR_CHILD_IS_NOT_MARRIED = 552,		//孩子尚未结婚
	ERR_CHILD_FOLLOW_TOUR_TIMEOUT = 553,	//接受娃娃亲出游超时
	ERR_CHILD_LEAD_TOUR_TIMEOUT = 554,	//发起娃娃亲出游超时
//555
	ERR_REFUSE_COUPLE_TOUR = 555,		//对方拒绝娃娃亲出游邀请
	ERR_TARGET_CHILD_IS_ALREADY_SUMMON = 556,
	ERR_TARGET_CHILD_IS_LOCKED = 557,
	ERR_TARGET_CHILD_FATAL_ERR = 558,
	ERR_TARGET_CHILD_NO_TOUR_COUNT = 559,
//560
	ERR_TARGET_CHILD_NOT_EXIST = 560,
	ERR_TARGET_CHILD_WRONG_STAGE = 561,
	ERR_TARGET_CHILD_IS_NOT_MARRIED = 562,
	ERR_CHILD_DIVORCE_TARGET_LESS_MONEY	= 563,	//童婚离婚对方钱不够
	ERR_TARGET_CHILD_IS_TOURING = 564,
//565
	ERR_CHILD_MARRY_SPOUSE_TOO_FAR = 565,		//婚礼结婚对象距离过远
	ERR_CHILD_GEM_SLOT_IS_EMPTY = 566,
	ERR_CHILD_GEM_SLOT_NOT_EMPTY = 567,
	ERR_CHILD_GEM_PARAM_ERROR = 568,
	ERR_CHILD_GEM_LEVEL_TOO_HIGH = 569,
//570
	ERR_TARGET_OFFLINE_CHILD_VERSION = 570,
	ERR_CHILD_TOUR_SUCCESS = 571,
	ERR_CHILD_ADD_STAR_PARAM_ERROR = 572,
	ERR_CHILD_STAR_FULL = 573,
	ERR_CORPS_SERVER_BATTLE_AWARD_GET = 574,	//已领过跨服城战奖励
//575
	ERR_CORPS_SERVER_BATTLE_AWARD_TIME = 575,        //跨服城战奖励未到领取时间
	ERR_REPU_LIMIT = 576,
	ERR_CORPS_SERVER_BATTLE_TITLE_GET = 577,        //已领过跨服城战称号
	ERR_CORPS_SERVER_BATTLE_TITLE_TIME = 578,        //跨服城战称号未到领取时间
	ERR_CORPS_SERVER_BATTLE_TITLE_NOT_EXIST = 579,        //未参与跨服城战
	//580
	ERR_CORPS_SERVER_BATTLE_AWARD_LEVEL = 580,
	ERR_CHILD_IS_MARRIED = 581,		//孩子已婚
	ERR_GAME_MAINTAIN		= 582,		//服务器关闭期间不允许操作
	ERR_WORLD_SPEAK_LEVEL_LIMIT	= 583,	//狮吼等级未达到限制
	ERR_SKILL_NOT_MATCH_SLOT	= 584,	//技能和技能槽不匹配
	ERR_INTERACT_COOL_DOWN		= 585,	//交互冷却
	ERR_NOT_IN_INTERACT		= 586,	//不在任何交互中
	ERR_MATTER_INSTERACT_FULL	= 587,	//坐不下了
	ERR_EQUIP_AFFIXES_TRANS_SAME_ITEM  	= 589,	//相同物品无法进行词缀洗练转移
	//590
	ERR_EQUIP_AFFIXES_TRANS_NOT_EQUIP	= 590,	//非装备物品无法进行词缀洗练转移
	ERR_EQUIP_AFFIXES_TRANS_DIFF_EQUIP_MASK	= 591,	//不同部位的装备无法进行词缀洗练转移
	ERR_EQUIP_AFFIXES_TRANS_WRONG_QUALITY	= 592,	//不能从高品质装备向低品质装备进行词缀洗练转移
	ERR_EQUIP_AFFIXES_TRANS_WRONG_GRADE	= 593,	//不能从高等级装备向低等级装备进行词缀洗练
	ERR_EQUIP_AFFIXES_TRANS_WRONG_INDEX	= 594,	//洗练转移词缀错误
	ERR_EQUIP_AFFIXES_TRANS_SAME_AFFIXES	= 595,	//相同词缀无法转移
	ERR_EQUIP_AFFIXES_TRANS_AFFIXES_NOT_FOUND	= 596,	//没有找到转移的词缀
	ERR_EQUIP_AFFIXES_TRANS_SAME_ADDON_TYPE	= 597,	//词缀转移不能出现相同属性的装备
	ERR_EQUIP_TRANS_LOW_TO_HIGH		= 598,	//词缀转移不能将低属性词缀覆盖高属性词缀
	ERR_EQUIP_AFFIXES_TRANS_LESS_MONEY	= 599,	//词缀转移钱不够
	//600
	ERR_EQUIP_AFFIXES_TRANS_SRC_TOO_LOW	= 600,
	ERR_WORLD_SPEAK_MONEY_LIMIT		= 601,	//狮吼背景 元宝不够
	ERR_EQUIP_SKILL_TRANS_SAME_ITEM		= 602,	//相同物品不能转移特技
	ERR_EQUIP_SKILL_TRANS_NOT_EQUIP		= 603,	//非装备不能转移特技
	ERR_EQUIP_SKILL_TRANS_DIFF_EQUIP_MASK	= 604,	//不是相同部位的装备禁止转移特技
	ERR_EQUIP_SKILL_TRANS_WRONG_GRADE	= 605,	//不能从高等级装备想低等装备上转移特技
	ERR_EQUIP_SKILL_TRANS_SKILL_NOT_FOUND 	= 606,	//转移的特技不存在
	ERR_EQUIP_SKILL_TRANS_WRONG_QUALITY	= 607,	//不能从高品质装备向低品质装备进行特技转移
	ERR_INSTANCE_LUCKY_DRAW_WRONG_INDEX	= 608,	//副本翻牌错误的索引
	ERR_INSTANCE_LUCKY_DRAW_ALREADY_GET	= 609,	//副本翻牌已经领奖过
	//610
	ERR_INSTANCE_LUCKY_DRAW_EMPTY		= 610,	//副本翻牌都已经领光
	ERR_INSTANCE_FORBID_TEAM_FOLLOW     = 611,  //该副本无法组队跟随
	ERR_GUARD_NOT_FOUND					= 612,	//守护灵找不到
	ERR_GUARD_WRONG_ELEMENT				= 613,	//守护灵元素类型不匹配
	ERR_GUARD_EXP_FULL					= 614,	//守护灵经验满
	ERR_GUARD_ILLEGAL_NAME				= 615,	//守护灵名字不合法
	ERR_GUARD_SHAPE_NOT_OWN				= 616,	//守护灵形态未获得
	ERR_GUARD_APTITUDE_LIMIT			= 617,	//守护灵阶段和资质已达上限
	ERR_GUARD_INVALID_SKILL_SLOT		= 618,	//指定的守护灵技能槽位无效
	ERR_GUARD_SKILL_CONFLICTS			= 619,	//守护灵技能冲突
	//620
	ERR_GUARD_SLOT_MAX_LEVEL			= 620,	//守护灵阵法已达最高等级
	ERR_GUARD_SKILL_LEARNED				= 621,	//守护灵技能已经学习
	ERR_ERR_RETRIEVE_POINT_LIMIT			= 622,	//找回奖励双倍点数不足
	ERR_ERR_RETRIEVE_PART				= 623,	//仅找回部分奖励
	ERR_ADVENTURE_WAIT_FINISH			= 624,	//当前还有冒险任务没完成
	ERR_ADVENTURE_NO_CHANCE				= 625,	//骰子用完了
	ERR_FLYSWORD_SURFACE_SWITCH_FLYMODE_FAILED	= 626,	//载具切换飞行状态失败
	ERR_FLYSWORD_SURFACE_TRAIN_LIMIT		= 627,	//座驾进化单项进化数值达到上限
	ERR_FLYSWORD_SURFACE_TRAIN_LESS_ITEM1		= 628,	//座驾进化缺少消耗物品1
	ERR_FLYSWORD_SURFACE_TRAIN_LESS_ITEM2		= 629,	//座驾进化缺少消耗物品2
	//630
	ERR_FLYSWORD_SURFACE_COLOR_ACTIVE_WRONG_PART	= 630,	//座驾喷绘激活颜色错误的部位
	ERR_FLYSWORD_SURFACE_COLOR_ACTIVE_LIMIT		= 631,	//座驾喷绘激活颜色达到当前的上限
	ERR_FLYSWORD_SURFACE_COLOR_ACTIVE_LESS_ITEM	= 632,	//座驾喷绘激活颜色缺少消耗物品
	ERR_FLYSWORD_SURFACE_COLOR_PAINT_WRONG_PART	= 633,	//座驾喷绘染色错误的部位
	ERR_FLYSWORD_SURFACE_COLOR_PAINT_NO_ACTIVE	= 634,	//座驾喷绘染色对应颜色没有被激活
	ERR_FLYSWORD_SURFACE_COLOR_PAINT_LESS_ITEM	= 635,	//座驾喷绘染色缺少消耗物品
	ERR_FLYSWORD_SURFACE_FASHION_ACTIVE_WRONG_PART	= 636,	//座驾改装错误的改装部位
	ERR_FLYSWORD_SURFACE_FASHION_ACTIVE_WRONG_INDEX	= 637,	//座驾改装错误的改装索引
	ERR_FLYSWORD_SURFACE_FASHION_ALREADY_ACTIVE	= 638,	//座驾改装对应部件已经被激活
	ERR_FLYSWORD_SURFACE_FASHION_ACTIVE_LESS_ITEM	= 639,	//座驾改装对应部件已经被激活
	//640
	ERR_FLYSWORD_SURFACE_FASHION_SELECT_WRONG_PART	= 640,	//座驾改装选择部件错误的部位
	ERR_FLYSWORD_SURFACE_FASHION_SELECT_NO_ACTIVE	= 641,	//座驾改装选择部件没有被解锁
	ERR_XYXW_FLYSWORD_UNLOCK_DOUBLE_FLY		= 642,	//相依相偎需要先解锁双人座驾
	ERR_XYXW_IS_FULL				= 643,	//已经在相依相偎中
	ERR_XYXW_CAN_NOT_INVITE_MULTI_FLY		= 644,	//座驾相依相偎状态下无法邀请多人骑乘
	ERR_QQMM_NEED_XYXW				= 645,	//亲亲密密需要相依相偎对象
	ERR_FLYSWORD_SWITCH_SKILL_FAILED		= 646,	//座驾切换炫技技能失败
	ERR_GUARD_SKILL_FIGHT_TYPE			= 647,	//守护灵技能战斗类型不符合
	ERR_EXPIRE_SURFACE_CAN_NOT_OPERATE		= 648,	//限时座驾无法进行该操作
	ERR_ENHANCE_BAODI_NO_NEED			= 649,	//强化不能使用保底符
	//650
	ERR_ADVENTURE_ITEMTYPE_ERR 			= 650,
	ERR_ADVENTURE_MISSION_CONFIG_ID_ERR 		= 652,
	ERR_ADVENTURE_ADD_MISSION_ERR 			= 653,
	ERR_ADVENTURE_INVALID_TASK_GRANT_TID		= 654,

	ERR_SCENE_LIMIT_XYXW				= 655,	//场景禁止相依相偎
	ERR_SCENE_LIMIT_MULTI_FLY			= 656,	//场景禁止多人骑乘
	ERR_SCENE_LIMIT_XYXW_LIMT_JUMP			= 657,	//场景禁止相依相偎，无法进入
	ERR_SCENE_LIMIT_MULTI_FLY_LIMT_JUMP		= 658,	//场景禁止多人骑乘，无法进入
	ERR_XYXW_ALREADY_INVITE				= 659,	//相依相偎已经发出邀请
	//660
	ERR_OTHER_IN_PVP 				= 660,
	ERR_INSTANCE_FIGHTING_CAPACITY_LIMIT 		= 661, //进入副本的战斗力前提不符合
	ERR_XYXW_FIELD_FLY_CONFLICT			= 662,	//地面相依相偎与座驾相依相偎相互冲突
	ERR_CORPS_RECRUITMENT_MANFEST_INVALID		= 663,	//错误的社团招聘宣言内容
	ERR_CORPS_BADGE_INVALID				= 664,	//错误的社团招徽章
	ERR_CORPS_SUPPORT_SIDE_INVALID			= 665,	//错误的社团拥护组织
	ERR_CORPS_INSTANCE_LIMIT_JOIN_TIME      = 666,  //加入时间不足不能进入社团副本
	ERR_COPRS_INSTANCE_LIMIT_INTERN         = 667,  //实习生不能进入社团副本
	ERR_COPRS_INSTANCE_LIMIT_TEMP           = 668,  //休假中不能进入社团副本

	//670
	ERR_RETINUE_FASHION_ALREADY_ACTIVE              = 670,  //伙伴已激活此时装
	ERR_RETINUE_FAHSION_COLOR_NOT_ACTIVE            = 671,  //伙伴还没激活此时装
	ERR_RETINUE_TASK_ALREADY_LIMIT                  = 672,  //伙伴任务超过上限
	ERR_RETINUE_TASK_NUM_NOT_MATCH                  = 673,  //伙伴任务人数不满足
	ERR_RETINUE_TASK_NOT_EXIST                      = 674,  //没有找到此任务
	ERR_RETINUE_TASK_AWARD_FAIL                     = 675,  //获取任务奖励失败
	ERR_RETINUE_SELECT_COMBAT                       = 676,  //战斗状态不能更换主战伙伴
	ERR_RETINUE_PRIVATE_LESS                        = 677,  //私有物数量不足
	ERR_RETINUE_COMPOSE_LESS                        = 678,  //伙伴数目不足无法拆解
	ERR_RETINUE_MAX_QUALITY                         = 679,  //伙伴品阶已升值最大

	//680
	ERR_RETINUE_UPGRADE_ITEM                        = 680,  //升阶所需道具不足
	ERR_RETINUE_ACTIVE_PRIVATE_ALREADY              = 681,  //伙伴已激活过此私有物
	ERR_RETINUE_ACTIVE_PRIVATE_NOT_MATCH            = 682,  //无法激活此私有物
	ERR_RETINUE_PLAYER_FASHION_NOT_ACTIVE           = 683,  //玩家身上还没有激活此件时装
	ERR_FAUCTION_STORE_ALREADY_OPEN					= 684,	//店铺已经开过了
	ERR_FAUCTION_STORE_NOT_OPEN						= 685,	//店铺还没有开
	ERR_FAUCTION_STORE_LEVEL_MAX					= 686,	//店铺等级已到最高
	ERR_FAUCTION_STORE_EXP_LOW						= 687,	//店铺经验不足
	ERR_FAUCTION_STORE_STYLE_LOCK					= 688,	//店铺装修风格未解锁
	ERR_FAUCTION_STORE_FULL							= 689,	//店铺已满

	//690
	ERR_XYXW_FOLLOWER_LIMIT_JUMP					= 690,	//非主绑的相依相偎禁止切地图
	ERR_NEW_PLAYER_SCENE_CLOSE						= 691,	//创建选人场景关闭
	ERR_SKILL_IS_COOLING							= 692,	//技能处于冷却中
	ERR_IRRIGATE_COOLDOWN							= 693,  //许愿冷却中
	ERR_IRRIGATE_NO_COUNT							= 694,  //没有许愿次数
	ERR_IRRIGATE_LESS_MONEY							= 695,  //许愿消耗不足
	ERR_HARVEST_NO_FRUIT							= 696,	//没有果实可以收获
	ERR_FLYSWORD_LIMIT_DIVE							= 697,	//座驾无法潜水
	ERR_SOCIAL_SPACE_LEVEL_LOW						= 698,	//等级不够
	ERR_SOCIAL_SPACE_STEP_ALREADY					= 699,	//已经踩过此空间

	//700
	ERR_SOCIAL_SPACE_STYLE_WRONG					= 700,	//设置装扮失败
	ERR_SOCIAL_SPACE_COMMON							= 701,	//空间平台处理失败
	ERR_SOCIAL_SPACE_COOLDOWN						= 702,
	ERR_SOCIAL_SPACE_FUNC_NOT_OPEN					= 703,
	ERR_SOCIAL_SPACE_EXTEND_ALBUM_NOT_OPEN			= 704,
	ERR_EXTEND_ALBUM_PARAM_ERR						= 705,	//相册扩容参数错误
	ERR_SOCIAL_SPACE_WRONG_PARAM					= 706,  //参数检查错误
	ERR_INSTANCE_LOTTERY_LIMIT						= 707,	//副本翻牌抽奖达到数量限制
	ERR_MAIL_PACK_SUCCESS							= 708,	//邮件赠送物品成功
	ERR_MAIL_PACK_NOT_EXIST							= 709,	//邮件赠送物品不存在

	//710
	ERR_MAIL_PACK_CAN_NOT_GIFT						= 710,	//邮件赠送物品不能被赠送
	ERR_MAIL_PACK_IS_EQUIPMENT						= 711,	//邮件赠送物品是装备
	ERR_MAIL_PACK_COUNT_LIMIT						= 713,	//邮件赠送物品次数限制
	ERR_MAIL_PACK_DB_ERROR							= 714,	//邮件赠送物品db错误
	ERR_MAIL_PACK_TIMEOUT							= 715,	//邮件赠送物品超时
	ERR_INVALID_PET_NAME							= 716,	//非法的宠物名字
	ERR_INVALID_CHILD_NAME							= 717,	//非法的宠物名字
	ERR_INVALID_GUARD_NAME							= 718,	//非法的守护灵名字
	ERR_CORPS_REPU_LIMIT							= 719,	//社团声望没有开启

	//720
	ERR_QQMM_WITHOUT_XYXW							= 720,	//亲亲密密需要相依相偎状态
	ERR_FUNC_CLOSED_TEMPORARY						= 721,  //功能未开启
	ERR_EXTRACT_LONGYU_NOT_EQUIP					= 722,	//非装备不能提取龙语
	ERR_EXTRACT_LONGYU_HAS_NOT						= 723,	//装备没有龙语，不能提取
	ERR_INTERACT_ACTIVE_ALREADY					= 724,  //交互物体已经启动
	ERR_INTERACT_NOT_MAIN						= 725,  //只有主绑才能操作
	ERR_GATHER_WEATHER_LIMIT						= 726,	//采集时候天气错误
	ERR_RETINUE_DONT_NEED_GIFT				= 727,	//该伙伴不能送礼
	ERR_RETINUE_TAKE_GIFT_LEVEL_ERR				= 728,	//无法领取该等级礼物
	ERR_RETINUE_TAKE_GIFT_AMITY_LESS			= 729,	//领取礼物好友度不足

	//730
	ERR_RETINUE_GIFT_LESS							= 730,   //伙伴礼物不足
	ERR_AUCTION_SELL_LEVEL_LIMIT					= 731,	//店铺出售等级不足
	ERR_AUCTION_BUY_LEVEL_LIMIT						= 732,	//店铺购买等级不足
	ERR_FAUCTION_ITEM_NOT_ALLOW						= 733,	//物品不能上架店铺
	ERR_AUCTION_PRICE_NOT_ALLOW						= 734,	//物品上架价格不再合法区间内

	//735
	ERR_TASK_ACTIVITY_FACTOR_LIMIT					= 735,	//活动周期性经验上限
	ERR_WRONG_CORPS_BATTLE_STRATEGY_TYPE			= 736,	//错误的社团指挥策略
	ERR_WRONG_CORPS_BATTLE_STRATEGY_POS				= 737,	//错误的社团指挥策略位置
	ERR_PLAYER_NOT_CORPS_BATTLE_COMMANDER			= 738,	//玩家不是社团战场指挥
	ERR_PLAYER_NOT_IN_CORPS_BATTLE					= 739,	//玩家不在社团战场内

	//740
	ERR_INTERACT_POS_USED							= 740,  //交互物体位置被占用
	ERR_RETINUE_AMITY_LESS							= 741,  //伙伴好友度不足
	ERR_NO_ACTIVITY_FACTOR_EXP						= 742,	//没有活动周期性经验可领取
	ERR_XYXW_INVITE_COOLDOWN						= 743,	//相依相偎邀请冷去中
	ERR_XYXW_INVITE_SUCCESS							= 744,	//相依相偎邀请发送成功

	//745
	ERR_IDIP_FORBID_ITEM_USE						= 745,	//idip禁止物品使用
	ERR_IDIP_FORBID_ENTER_SCENE						= 746,	//idip禁止玩家进入场景
	ERR_IDIP_FORBID_NPC_SHOP						= 747,	//idip禁止npc随身商店
	ERR_IDIP_FORBID_NPC_SHOP_ITEM					= 748,	//idip禁止npc随身商店物品
	ERR_IDIP_FORBID_REWARD							= 749,	//idip禁止奖励模版

	//750
	ERR_IDIP_FORBID_MATTER							= 750,	//idip禁止矿物
	ERR_IDIP_FORBID_RETRIEVE						= 751,	//idip禁止奖励
	ERR_CANNON_INTERACT_FULL						= 752,	//大炮坐不下了
	ERR_TARGET_IN_INTERACT							= 753,	//对方正在交互状态中
	ERR_FLYSWORD_SURFACE_ALREADY_GAIN					= 754,	//已经获取到该座驾

	//755
	ERR_BOOK_TIMEOUT							= 755,	//读书超时
	ERR_FLYSWORD_CANNOT_DIVE						= 756,	//座驾不能潜水
	ERR_ITEM_CAN_BE_USE_ONLY_ONCE						= 757,  //物品只能使用一次
	ERR_INTERACT_WITH_TRANSFORM						= 758,  //变身时不允许交互
	ERR_FLYSWORD_PRODUCE_SURFACE_DUP					= 759,	//已经制造过该座驾幻化
	ERR_FLYSWORD_PRODUCE_SURFACE_NONE					= 760,	//没有找到需要制造得座驾幻化
	ERR_FLYSWORD_PRODUCE_SURFACE_MONEY					= 761,	//制造座驾幻化金钱不足
	ERR_FLYSWORD_PRODUCE_SURFACE_ITEM					= 762,	//制造座驾幻化金钱不足
	ERR_FLYSWORD_MATERIAL_EXIST						= 763,	//座驾特殊喷绘已经存在
	ERR_FLYSWORD_MATERIAL_NOT_SUPPORT					= 764,	//改座驾不支持特殊喷绘
	ERR_FLYSWORD_MATERIAL_ITEM						= 765,	//解锁特殊喷绘缺少物品
	ERR_FLYSWORD_MATERIAL_UNLOCK						= 766,	//特殊喷绘需要解锁
	ERR_FLYSWORD_MATERIAL_NO_SURFACE					= 767,	//特殊喷绘座驾不存在
	ERR_FLYSWORD_MATERIAL_CURRENT						= 768,	//切换的喷绘为当前喷绘
	ERR_FLYSWORD_COLOR_ACTIVE_DUP						= 769,	//重复激活

	//770
	ERR_INVALID_SKILL_NAME							= 770,	//非法的技能组合名
	ERR_INVALID_TALENT_NAME							= 771,	//非法的言灵组合名
	ERR_COLLECTION_ACTIVE_DUP                                               = 772,  //已经永久激活手办
	ERR_INTIMATE_PARTY_APPLY                    = 773, //已经有人在申请派对
	ERR_INTIMATE_PARTY_BEGIN                    = 774, //派对已经开始

	ERR_MINI_GAME_NOT_FOUND				= 775,	//找不到对应的小游戏
	ERR_MAHJONG_NOT_FINISHED			= 776,	//连连看有游戏尚未完成
	ERR_MAHJONG_PARAM_ERROR				= 777,	//连连看参数错误
	ERR_MAHJONG_WRONG_PAIR				= 778,	//连连看不成对
	ERR_MAHJONG_GAME_BREAK				= 779,	//连连看游戏异常结束
	ERR_MAHJONG_NO_GAME					= 780,	//连连看游戏没有开始
	ERR_MAHJONG_TIMEOUT					= 781,	//连连看游戏超时

	ERR_FINDSAME_NOT_TIMEOUT			= 782,	//记忆翻格子 时间已过
	ERR_FINDSAME_NOT_FINISHED			= 783,  //记忆翻格子 游戏还在进行中
	ERR_FINDSAME_PARAM_ERR				= 784,  //记忆翻格子 参数错误

	//785
	ERR_CAREER_ACTIVATE_MAX				= 785,	//身份激活数量达到上限
	ERR_CAREER_ACTIVATE_LEVEL_LOW		= 786,	//身份激活等级不足
	ERR_CAREER_ACTIVATE_ALREADY			= 787,	//身份激活已经激活
	ERR_CAREER_INVALID_TID				= 788,	//无效的身份模板ID
	ERR_CAREER_NOT_ACTIVATED			= 789,	//身份未激活

	//790
	ERR_CAREER_ABILITY_LEVEL_LOW					= 790,	//身份能力等级不足
	ERR_CAREER_LEVEL_MAX							= 791,	//身份已到最高等级
	ERR_CAREER_LEVELUP_BEGIN						= 792,	//身份已经开始晋级
	ERR_COOKBOOK_NOT_LEARNED						= 793,	//食谱没有学会
	ERR_CAREER_ENERGY_LOW							= 794,	//身份精力不足

	//795
	ERR_COOKBOOK_STALL_TIME_MAX						= 795,	//食谱摆摊次数上限
	ERR_COOKBOOK_GIFT_TIME_MAX						= 796,	//食谱赠送次数上限
	ERR_MINIGAME_ONEPEN_PARAM_ERR					= 797,  //一笔画小游戏 参数错误
	ERR_MINIGAME_ONEPEN_SERVER_ERR					= 798,  //一笔画小游戏 服务器错误
	ERR_MINIGAME_ONEPEN_GAME_PROCESSING				= 799,  //一笔画小游戏 游戏进行中

	//800
	ERR_FINDGOODS_TIMEOUT							= 800, //找道具 时间已过
	ERR_FINDGOODS_NOT_FINISHED						= 801, //找道具 游戏还在进行中
	ERR_FINDGOODS_PARAM_ERROR						= 802,	//找道具 参数错误
	ERR_RECEIVEITEM_TIMEOUT							= 803,	//接道具小游戏 时间已过
	ERR_GAME2048_CLOSED								= 804, // 小游戏暂未开启

	//805
	ERR_GAME2048_NO_HISTORY							= 805, // 初始状态无法后退
	ERR_GAME2048_NO_UNDO							= 806, // 回退机会已经用完了
	ERR_CAREER_STAMINA_LOW							= 807,	//身份体力不足
	ERR_CAREER_FREEZE_TIME							= 808, //距离身份冻结时间太短
	ERR_GUARD_PROTO_LOW								= 809,	//守护灵原型过低

	//810
	ERR_GUARD_MAX_PHASE								= 810,	//守护灵已经进化到最高阶段
	ERR_GUARD_GROWTH_LOW							= 811,	//守护灵进化值过低
	ERR_GUARD_SKILL_OPEN							= 812,	//守护灵技能格子已经解锁
	ERR_GUARD_SKILL_NOT_OPEN						= 813,	//守护灵技能格子未解锁
	ERR_GUARD_SKILL_ELEMENT							= 814,	//守护灵技能元素类型不符合

	//815
	ERR_CORPS_RECRUITMENT_URL_INVALID				= 815,  //社团招聘图片非法
	ERR_MINI_GAME_OTHER_PROCESSING					= 816, //有正在进行中的小游戏，无法开启新游戏
	ERR_PRODUCE_PROF_LIMIT							= 817,	//制造职业限制
	ERR_PRODUCE_LEVEL_LIMIT							= 818,	//制造等级限制
	ERR_PRODUCE_SKILL_LIMIT							= 819,	//制造技能限制

	//820
	ERR_PRODUCE_LESS_MONEY							= 820,	//制造钱不够
	ERR_PRODUCE_LESS_REPU							= 821,	//制造声望不够
	ERR_PRODUCE_LESS_MATERIAL						= 822,	//制造消耗品不够
	ERR_PRODUCE_LESS_LUCKY_ITEM						= 823,	//制造幸运剂不够
	ERR_PRODUCE_NO_LUCKY_ITEM						= 824,	//制造不能使用幸运剂

	//825
	ERR_DOLL_WRONG_PARAM							= 825, //娃娃机参数错误
	ERR_DOLL_HAVE_COIN								= 826, //已经投币
	ERR_DOLL_NOT_ENOUGH_COIN						= 827, //币不足
	ERR_DOLL_NO_COIN								= 828, //未投币
	ERR_DOLL_CHECK									= 829, //已经抓过了

	//830
	ERR_DOLL_TIMEOUT								= 830, //时间到
	ERR_DOLL_NO_CHECK								= 831, //还没抓 不能刷新
	ERR_DOLL_BAG_NO_SPACE							= 832, //包裹空间不足
	ERR_AREA_BATTLE_MEMBER_NOT_TOGETHER				= 833, //组队竞技场队员不在一起
	ERR_MINIGAME_NOT_FINISHED						= 834, //小游戏统一错误码 有未完成游戏进行中
	//835
	ERR_ACTIVITY_CLOSED						= 835, //活动未开启
	ERR_STOCK_PARAM							= 836, //股票参数错误
	ERR_STOCK_NO_DATA						= 837, //未找到股票数据
	ERR_STOCK_TIME							= 838, //不在开盘时间
	ERR_STOCK_PRICE							= 839, //股票价格已过期
	//840
	ERR_STOCK_BUY_LIMIT						= 840, //超过购买总限额
	ERR_STOCK_LESS_MONEY						= 841, //钱不够
	ERR_STOCK_LESS_COUNT						= 842, //所持股票不足
	ERR_CHANGE_PROF_WRONG_STATE					= 843, //当前状态不能进行转职
	ERR_CHANGE_PROF_LEVEL_LIMIT					= 844, //等级不足，无法开启职业

	//845
	ERR_COOK_MATERIAL_ITEM_CAREER_CANNOT_BUY    = 845,  //食材物品对应身份才能购买
	ERR_OPEN_PROF_LEVEL_LIMIT					= 846, //等级不足，无法开启职业
	ERR_CHANGE_PROF_NOT_OPEN					= 847, //职业未开启，无法转职
	ERR_CAREER_ITEM_LIST_FULL					= 848,	//身份物品队列已满
	ERR_CAREER_ITEM_NOT_FOUND					= 849,	//身份物品找不到

	//850
	ERR_CAREER_ITEM_NOT_FINISH					= 850,	//身份物品未完成
	ERR_INVALID_AUTO_COMBAT_SET_NAME			= 851,	//非法的辅助战斗方案名
	ERR_HUNT_DRAGON_NOT_LEADER					= 852,	//猎龙活动不是队长
	ERR_HUNT_DRAGON_TEAM_MEMBER_NUM_LIMIT		= 853,	//猎龙活动队伍人数不足
	ERR_HUNT_DRAGON_TEAM_MEMBER_LEVEL_LIMIT		= 854,	//猎龙活动队伍人员等级不足

	//855
	ERR_HUNT_DRAGON_INSTANCE_TID				= 855,	//猎龙活动instance_tid错误
	ERR_FACELIFT_LEVEL_LOW					= 856,	//重塑容貌等级不够
	ERR_FACELIFT_MONEY_LOW					= 857,	//重塑容貌钱不够
	ERR_SOLUTION_OP_IN_COMBAT_STATE			= 858, // 战斗状态不能切换总方案
	ERR_SOLUTION_MOIDFY_DEFAULT				= 859, // 默认配置不能修改

	//860
	ERR_THIS_MOUNT_CANNOT_UNLOCK_TEMPORARY	= 860,	// 该座驾暂时不能解锁
	ERR_THIS_MOUNT_CANNOT_PRODUCE_TEMPORARY	= 861,	// 该座驾暂时不能制造
	ERR_THIS_MOUNT_CANNOT_PAINT_TEMPORARY	= 862,	// 该座驾暂时不能喷涂
	ERR_THIS_MOUNT_CANNOT_ACTIVATE_COLOR_TEMPORARY	= 863,	// 该座驾暂时无法解锁喷涂颜色
	ERR_THIS_MOUNT_CANNOT_SELECT_FASHION	= 864,	// 该座驾暂时不能改装

	// 865
	ERR_THIS_FASHION_CANNOT_UNLOCK_TEMPORARY = 865,	// 该时装暂时不能解锁
	ERR_THIS_FASHION_CANNOT_PAINT_TEMPORARY = 866, // 该时装暂时不能染色
	ERR_THIS_FASHION_CANNOT_ACTIVATE_COLOR_TEMPORARY = 867, // 该时装暂时不能解锁颜色
	ERR_THIS_ITEM_CANNOT_PUT_IN_DEPOSITORY_TEMPORARY = 868, // 该物品暂时不能放入仓库
	ERR_THIS_ITEM_CANNOT_TAKE_OUT_DEPOSITORY_TEMPORARY = 869, // 该物品暂时不能从仓库中取出

	// 870
	ERR_THIS_CAREER_COOKBOOK_CANNOT_USE_TEMPORARY = 870, // 该食谱暂时无法使用
	ERR_THIS_CORPS_BADGE_CANNOT_SET_TEMPORARY = 871, // 该社团徽章暂时不能设置
	ERR_THIS_MOUNT_CANNOT_TRAIN_TEMPORARY = 872, // 该座驾暂时不能进化
	ERR_THIS_CAREER_CANNOT_FREEZE_TEMPORARY = 873, // 该身份暂时不能冻结
	ERR_CAREER_LOW_LEVEL					= 874,	// 身份等级不足

	// 875
	ERR_INSTANCE_LUCK_DRAW_CLOESED		= 875,	//副本翻牌子已经结束
	ERR_INSTANCE_LUCK_DRAW_NO_LOTTERY	= 876,	//副本翻牌子没有对应彩票
	ERR_INSTANCE_LUCK_DRAW_NO_LOTTERY_ITEM	= 877,	//副本翻牌子没有彩票物品
	ERR_CORPS_BATTLE_COMMANDER_FUNC_CLOSE	= 878,	//社团指挥功能暂时未开启
	ERR_GUARD_SLOT_LOCKED = 879, // 宠物槽位尚未解锁

	// 880
	ERR_STAR_FASHION_COUNT_MAX				= 880,	//明星换装次数上限
	ERR_STAR_FASHION_LESS_TIME				= 881,	//明星换装时间未到
	ERR_FLYSWORD_PRODUCE_SURFACE_LEVEL			= 882,	//未达到制造座驾幻化所需要的等级
	ERR_CAREER_STAR_AGENT_HIRE_CD   = 883,  //雇佣经纪人正在冷却中
	ERR_CAREER_STAR_AGENT_HIRE_EXIST = 884, //已经有经纪人了
	ERR_CAREER_STAR_AGENT_HIRE_CFG  = 885,  //没有这个经纪人
	ERR_CAREER_STAR_AGENT_HIRE_LEVEL = 886, //明星等级不足，经纪人未解锁
	ERR_CAREER_STAR_AGENT_HIRE_RETINUE = 887,       //伙伴亲密度不足，经纪人未解锁
	ERR_CAREER_STAR_AGENT_HIRE_TASK = 888,  //前置任务未完成，经纪人未解锁
	ERR_FLYSWORD_PRODUCE_SURFACE_SCORE			= 889,	//未达到制造座驾幻化所需要的角色评分

	// 890
	ERR_PURE_BLOODED_INVALID_TALENT_INDEX		= 890,	//纯血提纯，错误的血装天赋方案索引
	ERR_PURE_BLOODED_INVALID_TALENT_TID		= 891,	//纯血提纯，错误的血装天赋TID
	ERR_PURE_BLOODED_INVALID_TALENT_PROF		= 892,	//纯血提纯，错误的血装天赋职业
	ERR_PURE_BLOODED_INVALID_TALENT_STAGE		= 893,	//纯血提纯，错误的血装天赋层级
	ERR_PURE_BLOODED_LESS_TALENT_STAR		= 894,	//纯血提纯，错误的血统天赋点数
	ERR_PURE_BLOODED_NEED_RELY_TALENT		= 895,	//纯血提纯，需要依赖的天赋完成
	ERR_PURE_BLOODED_TALENT_STAR_LIMIT		= 896,	//纯血提纯，天赋点数达到上限
	ERR_PURE_BLOODED_EQUIP_LEVEL			= 897,	//纯血提纯，玩家未达到天赋所需的等级
	ERR_PURE_BLOODED_INVALID_EQUIP_TYPE		= 898,	//纯血提纯，错误的装备类型
	ERR_PURE_BLOODED_INVALID_EQUIP_POS		= 899,	//纯血提纯，错误的装备位置

	// 900
	ERR_PURE_BLOODED_INVALID_EQUIP_LEVEL	= 900,	//纯血提纯，无法用低等级的装备覆盖高等级的装备
	ERR_QUESTION_GAME_OVER					= 901,	//智慧问答 游戏已经结束
	ERR_QUESTION_HAS_ANSWER					= 902,	//智慧问答 本题已答过
	ERR_QUESTION_HAS_NOT_ANSWER				= 903,	//智慧问答 本题还未答
	ERR_QUESTION_INVALID_PARAM				= 904,	//智慧问答 参数错误
	ERR_SIMPLIFIED_CLIENT_RESOURCE_WITHOUT_DOWNLOAD = 905,  //小包资源未下载
	ERR_FORBID_INSTANCE						= 906,	//临时禁止进入副本
	ERR_STAR_FASHION_FORBID_TIME			= 907,	//明星换装当前时间禁止
	ERR_AUCTION_NOT_MAINROLE				= 908,	//只有大号才能拍卖行购买
	ERR_ENTER_TEAM_STABLE					= 909,	//5分钟内无人员变更，队伍进入稳定状态

	// 910
	ERR_LEAVE_TEAM_STABLE					= 910,	//有队员变更，队伍暂时离开稳定状态
	ERR_INTERACT_SUB_OPERAION               = 911,  //交互子操作出错，客户端会发起退出交互
	ERR_NEW_SECT_APPLY_OP_TIMEOUT		= 912,	//拜师操作超时
	ERR_NEW_SECT_APPLY_INVAILD_TITLE	= 913,	//
	ERR_NEW_SECT_APPLY_ALREADY_CONFIRM	= 914,	//
	ERR_NEW_SECT_APPLY_INVALID_TARGET	= 914,	//
	ERR_NEW_SECT_APPLY_NO_TITLE		= 915,	//
	ERR_TOP_STAR_ACTIVITY_CLOSE				= 916,	//人气巨星 活动关闭
	ERR_TOP_STAR_AWARD_TAKEN				= 917,	//人气巨星 奖励已领
	ERR_TOP_STAR_LESS_CHEER					= 918,	//人气巨星 应援不足
	ERR_ITEM_CANNOT_TRADE_TEMPORARY = 919, // 该商品已经暂时被禁止交易
	ERR_CHANGE_BODY_LEVEL_LIMIT		= 920,	// 等级不足无法转换性别、体型
	ERR_CHANGE_BODY_NO_CHANGE		= 921,	// 性别和体型都没有改变
	ERR_LADDER_ACTIVITY_CLOSED				= 922,  // 屠龙考核活动未开启
	ERR_ENTER_HOMETOWN_LEVEL_NOT_MATCH     = 923, // 进入家园等级不够
	ERR_CHANGE_GENDER_MARRIED              = 924, // 结婚后不能性别转换

	ERR_SSP_VOTE_WRONG_TIME			= 929,	//不在朋友圈投票时间

	// 930
	ERR_CAREER_SHOP_LESS_POPULAR		= 930,	//小店身份：人气不足
	ERR_CAREER_SHOP_IS_OPEN				= 931,	//小店身份：开店时不能操作
	ERR_CAREER_SHOP_INVALID_REQUEST		= 932,	//小店身份：错误的请求
	ERR_CAREER_SHOP_LESS_STAGE			= 933,	//小店身份：规模不足
	ERR_CAREER_SHOP_LESS_FOUND			= 934,	//小店身份：资金不足
	ERR_CAREER_SHOP_EMPLOYEE_FULL		= 935,	//小店身份：店员已满
	ERR_CAREER_SHOP_EMPLOYEE_LEVEL		= 936,	//小店身份：店员等级过高
	ERR_CAREER_SHOP_EMPLOYEE_TRAINING	= 937,	//小店身份：店员培训中
	ERR_CAREER_SHOP_TRAIN_COUNT			= 938,	//小店身份：店员培训次数已满
	ERR_CAREER_SHOP_EMPLOYEE_PROP_MAX	= 939,	//小店身份：店员属性已达上限
	ERR_CAREER_SHOP_NOT_OPEN_TIME		= 940,	//小店身份：当前时间不能开店
	ERR_CAREER_SHOP_TODAY_OPEN			= 941,	//小店身份：今天已经开过店
	ERR_CAREER_SHOP_INVALID_NAME		= 942,	//小店身份：无效的名字
	ERR_CAREER_SHOP_INVALID_SLOGAN		= 943,	//小店身份：无效的宣传语
	ERR_CAREER_SHOP_ACTIVITY_MAX		= 944,	//小店身份：店员积极性已达上限
	ERR_CAREER_SHOP_ITEM_BUYOUT			= 945,	//小店身份：商品已经卖出
	ERR_CAREER_SHOP_NO_FREE_COUNT		= 946,	//小店身份：免单券已用完
	ERR_CAREER_SHOP_NO_SHOP_FREE_COUNT	= 947,	//小店身份：小店免单券已用完
	ERR_CAREER_SHOP_NO_PRODUCE_COUNT	= 948,	//小店身份：制作次数已用完
	ERR_CAREER_SHOP_HAS_BUY_FROM_OWNER	= 949,	//小店身份：今天在这个小店购买过
	ERR_CAREER_SHOP_BINDCASH_WITHDRAW	= 950,	//小店身份：分红已领取
	ERR_CAREER_SHOP_IS_CLOSE			= 951,	//小店身份：关店时不能操作
	ERR_CAREER_ABILITY_LOCKED			= 952,	//身份：能力未解锁
	ERR_CAREER_SHOP_PLAYER_LEVEL		= 953,	//小店身份：角色等级不足
	ERR_CAREER_SHOP_NO_SPECIAL_EVENT	= 954,	//小店身份：没有特殊事件
	ERR_CAREER_SHOP_EMPLOYEE_WORKING	= 955,	//小店身份：店员已经被雇佣
	ERR_CAREER_LESS_MASTER				= 956,	//身份：熟练度不足
	ERR_CAREER_DELIVERY_TASK_FAIL		= 957,	//小店身份：任务接取失败
	ERR_CAREER_SHOP_NOT_FRIEND			= 958,	//小店身份：不是好友
	ERR_CAREER_SHOP_NPC_COOKING      	= 959,	//小店身份：厨师正在烹饪中
	ERR_CAREER_SHOP_FULL				= 960,	//小店身份：小店人数已满
	ERR_CAREER_SHOP_PARTNER_FULL		= 961,	//小店合伙人：合伙数量达上限

	ERR_HOME_WELCOME_TIP_ERR = 970, // 家园欢迎语异常
	ERR_HOME_PARTY_INVITE_CAN_ENTER = 971,   // 仅邀请人可进入

	ERR_CLOUD_TRUST_LOGIN_IN_PLAYER		= 980,	//云托管用户

	// 雕塑小游戏错误码
	ERR_MINIGAME_SCULPTURE_INVALID_OP = 1000, // 无效的操作
	ERR_MINIGAME_SCULPTURE_INVALID_SCULPTURE = 1001, // 无效的雕像
	ERR_MINIGAME_SCULPTURE_INVALID_BURIN = 1002, // 无效的刻刀
	ERR_MINIGAME_SCULPTURE_INVALID_PIECE = 1003, // 无效的块
	ERR_MINIGAME_SCULPTURE_BURIN_MORE_STRENGTH = 1004, // 刻刀强度过高
	ERR_MINIGAME_SCULPTURE_BURIN_LESS_STRENGTH = 1005, // 刻刀强度过低
	ERR_MINIGAME_SCULPTURE_FAIL = 1006, // 游戏失败
	ERR_MINIGAME_SCULPTURE_ARTICLE_A = 1007, // 一等作品
	ERR_MINIGAME_SCULPTURE_ARTICLE_B = 1008, // 二等作品
	ERR_MINIGAME_SCULPTURE_ARTICLE_C = 1009, // 三等作品

	// 1010
	ERR_NOT_CHANGE_LINE_INTIMATE_PARTY = 1010, //羁绊派对进行中主人不能离开
	ERR_CAR_RACE_PLAYER_OVER_SPEED = 1011, // 玩家超速 怀疑作弊
	ERR_ITEM_USE_CD = 1012, // 道具使用冷却中，请稍候再试
	ERR_TICKET_COMMON	= 1013,//彩票抽奖通用错误，lua返回
	ERR_NPC_SHOP_ITEM_ACTIVITY_CLOSED	= 1014,//商店售卖物品活动关闭
	ERR_PRODUCE_ITEM_SACRED_NUCLEUS_TARGET_ITEM_TID = 1015,//生产物品圣核目标产物错误
	ERR_HERALDICAE_NOT_FOUNT			= 1016,//没有对应纹章石的配置
	ERR_HERALDICAE_LESS_ITEM			= 1017,//没有对应纹章石的物品
	ERR_HERALDICAE_PLAYER_LEVEL			= 1018,//玩家等级不足，无法使用对应的纹章石
	ERR_HERALDICAE_LEVEL_NOT_COVER			= 1019,//低等级的纹章石无法覆盖高等级纹章石
	ERR_HERALDICAE_QUALITY_NOT_COVER		= 1020,//低品质的纹章石无法覆盖高品质的纹章石

	ERR_HOMETOWN_HAS_OWNER	= 1023,	//家园已经有主人了
	ERR_PUBG_PICK_LOW_BAG   			= 1024,//吃鸡，不能捡低级背包
	ERR_DANCE_TOGETHER_NOT_LEADER					= 1025,	//共舞情缘不是队长
	ERR_DANCE_TOGETHER_TEAM_MEMBER_NUM_LIMIT		= 1026,	//共舞情缘队伍人数不是两人
	ERR_DANCE_TOGETHER_EARLY_END					= 1027, //共舞情缘提前结束
	ERR_ACTIVITY_EFFIGY_LESS_COUNT					= 1028,	//活动雕像物品不够
	ERR_ACTIVITY_EFFIGY_TASK_FAIL					= 1029,	//活动雕像任务发放失败
	ERR_ACTIVITY_EFFIGY_ALREADY_HAS_FULL			= 1030,	//活动雕像已经满了
	ERR_AREA_MINIGAME_WRONG_SCENE					= 1031, // 当前地图不能匹配小游戏对战
	ERR_AREA_MINIGAME_START_FAIL					= 1032, // 开启对战失败
	ERR_DANCE_TOGETHER_INVITE_SUCCESS				= 1033, //共舞情缘邀请成功
	ERR_DANCE_TOGETHER_ACTIVITY_CLOSE				= 1034, //共舞情缘活动已关闭
	ERR_CORPS_BOSS_WEEK_LIMIT                       = 1035, // 社团BOSS周限制

	ERR_HOME_MISS_PAYMENT = 1040, // 家园欠费

	ERR_GUARD_NOT_MARRY								= 1050,	//守护灵没有结伴
	ERR_GUARD_BREED_ALREADY							= 1051,	//守护灵今日已经培育
	ERR_GUARD_BREED_TIMEOUT							= 1052,	//守护灵培育周期已过
	ERR_GUARD_BREED_NOT_FINISH						= 1053,	//守护灵培育周期还未完成
	ERR_GUARD_MARRY_LEVEL_LOW						= 1054,	//守护灵结婚等级不足
	ERR_GUARD_MARRY_NOT_FRIEND						= 1055,	//守护灵结婚双方不是好友
	ERR_GUARD_MARRY_SPOUSE_TOO_FAR					= 1056,	//守护灵结婚距离过远
	ERR_GUARD_MARRY_NOT_READY						= 1057,	//守护灵结婚尚未开始
	ERR_GUARD_MARRY_CANNOT_MARRY					= 1058,	//守护灵结婚所选的守护灵不能结婚
	ERR_GUARD_MARRY_WRONG_STATE						= 1059, //守护灵结婚状态错误
	ERR_GUARD_MARRY_DIFF_ID							= 1060,	//守护灵结婚不同ID
	ERR_GUARD_MARRY_GUARD_NOTFOUND					= 1061, //守护灵结婚守护灵找不到
	ERR_GUARD_MARRY_SKIN_LOCK						= 1062, //守护灵皮肤未解锁
	ERR_GUARD_MARRY_GFX_LOCK                                                = 1063, //守护灵特效未解锁
	ERR_MORA_INVITE_TARGET_NOT_FOUND                        		= 1064, //猜拳邀请未找到target
	ERR_MORA_TARGET_IN_MORA							= 1065,	//对方已经在猜拳中
	ERR_MORA_WRONG_REPLY_TARGET						= 1066,	//错误的猜拳回复对象
	ERR_MORA_REPLY_TARGET_NOT_FOUND					= 1067,	//猜拳回复对象未找到
	ERR_MORA_SELF_IN_MORA							= 1068,	//自己已经处于猜拳中
	ERR_MORA_TARGET_NOT_IN_INTERACT					= 1069,	//对方不在交互中

	ERR_CAREER_PRODUCE_ENERGY_LOW					= 1070,	//身份制造精力不足
	ERR_CAREER_PRODUCE_STAMINA_LOW					= 1071,	//身份制造体力不足
	ERR_EQUIP_SUIT_ALREADY_UNLOCK                                   = 1072, //该套装已经被激活
	ERR_EQUIP_SUIT_NO_CONFIG                                        = 1073,	//没有对应的套装配置
	ERR_EQUIP_SUIT_LESS_SYNTHESIS_ITEM                              = 1074,	//缺少套装合成所需物品
	ERR_EQUIP_SUIT_UNLOCK_NOT_EQUIP					= 1075,	//非装备不能解锁套装
	ERR_EQUIP_SUIT_UNLOCK_HAS_NOT_SUIT				= 1076,	//解锁套装，该装备没有套装
	ERR_EQUIP_SUIT_UNLOCK_ALREADY_EXTRA				= 1077,	//解锁套装，该套装已经被提取
	ERR_EXTRACT_SUIT_NOT_EQUIP					= 1078,	//非装备无法提取套装
	ERR_EXTRACT_SUIT_HAS_NOT					= 1079,	//装备没有套装
	ERR_EXTRACT_SUIT_ALREADY_EXTRACT				= 1080,	//装备套装已经被提起

	ERR_SOUL_SEND_ITEM_COUNT_LIMIT                                  = 1081, //灵魂伴侣赠送限次
	ERR_SOUL_GET_ITEM_COUNT_LIMIT                                   = 1082, //灵魂伴侣获取物品限次
	ERR_SOUL_INVALID_ITEM                                           = 1083, //灵魂伴侣物品不可赠送

	ERR_PUBG_BEEN_RESCUE						= 1084, //吃鸡，目标被人救了
	ERR_LONGYU_UPGRADE_PLAYER_LEVEL					= 1085,	//龙语晋级，玩家等级不足
	ERR_LONGYU_UPGRADE_NOT_GOLDEN					= 1086,	//非金色龙语不能晋级
	ERR_LONGYU_UPGRADE_ALREADY_UPGRADE				= 1087,	//该方向龙语已经晋级
	ERR_LONGYU_UPGRADE_WRONG_DST					= 1088,	//龙语晋级错误的晋级方向
	ERR_LONGYU_UPGRADE_ITEM_LESS					= 1089,	//龙语晋级缺少物品
	ERR_LONGYU_UPGRADE_ITEM_GOLDEN_LONGYU				= 1090,	//晋升龙语装备不含金龙语

	ERR_CAREER_FREEZE_TASK							= 1091,	//已经领取了晋级任务，不能冻结身份

	ERR_CHILD_TALENT_MAX_LIMIT						= 1092, //该专长已达上限
	ERR_CHILD_DESTINY_WRONG_LEVEL					= 1093, // 天命层数错误
	ERR_CHILD_DESTINY_UNLOCKED						= 1094, // 天命水晶已解锁
	ERR_CHILD_DESTINY_NOT_ENOUGH_POINT				= 1095, // 天命点数不足
	ERR_CHILD_NO_DEVICE_SLOT						= 1096, // 同步装置槽位未解锁
	ERR_CHILD_DEVICE_WRONG_COLOR					= 1097, // 同步装置槽位和装置不匹配
	ERR_CHILD_SKILL_LEARN_FAIL						= 1098, // 继承者技能学习失败

	ERR_PARADING_WITH_TRANSFORM					= 1100,	//变身状态禁止巡游

	ERR_CHILD_HAS_UNFINISH_TASK					= 1101, //继承者身上有任务未完成
	ERR_CHILD_IN_BATTLE						= 1102, //继承者在出战状态
	ERR_UNLOCK_CHILD_DESTINY_NOT_ALL				= 1103, //解锁天命整层所需点数不够
	ERR_CHILD_DIVORCE_BUSY						= 1104,	//孩子离婚锁定中
	ERR_CHILD_MARRY_CD						= 1105,	//孩子结婚冷却中

	ERR_EDIT_DIAOXIANG_COOLDOWN                 = 1106, //编辑雕像冷却中
	ERR_OVERCOOK_LEVEL_LOCK						= 1107,	//前置关卡未解锁
	ERR_QIANHE_CENTER_BATTLE_NOT_OPEN				= 1108, //千鹤跨服活动未开启
	ERR_QIANHE_CENTER_BATTLE_NO_TEAM				= 1109,	//千鹤跨服未组队
	ERR_QIANHE_CENTER_BATTLE_TIME_LIMIT				= 1110,	//千鹤跨服剩余时间不足
	ERR_QIANHE_CENTER_BATTLE_ITEM_LIMIT				= 1111, //千鹤跨服道具不足
	ERR_EVA_CAN_NOT_TEAM_FOLLOW                     = 1112, //EVA座驾不能组队跟随
	ERR_HARMONIOUS_TEAMMEMBER                       = 1113, //良缘请求人数不对
	ERR_HARMONIOUS_SELF_MONEY_LOW                       = 1114, //良缘操作金钱不够
	ERR_HARMONIOUS_DISTANCE_FAR                     = 1116, //良缘对象距离过远
	ERR_HARMONIOUS_OBJECT_MONEY_LOW                     = 1117, //良缘操作对象金钱不足
	ERR_HARMONIOUS_OBJECT_MARRY                     = 1118,//良缘对象已婚
	ERR_HARMONIOUS_SELF_MARRY                       = 1119, //良缘 自己已婚
	ERR_HARMONIOUS_NOT_CAPTAIN                      = 1120, //请求者非队长
	ERR_HARMONIOUS_OBJECT_UNMARRIED                     = 1121,//良缘对象未婚婚
	ERR_HARMONIOUS_SELF_UNMARRIED                       = 1122,//良缘自己未婚

	ERR_MINIGAME_SPELLING_ACTIVITY_PRETASK_ERR      = 1123, // 拼字小游戏相关活动或前置任务未开启
	ERR_MINIGAME_SPELLING_REAPEATED_ERR             = 1124, // 拼字小游戏玩家正确作答并再次打开小游戏错误码回复
	ERR_MINIGAME_SPELLING_DATA_ERR                  = 1125, // 客户端发送数据不合法
	ERR_MINIGAME_SPELLING_IS_NOT_FINISHED_ERR       = 1126, // 零点时小游戏还未真正结束，需重新打开游戏
	ERR_MINIGAME_SPELLING_MINIGAMECFG_ERR           = 1127, // 策划配置错误

	ERR_HARMONIOUS_SAME_GENDER           			= 1128,   //良缘性别相同
	ERR_CENTER_ARENA_TEAM_TICKET_NOT_EXIST			= 1129,   //跨服爆破行动邀请函不存在
	ERR_PURE_BLOODED_INVALID_SPECIAL_TALENT		    = 1130,	    //天赋ID不是特殊通用天赋
	ERR_PURE_BLOODED_LESS_BRANCH_TALENT		        = 1131,	    //特殊通用天赋当前分支天赋数量不足

	ERR_CONTRACT_BUY_REPLY_TIMEOUT                  = 1140,     //契约家园买房，对方回复超时
	ERR_CONTRACT_BUY_INVALID_TARGET                 = 1141,     //锲约家园买房，回复数据错误
	ERR_CONTRACT_BUY_REPLY_REFUSE                   = 1142,     //锲约家园买房，对方拒绝
	ERR_CONTRACT_SELL_REPLY_TIMEOUT                 = 1143,     //契约家园卖房，对方回复超时
	ERR_CONTRACT_SELL_INVALID_TARGET                = 1144,     //锲约家园卖房，回复数据错误
	ERR_CONTRACT_SELL_REPLY_REFUSE                  = 1145,     //锲约家园卖房，对方拒绝
	ERR_CONTRACT_RENEW_REPLY_TIMEOUT                = 1146,     //契约家园恢复，对方回复超时
	ERR_CONTRACT_RENEW_INVALID_TARGET               = 1147,     //锲约家园恢复，回复数据错误
	ERR_CONTRACT_RENEW_REPLY_REFUSE                 = 1148,     //锲约家园恢复，对方拒绝


	ERR_DRAGONBORN_INVALID_ITEM                   = 1150,     //龙裔未找到
	ERR_DRAGONBORN_COMMON_ERR                     = 1151,     //龙裔一般错误
	ERR_DRAGONBORN_BREAK_ITEM_ERR                 = 1152,     //龙裔突破材料错误
	ERR_DRAGONBORN_BREAK_LEVEL_LIMIT              = 1153,     //龙裔突破次数上限
	ERR_DRAGONBORN_BREAK_NEED_NUM                 = 1154,     //龙裔突破所需数量不足
	ERR_DRAGONBORN_EVOLUTION_LEVEL_LIMIT          = 1155,     //龙裔进化次数上限
	ERR_DRAGONBORN_TRAIN_EXEC_ERR                 = 1156,     //龙裔培养执行错误
	ERR_DRAGONBORN_LEVEL_LIMIT                    = 1157,     //龙裔等级到达上限
	ERR_DRAGONBORN_RECRUIT_NOT_OPEN               = 1158,     //龙裔召唤活动未开
	ERR_DRAGONBORN_RECRUIT_ERR                    = 1159,     //龙裔召唤错误
	ERR_DRAGONBORN_LEARN_SKILL_EXSIT              = 1160,     //龙裔技能学习重复
	ERR_DRAGONBORN_LEARN_SKILL_ERR                = 1161,     //龙裔技能学习失败
	ERR_DRAGONBORN_BEDGE_FULL                     = 1162,     //龙裔列表空间不足

	ERR_PLAYER_MINE_HOLD                        = 1163,    //队伍或者人身上已经有相同的矿了
	ERR_COMMON_USE_LIMIT						= 1164,		// 通用限次
	ERR_MAP_PERSON_FULL							= 1165,		//地图人数过多
	ERR_THUNDERSTRIKE_INDEX_ERR					= 1166,		//天谴计划矿物buff索引范围错误
	ERR_BOUQUET_HAS_COLLECTED					= 1167,		//花束已经被收藏了
	ERR_BOUQUET_CANNOT_USE2TARGET				= 1168,		//花束已经被收藏了
	ERR_BOUQUET_NOT_ENOUGH						= 1169,		//花束不足
	ERR_BOUQUET_BLESS_WORDS_INVALID				= 1170,		//花束祝福包含敏感字
	ERR_LONGHUN_UPGRADE_PLAYER_LEVEL					= 1171,	//龙语晋级，玩家等级不足
	ERR_LONGHUN_UPGRADE_NOT_GOLDEN					= 1172,	//非金色龙语不能晋级
	ERR_LONGHUN_UPGRADE_ALREADY_UPGRADE				= 1173,	//该方向龙语已经晋级
	ERR_LONGHUN_UPGRADE_WRONG_DST					= 1174,	//龙语晋级错误的晋级方向
	ERR_LONGHUN_UPGRADE_ITEM_LESS					= 1175,	//龙语晋级缺少物品

	ERR_RECHAGE_ACT_NOT_FOUND                       = 1176, //付费动作未发现
	ERR_RECHAGE_ACT_UNLOCK                          = 1177,//付费动作已解锁

	ERR_ROAM_COMMUNITY_NOT_JOIN						= 1178,//当前不在社团内
	ERR_ROAM_COMMUNITY_GET_AWARD_FAILED				= 1179,//领取错误
	ERR_ROAM_COMMUNITY_HAS_ALREADY_GET				= 1180,//已领取
	ERR_ROAM_COMMUNITY_ACTIVE_NOT_ENOUGH			= 1181,//活性不足
	ERR_LONGHUN_UPGRADE_CANNOT_USE_COMMON_ITEM		= 1182,	//该龙语晋级不能使用通用物品

	ERR_MOUNT_NONE_EFFECT_TEMPORARY					= 1183,	// 该座驾暂未开放焕彩
	ERR_EFFECT_UPGRADE_LACK_ITEM					= 1184, // 座驾焕彩升级缺乏材料
	ERR_EFFECT_UNLOCK_LEVEL_UNQUALIFIED				= 1185,	// 座驾焕彩解锁阶级不足
	ERR_EFFECT_UNLOCK_LACK_ITEM						= 1186, // 座驾焕彩解锁缺乏材料
	ERR_EFFECT_UPGRADE_COUNT_ERROR					= 1187, // 座驾焕纹解析次数错误
	ERR_EFFECT_UPGRADE_COST_CONFIG_ERROR			= 1188, // 座驾焕纹解析消耗配置错误
	ERR_EFFECT_UPGRADE_SERVER_ERROR					= 1189, // 座驾焕纹解析服务器错误
	ERR_HOMETOWN_GIFT_MSG_ERR						= 1190, // 家园礼物寄语包含敏感词
	ERR_DYNAMIC_GIFT_MSG_ERR						= 1191, // 朋友圈礼物寄语包含敏感词


	ERR_PLAYER_FUNC_FORBID_MODIFY					= 1200,	// ace合规修改：idip禁止修改
	ERR_ENTER_INSTANCE_IN_COMBAT					= 1201,	// 战斗状态下不允许进入副本

	// 龙语方案
	ERR_LONGYU_SOLUTION_INVALID_INDEX				= 1210,	// 无效的方案索引
	ERR_LONGYU_SOLUTION_ALREADY_UNLOCK				= 1211,	// 方案已解锁
	ERR_LONGYU_SOLUTION_MONEY_NOT_ENOUGH			= 1212, // 货币不足
	ERR_LONGYU_SOLUTION_SAME_INDEX					= 1213, // 方案索引相同
	ERR_LONGYU_SOLUTION_LOCK						= 1214, // 方案未解锁
	ERR_LONGYU_SOLUTION_LONGYU_LOCK					= 1215,	// 龙语未解锁
	ERR_LONGYU_SOLUTION_INVALID_NAME				= 1216,	// 非法方案名称
	ERR_LONGYU_SOLUTION_PASSIVE_LONGYU				= 1217,	// 被动龙语
	ERR_LONGYU_SOLUTION_COMBAT_STATE				= 1218,	// 战斗状态

	// 伙伴方案
	ERR_RETINUE_SOLUTION_INVALID_INDEX				= 1220,	// 无效的方案索引
	ERR_RETINUE_SOLUTION_ALREADY_UNLOCK				= 1221,	// 方案已解锁
	ERR_RETINUE_SOLUTION_MONEY_NOT_ENOUGH			= 1222, // 货币不足
	ERR_RETINUE_SOLUTION_SAME_INDEX					= 1223, // 方案索引相同
	ERR_RETINUE_SOLUTION_LOCK						= 1224, // 方案未解锁
	ERR_RETINUE_SOLUTION_INDEX_LOCK					= 1225,	// 位置未解锁
	ERR_RETINUE_SOLUTION_INVALID_NAME				= 1226,	// 非法方案名称
	ERR_RETINUE_SOLUTION_COMBAT_STATE				= 1227,	// 战斗状态
	ERR_RETINUE_SOLUTION_RETINUE_NOT_COLLECTED		= 1228,	// 伙伴未解锁

	ERR_SELF_SELECT_REWARD_INVALID_TYPE					= 1231,	// 无效抽奖类型
	ERR_SELF_SELECT_REWARD_ACTIVITY_NOT_OPEN			= 1232,	// 活动未开启
	ERR_SELF_SELECT_REWARD_NOT_SELECT_REWARD			= 1233,	// 未选择自选奖励
	ERR_SELF_SELECT_REWARD_SLOT_NOT_ENOUGH 				= 1234,	// 背包空间不足
	ERR_SELF_SELECT_REWARD_EPALCE_CURRENCY_NOT_ENOUGH	= 1235,	// 代币不足
	ERR_SELF_SELECT_REWARD_CASH_NOT_ENOUGH				= 1236,	// 点券不足
	ERR_SELF_SELECT_REWARD_LEFT_ITEM_NOT_ENOUGH			= 1237,	// 奖池剩余物品不足
	ERR_SELF_SELECT_REWARD_ERROR						= 1238,	// 服务器内部出现数据错误或者策划配置错误，一般不会发生
	ERR_SELF_SELECT_REWARD_RESET_TIMES_LIMIT			= 1239,	// 重置次数超上限
	ERR_SELF_SELECT_REWARD_NO_SELECT_REWARD				= 1240,	// 奖池没有自选奖励
	ERR_SELF_SELECT_REWARD_NEED_SELECT_ALL				= 1241,	// 自选奖励数量不对，需要全部发过来
	ERR_SELF_SELECT_REWARD_NOT_ALLOW_RESELECT			= 1242,	// 已经抽取到的自选奖励不允许修改
	ERR_SELF_SELECT_REWARD_INVALID_ITEM_ID				= 1243,	// 自选奖励配置里面没有找到物品id
	ERR_SELF_SELECT_REWARD_DRAW_POINT_INVALID_INDEX		= 1244,	// 非法总抽奖励索引
	ERR_SELF_SELECT_REWARD_DRAW_POINT_NOT_ENOUGH		= 1245,	// 总抽奖励积分不足
	ERR_SELF_SELECT_REWARD_DRAW_POINT_HAS_GET			= 1246,	// 已领取该总抽奖励
	ERR_SELF_SELECT_REWARD_ACTIVITY_POINT_INVALID_INDEX	= 1247,	// 非法活跃值奖励索引
	ERR_SELF_SELECT_REWARD_ACTIVITY_POINT_NOT_ENOUGH	= 1248,	// 活跃值奖励积分不足
	ERR_SELF_SELECT_REWARD_ACTIVITY_POINT_HAS_GET		= 1249,	// 已领取该活跃值奖励
	ERR_SELF_SELECT_REWARD_SELECT_NUM_LIMIT				= 1250,	// 自选奖励数量不对，超过上限
	ERR_SELF_SELECT_REWARD_HAS_NO_SELECT				= 1251,	// 自选奖励数量不对，有未选择的
	ERR_SELF_SELECT_REWARD_DICE_ITEM_NOT_ENOUGH			= 1252,	// 骰子物品不足
	ERR_SELF_SELECT_REWARD_INDEX_REWARD_HAS_GET			= 1253,	// 该位置奖励已获得
	ERR_SELF_SELECT_REWARD_CAN_ENTER_NEXT				= 1255,	// 无法进入下层
	ERR_SELF_SELECT_REWARD_ALREAD_MAX_LEVEL				= 1256,	// 已达到最大层
	ERR_SELF_SELECT_REWARD_SELECT_ITEM_REPEATED			= 1257,	// 自选奖励重复

	ERR_SPECTATE_INTERACT_MATTER_LIMIT					= 1258,	// 观战模式禁止交互矿物

	ERR_PURE_BLOODED_HAVE_TWIN_PRUEBLOOD_TALENT			= 1259,	//纯血提纯，有互斥的纯血天赋存在
	ERR_PURE_BLOODED_HAVE_NOT_BASE_TALENT				= 1260,	//纯血提纯，没有前置的基础天赋

	// 龙魂方案
	ERR_LONGHUN_SOLUTION_INVALID_INDEX					= 1270,	// 无效的方案索引
	ERR_LONGHUN_SOLUTION_ALREADY_UNLOCK					= 1271,	// 方案已解锁
	ERR_LONGHUN_SOLUTION_MONEY_NOT_ENOUGH				= 1272, // 货币不足
	ERR_LONGHUN_SOLUTION_SAME_INDEX						= 1273, // 方案索引相同
	ERR_LONGHUN_SOLUTION_LOCK							= 1274, // 方案未解锁
	ERR_LONGHUN_SOLUTION_LONGHUN_LOCK					= 1275,	// 龙魂未解锁
	ERR_LONGHUN_SOLUTION_INVALID_NAME					= 1276,	// 非法方案名称
	ERR_LONGHUN_SOLUTION_PASSIVE_LONGYU					= 1277,	// 被动龙魂
	ERR_LONGHUN_SOLUTION_COMBAT_STATE					= 1278,	// 战斗状态

	ERR_MULT_CHAIJIE_SLOT_NOT_ENOUGH					= 1279,	// 批量拆解背包空间不足
	ERR_INSTANCE_TYPE_NOT_MATCH_TEAM_TYPE				= 1280, // 队伍类型和副本类型不匹配

	ERR_SELF_SELECT_REWARD_INVALID_INDEX				= 1281,	// 无效索引
	ERR_SELF_SELECT_REWARD_COOLDOWN						= 1282,	// 冷却中
	ERR_SELF_SELECT_REWARD_INDEX_HAS_DRAW				= 1283, // 该位置已抽取
	ERR_SELF_SELECT_REWARD_INVALID_SSR_IDX				= 1284, // 非法的层级类型
	ERR_SELF_SELECT_REWARD_ALL_DRAW_OUT_CHECK_FAILED	= 1285, // 全部抽出的校验失败了
	ERR_SELF_SELECT_REWARD_INVALID_EXCHANGE_KEY			= 1286, // 兑换索引非法
	ERR_SELF_SELECT_REWARD_EXCHANGE_LIMIT				= 1287, // 已达兑换限制次数
	ERR_SELF_SELECT_REWARD_EXCHANGE_REPU_NOT_ENOUGH		= 1288,	// 声望不足
	ERR_GROW_UP_INVALID_OP								= 1289,	// 非法操作
	ERR_TALENT_SOLUTION_COMBAT_STATE					= 1290,	// 战斗状态不能切天赋方案

	ERR_OPEN_PROF_COST_ITEM_LIMIT						= 1291, // 转职物品不足
	ERR_HTUNDER_BUFFOP_INVALID_WORLD					= 1292, // 在非法场景进行天谴计划buff操作
};

const int GNET_GS_ERROR_MAP[][2] =
{
	{0,					ERR_SERVICE_UNAVILABLE	},	//通用服务器内部错误
	{-1 * GNET::ERROR_GENERAL,		ERR_SERVICE_UNAVILABLE	},	// GNET::ERROR_GENERAL
	{-1 * GNET::ERROR_LINE_NOTFOUND,	ERR_LINE_NOT_FOUND	},	// GNET::ERROR_LINE_NOTFOUND
	{-1 * GNET::ERROR_PLAYER_OFFLINE,	ERR_ROLE_OFFLINE	},	// GNET::ERROR_PLAYER_OFFLINE
	{-1 * GNET::ERROR_TEAM_NOTFOUND,	ERR_NOT_IN_TEAM		},	// GNET::ERROR_TEAM_NOTFOUND
	{-1 * GNET::ERROR_DB_TIMEOUT,		ERR_OPERATION_TIMEOUT	},	// GNET::ERROR_DB_TIMEOUT
	{-1 * GNET::ERROR_TEAM_IN_INSTANCE,	ERR_TEAM_IN_INSTANCE	},
	{-1 * GNET::ERROR_TEAM_DENIED,		ERR_NOT_TEAM_LEADER	},
	{-1 * GNET::ERROR_NOT_ENOUGH_MEMBER,	ERR_NOT_ENOUGH_MEMBER	},	//
	{-1 * GNET::ERROR_GS_DROPDELIVERY,	ERR_LINE_NOT_FOUND	},	// GNET::ERROR_GENERAL
	{-1 * GNET::ERROR_GS_INVALIDSTATE,	ERR_LINE_NOT_FOUND	},	// GNET::ERROR_GENERAL
	{-1 * GNET::ERROR_GS_MULTILOGIN,	ERR_MULTILOGIN		},	// GNET::ERROR_GENERAL
	{-1 * GNET::ERROR_GS_INVALIDDATA,	ERR_SERVICE_UNAVILABLE	},	// GNET::ERROR_GENERAL
	{-1 * GNET::ERROR_GS_INVALIDWORLD,	ERR_LINE_NOT_FOUND	},	// GNET::ERROR_GENERAL
	{-1 * GNET::ERROR_GS_OVERLOADED,	ERR_MIRROR_FULL		},	// GNET::ERROR_GENERAL
	{-1 * GNET::ERROR_GS_INVALIDPOSITION,	ERR_INVALID_POSITION	},	// GNET::ERROR_GENERAL
	{-1 * GNET::ERROR_GS_LOADFAILED,	ERR_SERVICE_UNAVILABLE	},	// GNET::ERROR_GENERAL
	{-1 * GNET::ERROR_INVALID_NAME,		ERR_INVALID_NAME	},
	{-1 * GNET::ERROR_GAMEDBD_NAMEUSED,	ERR_NAME_USED		},
	{-1 * GNET::ERROR_INVALID_PASSWORD,	ERR_PASSWD_NOT_MATCH	},
	{-1 * GNET::ERROR_CANNOT_SPECTATE,	ERR_NO_FOUCS_PLAYER	},
	{-1 * GNET::ERROR_ALLIANCE_CANNT_APPLY,	ERR_ALLIANCE_STATE	},
	{-1 * GNET::ERROR_ALLIANCE_CANNT_START,	ERR_ALLIANCE_STATE	},
	{-1 * GNET::ERROR_ALLIANCE_MAX_FAMILY,	ERR_ALLIANCE_MAX	},
	{-1 * GNET::ERROR_IN_ALLIANCE_WAR,	ERR_ALLIANCE_STATE	},
	{-1 * GNET::ERROR_NO_FAMILY,		ERR_NO_FAMILY		},
	{-1 * GNET::ERROR_FACTION_NOTFOUND,	ERR_NO_MAFIA		},
	{-1 * GNET::ERROR_FACTION_NO_BASE,	ERR_NO_MAFIA_BASE	},
	{-1 * GNET::ERROR_FACTION_MAX_SUB,	ERR_MAFIA_MAX_SUB	},
	{-1 * GNET::ERROR_FACTION_APPLIED,	ERR_MAFIA_APPLIED	},
	{-1 * GNET::ERROR_FACTION_SUB_FACTION,	ERR_MAFIA_SUB_MAFIA	},
	{-1 * GNET::ERROR_FACTION_SUB_COOLDOWN,	ERR_OPERATION_IS_COOLING},
	{-1 * GNET::ERROR_TIZI_HASEXIST,	ERR_TIZI_HASEXIST	},
	{-1 * GNET::ERROR_TIZI_NOTEXIST,	ERR_TIZI_NOTEXIST	},
	{-1 * GNET::ERROR_FACTION_PERMISSION,	ERR_MAFIA_NO_PERMISSION	},
	{-1 * GNET::ERROR_FACTION_TIZINOTTIGUAN, ERR_MAFIA_TIZINOTTIGUAN},
	{-1 * GNET::ERROR_TIGUAN_CANT_ENTER,	ERR_MAFIA_TIGUANING	},
	{-1 * GNET::ERROR_MULTILOGIN,		ERR_MULTILOGIN		},
	{-1 * GNET::ERROR_NATION_IN_WAR,	ERR_NATION_IN_WAR	},
	{-1 * GNET::ERROR_LESS_LEVEL,		ERR_LEVEL_NOT_MATCH	},
	{-1 * GNET::ERROR_RENAME_PLAYER_NOT_FOUND,      ERR_RENAME_FAILED},
	{-1 * GNET::ERROR_RENAME_PLAYER_NOT_READY,      ERR_RENAME_FAILED},
	{-1 * GNET::ERROR_HOME_NO_HOME,		ERR_HOMETOWN_UNAVALIABLE},
	{-1 * GNET::ERROR_HOME_NOTFRIEND,	ERR_HOMETOWN_NO_PERMIT	},
	{-1 * GNET::ERROR_HOME_NO_FREE_SPACE,	ERR_HOMETOWN_FULL	},
	{-1 * GNET::ERROR_PLAYER_ROAMOUT,   ERR_ROAMOUT    },
	{-1 * GNET::ERROR_RENAME_CD,   ERR_ITEM_USE_CD    },
	{-1 * GNET::ERROR_CAREER_SHOP_NO_FREE_SPACE,   ERR_CAREER_SHOP_FULL	},
	{-1 * GNET::ERROR_HOMETOWN_PARTY_INVITE_CAN_ENTER, ERR_HOME_PARTY_INVITE_CAN_ENTER },
	{-1 * GNET::ERROR_MAP_PERSON_FULL, ERR_MAP_PERSON_FULL},
};

const inline bool CONVET_GNET_ERROR(int& error)
{
	if (error >= 0)
	{
		return false;
	}
	for (size_t i = 0; i < sizeof(GNET_GS_ERROR_MAP) / (sizeof(int) * 2); i++)
	{
		if (GNET_GS_ERROR_MAP[i][0] == error)
		{
			error = GNET_GS_ERROR_MAP[i][1];
			return true;
		}
	}
	error = GNET_GS_ERROR_MAP[0][1];
	return true;
}

enum
{
	GOP_INSTALL,
	GOP_UNINSTALL,
	GOP_BIND,
	GOP_BIND_DESTORY,
	GOP_BIND_DESTORY_RESTORE,
	GOP_MOUNT,
	GOP_TALISMAN_REFINE,
	GOP_TALISMAN_LVLUP,
	GOP_TALISMAN_RESET,
	GOP_TALISMAN_COMBINE,
};

// safe_lock.msgtype
enum
{
	SAFE_LOCK_SET    = 0,
	SAFE_LOCK_UNLOCK = 1,
};

//自研食谱相关错误码
enum
{
	ERR_RECIPE_RESEARCH_DATA                = 1001, //配置错误
	ERR_RECIPE_RESEARCH_FOOD_COUNT          = 1002, //食材种类数不对
	ERR_RECIPE_RESEARCH_FOOD_FULL           = 1003, //已经挑选过食材
	ERR_RECIPE_RESEARCH_FOOD_LOW            = 1004, //食材存量不足
	ERR_RECIPE_RESEARCH_SEASON_COUNT        = 1005, //调料种类数不对
	ERR_RECIPE_RESEARCH_NOT_NEED_SEASON     = 1006, //当前不在调味阶段
	ERR_RECIPE_RESEARCH_SEASON_FULL         = 1007, //已经有过调味
	ERR_RECIPE_RESEARCH_SEASON_LOW          = 1008, //调料存量不足
	ERR_RECIPE_RESEARCH_MINE_MISS           = 1009, //没有找到目标矿物
	ERR_RECIPE_RESEARCH_NOBIND_CONTAINER    = 1010, //当前未绑定托盘
	ERR_RECIPE_RESEARCH_BIND_CONTAINER      = 1011, //当前已绑定托盘
	ERR_RECIPE_RESEARCH_HAVE_INTERRUPT      = 1012, //必须先继续中断的流程
	ERR_RECIPE_RESEARCH_PROCESS_DUP         = 1013, //已完成此类操作
	ERR_RECIPE_RESEARCH_COOK_NOT_END        = 1014, //烹饪还未结束
	ERR_RECIPE_RESEARCH_WRONG_MATERIAL      = 1015, //食材选择不对
	ERR_RECIPE_RESEARCH_COOK_LEVEL_LOW      = 1016, //料理等级太低
	ERR_RECIPE_RESEARCH_WRONG_PROCESS       = 1017, //操作错误
	ERR_RECIPE_RESEARCH_NO_CONTAINER        = 1018, //必须有托盘才能操作
};

//商城购买相关内部错误码
enum
{
	//商城购买返回码
	ERR_MALL_GOODS_CHECK_FAIL		= 1,	//单件商品购买检查失败
	ERR_MALL_INVALID_REQUEST		= 2,
	ERR_MALL_CAN_NOT_AFFORD			= 3,	//钱不够
	ERR_MALL_INVENTORY_IS_FULL		= 4,
	ERR_MALL_MAILBOX_FULL			= 5,	//邮件已满
	ERR_MALL_SERVER_NETWORK			= 6,	//服务器通信错误
	ERR_MALL_REPU_CAN_NOT_AFFORD	= 7,	//声望不足
	ERR_MALL_MIDAS_BUSY				= 8,	//midas繁忙
	//单件商品购买错误码
	ERR_MALL_GOODS_SELL_TIME_LIMIT	= 10,
	ERR_MALL_PLAYER_PROP_DISMATCH	= 11,
	ERR_MALL_COMMON_USE_LIMIT		= 12,
	ERR_MALL_IDIP_FORBID_GOODS		= 13,	// idip禁止
	ERR_MALL_COOK_MATERIAL_ITEM_CAREER = 14,// 食材对应身份才能购买
	ERR_MALL_VIP_LEVEL_LIMIT		= 15,
	ERR_MALL_REPUTATION_LIMIT		= 16,	// 限制声望不够
	ERR_MALL_REPUTATION_MAX			= 17,	// 奖励声望达到上限
	ERR_MALL_DISCOUNT_CARD_FUNC_CLOSE	= 18,	// 折扣卡功能码关闭
	ERR_MALL_DISCOUNT_CARD_TOO_ENOUGH	= 19,	// 折扣卡数量大于商品数量
	ERR_MALL_CAN_NOT_USE_DISCOUNT_CARD	= 20,	// 不能使用折扣卡
	ERR_MALL_DISCOUNT_CARD_NOT_ENOUGH	= 21,	// 折扣卡数量不够
	ERR_MALL_REWARD_SERVICE             = 22,   // 奖励服务未开启
	ERR_MALL_FULL_REDUCTION_CLOSE		= 23,	// 满减功能关闭
	ERR_MALL_FULL_REDUCTION_ITEM_WRONG	= 24,	// 满减功能满减物品错误
	ERR_MALL_FULL_REDUCTION_CARD_WRONG	= 25,	// 满减功能满减卡错误
};

// 魔盒操作错误码
enum TetrahedronErrCode
{
	ERR_TETRAHEDRON_SUCCESS          = 0,  // 成功
	ERR_TETRAHEDRON_FAIL             = 1,  // 失败
	ERR_TETRAHEDRON_INVALID_IDX      = 2,  // 孔位位置错误
	ERR_TETRAHEDRON_INVALID_GEM      = 3,  // 宝石无效
	ERR_TETRAHEDRON_CONDITION        = 4,  // 条件不满足
	ERR_TETRAHEDRON_CONFIG           = 5,  // 配置错误
	ERR_TETRAHEDRON_GEM_ATTACHED     = 6,  // 已镶嵌宝石
	ERR_TETRAHEDRON_HOLE_DETACHED    = 7,  // 孔位尚未镶嵌
	ERR_TETRAHEDRON_BACKPACK_FULL    = 8,  // 背包已满
	ERR_TETRAHEDRON_GEM_UPGRADE_FAIL = 9,  // 宝石升级失败
	ERR_TETRAHEDRON_SUIT_LEVEL_LOW   = 10, // 套装等级过低
};

// 家园操作错误码
enum HomeTownOperationErrCode
{
	ERR_HOMETOWN_SUCCESS         = 0, // 成功
	ERR_HOMETOWN_FAIL            = 1, // 失败
	ERR_HOMETOWN_NONE            = 2, // 无家园
	ERR_HOMETOWN_MONEY_NO_ENOUGH = 3, // 钱不够
	ERR_HOMETOWN_FAIL_INNER      = 4, // 服务器内部错误
	ERR_HOMETOWN_OBJECT_LOCKED   = 5, // 尚未解锁
	ERR_HOMETOWN_MISS_PAYMENT    = 6, // 欠费
	ERR_HOMETOWN_NOT_FOR_SALE    = 7, // 非卖品
	ERR_HOMETOWN_GIFT_FUNC_NOT_OPEN   = 8, // 家园送礼没开启
	ERR_HOMETOWN_GIFT_WRONG_PARAM   = 9, // 家园送礼请求参数错误
	ERR_HOMETOWN_NO_NEED_CLEAN   = 10, // 不需要清洁
	ERR_HOMETOWN_NO_NEED_REPAIR  = 11, // 不需要维修
	ERR_HOMETOWN_EMPLOY_COND     = 12, // 管家雇佣条件不满足
	ERR_HOMETOWN_REPAIR_COOLDOWN = 13, // 维修操作冷却中
	ERR_HOMETOWN_FAIL_REPAIR     = 14, // 没修好
	ERR_HOMETOWN_OBJ_DIRTY       = 15, // 家具太脏
	ERR_HOMETOWN_OBJ_DAMAGED     = 16, // 家具损坏
	ERR_HOMETOWN_NOT_ENTER       = 17, // 无法拜访家园
	ERR_HOMETOWN_FUNC_FORBID     = 18, // 功能尚未开放
	ERR_HOMETOWN_CANNOT_SELL     = 19, // 不能出售
	ERR_HOMETOWN_MAX_LIMIT       = 20, // 达到拥有上限
	ERR_HOMETOWN_CHANGE_COOLDOWN = 21, // 卖房搬家冷却中
	ERR_HOMETOWN_OUTSIDE         = 22, // 不在家
	ERR_HOMETOWN_WORKER_WORKING  = 23, // 维修工正在工作
	ERR_HOMETOWN_WORKER_NO_NEED  = 24, // 不需要维修工
	ERR_HOMETOWN_PARK_USING      = 25, // 停车位已被占
	ERR_HOMETOWN_PARK_BLANK      = 26,
	ERR_HOMETOWN_NOT_OWNER       = 27,
	ERR_HOMETOWN_ASSESSING       = 28, // 测评中
	ERR_HOMETOWN_ASSESSED_TODAY  = 29, // 今日已测评
	ERR_HOMETOWN_NO_BUTLER		 = 30, // 没有雇佣管家
	ERR_HOMETOWN_BUTLER_FASHION_NOT_SAVE = 31, // 管家时装还没有保存
	ERR_HOMETOWN_BUTLER_FASHION_LOCK = 32, // 管家时装未解锁
	ERR_HOMETOWN_PLANT_NOT_EXIST  = 33, // 偷取的植物不存在
	ERR_HOMETOWN_CANNOT_STEAL  = 34, // 已经没有可偷的了
	ERR_HOMETOWN_PLANT_MAX = 35, // 已经达到种植最大上限
	ERR_HOMETOWN_NEGATIVE_NO_PICK = 36, // 负面状态不能采摘
	ERR_HOMETOWN_ALREADY_PLANT = 37, // 已经种植该植物
	ERR_HOMETOWN_NO_RIPE = 38, // 还没成熟
	ERR_HOMETOWN_NO_STEAL = 39, // 已经到达偷取上限
	ERR_HOMETOWN_PLANT_WRONG_OP = 40, // 种植非法操作
	ERR_HOMETOWN_NO_SEED = 41, // 没有种子
	ERR_PARTY_TYPE_WRONG  = 42, // 派对类型请求错误
	ERR_PARTY_PROSPERITY = 43, // 繁荣度不够
	ERR_PARTY_CD = 44, // 开派对CD冷却中
	ERR_PARTY_LIMIT = 45, // 开派对限次中
	ERR_PARTY_NO_MONEY = 46, // 开启派对货币不足
	ERR_PARTY_INVITE_CAN_ENTER = 47, // 仅邀请人可进入
	ERR_HOMETOWN_RESUCE_LIMIT = 48, // 帮别人消除达到限次
	ERR_HOMETOWN_NO_ACTIVITY = 49, // 活力值不足
	ERR_HOMETOWN_BLACKLIST = 50, // 处于黑名单中
	ERR_HOMETOWN_PARTY_END = 51, // 家园派对结束
	ERR_HOMETOWN_SCHEME_LOCKED = 52, // 家园装修方案尚未解锁
	ERR_HOMETOWN_SCHEME_NAME_INVALID = 53, // 无效的家园装修方案名称
	ERR_HOMETOWN_PROSPERITY_LOW = 54, // 家园繁荣度过低
	ERR_HOMETOWN_SCHEME_CD = 55, // 家园装修方案冷却中
	ERR_HOMETOWN_SCHEME_RETRY = 56, // 家园装修方案重试
	ERR_HOMETOWN_PARTY_DOING = 57, // 家园家园派对开启中无法操作
	ERR_HOMETOWN_SCHEME_INDEX_INVALID = 58, // 无效的家园装修方案序号
	ERR_HOMETOWN_SCHEME_UNLOCKED = 59, // 家园装修方案已解锁
	ERR_HOMETOWN_DESIGN_UPLOAD_TIMES_LIMIT = 60, // 家园设计上传次数限制
	ERR_HOMETOWN_DESIGN_RECOMMEND_CHANCE_LOW = 61, // 家园推荐券不足
	ERR_HOMETOWN_DESIGN_COOL_DOWN = 62, // 家园设计操作冷却中
	ERR_HOMETOWN_DESIGN_UPLOADING = 63, // 家园设计上传中
	ERR_HOMETOWN_DESIGN_NAME_OR_DESC_INVALID = 64, // 设计名或描述包含敏感词
	ERR_HOMETOWN_DESIGN_CSP_PARAM_INVALID = 65, // CSP检查参数错误
	ERR_HOMETOWN_DESIGN_CSP_UPLOAD_FAIL = 66, // CSP上传失败
	ERR_HOMETOWN_DESIGN_UPLOAD_COOL_DOWN = 67, // 家园设计上传冷却中
	ERR_HOMETOWN_DESIGN_APPLY_COOL_DOWN = 68, // 家园设计应用冷却中
	ERR_HOMETOWN_DESIGN_RECOMMEND_COOL_DOWN = 69, // 家园设计推荐冷却中
	ERR_HOMETOWN_DESIGN_NOT_CONTRACT        = 70,   // 契约家园设计非契约家园
	ERR_HOMETOWN_DESIGN_NOT_OWNER           = 71,   // 契约家园非所有人不得进入编辑模式
	ERR_HOMETOWN_DESIGN_ALREADY_IN_DESIGN   = 72,   // 契约家园已经处于编辑模式，无法进入编辑模式
	ERR_HOMETOWN_DESIGN_NOT_IN_DESIGN       = 73,   // 契约家园没有处于编辑模式，无法编辑
	ERR_HOMETOWN_CONTRACT_NOT_SELL_SPOUSE   = 74,   // 契约家园家具禁止对方出售
	ERR_HOMETOWN_CONTRACT_DEMOLISH_REFUSE   = 75,   // 契约家园全部拆除对方拒绝
	ERR_HOMETOWN_CONTRACT_TARGTE_TOO_FAR    = 76,   // 契约家园操作对方距离过远
	ERR_HOMETOWN_DESIGN_BAN_FURNITURE		= 77,	// 活动家园模板使用了被禁止的家具
	ERR_HOMETOWN_DESIGN_CAMP_END			= 78,	// 活动家园模板对应活动已结束
};

enum BigDataErrCode
{
	ERR_BIGDATA_SUCCESS                = 0, // 成功
	ERR_BIGDATA_FAIL                   = 1, // 上传失败错误
	ERR_BIGDATA_INVALID_FUNC           = 2, // 无效的功能类型
	ERR_BIGDATA_UPLOADING              = 3, // 正在上传
	ERR_BIGDATA_DATA_LARGE             = 4, // 数据过大
	ERR_BIGDATA_SEGMENT_INDEX_REPEATED = 5, // 数据段重复
	ERR_BIGDATA_SEGMENT_INDEX_INVALID  = 6, // 数据段序号无效
	ERR_BIGDATA_HEAD_NO_EXIST          = 7, // 服务器没有准备好对应的数据
	ERR_BIGDATA_SEGMENT_COUNT_LIMIT    = 8, // 数据段数量过多
	ERR_BIGDATA_HEAD_INVALID           = 9, // 头部数据无效
	ERR_BIGDATA_COMPRESS_DATA_INVALID  = 10, // 压缩数据无效
	ERR_BIGDATA_FAIL_TO_DECOMPRESS     = 11, // 解压失败
	ERR_BIGDATA_TIMEOUT                = 12, // 上传超时
};

//炼金矩阵错误码
enum AlchemyRunestoneErrCode
{
	ERR_ALCHEMY_SUCCESS				= 0,
	ERR_ALCHEMY_OP					= 1,	// op号错误或不存在
	ERR_ALCHEMY_NO_ITEM				= 2,	// 缺少道具
	ERR_ALCHEMY_PARAM				= 3,	// 参数不正确
	ERR_ALCHEMY_MOVE_FAIL			= 4,	// 插入失败
	ERR_ALCHEMY_ITEM_LIST_FULL		= 5,	// 背包已满
	ERR_ALCHEMY_CANNOT_MIX			= 6,	// 无法升级
	ERR_ALCHEMY_KFUNC				= 7,	// 功能码限制
	ERR_ALCHEMY_LESS_BREAK_ITEM		= 8,	// 突破道具不足(经验溢出)

};

enum Prof12ShadowFashionErrCode
{
	ERR_PROF12_SHADOW_FASHION_SUCCESS				= 0,
	ERR_PROF12_SHADOW_FASHION_LOST_MASK				= 1,	// 没有填充mask信息
	ERR_PROF12_SHADOW_FASHION_ERR_MASK				= 2,	// 存在错误的修改位置
	ERR_PROF12_SHADOW_FASHION_ERR_SIZE				= 3,	// 修改位置的数量和提供的时装数量不匹配
};

//彩虹航线(滑板)错误码
enum SkateBoardRaceErrCode
{
	ERR_SKATEBOARD_SUCCESS					= 0,
	ERR_SKATEBOARD_OP						= 1,	// op号错误或不存在
	ERR_SKATEBOARD_PARAM					= 2,	// 参数不正确
	ERR_SKATEBOARD_OTHER_PLAYER				= 3,	// 错误的玩家
	ERR_SKATEBOARD_LATE						= 4,	// 迟到了(延迟导致)
	ERR_SKATEBOARD_NO_EMPTY_SLOT			= 5,	// 道具槽已满
	ERR_SKATEBOARD_UNKNOW_ID				= 6,	// 未知的道具球id
	ERR_SKATEBOARD_SYNCHRONIZATION_FAILURE	= 7,	// 失去同步
	ERR_SKATEBOARD_NOT_COOL_DOWN			= 8,	// 技能未冷却完
};

enum RestaurantErrCode
{
	ERR_RESTAURANT_SUCCESS			= 0,
	ERR_RESTAURANT_OP				= 1,	// op号错误或不存在
	ERR_RESTAURANT_HAS_EXPLORE		= 2,	// 已存在探索地图
	ERR_RESTAURANT_NO_EXPLORE		= 3,	// 不存在探索地图
	ERR_RESTAURANT_MAP_LOCKED		= 4,	// 探索地图未解锁
	ERR_RESTAURANT_PARAM			= 5,	// 参数不正确
	ERR_RESTAURANT_SERVER			= 6,	// 服务器内部错误(配置)
	ERR_RESTAURANT_MAP_NOT_FINISH	= 7,	// 地图未达到扫荡条件
	ERR_RESTAURANT_NO_VITALITY		= 8,	// 体力不足
	ERR_RESTAURANT_NO_GOLD			= 9,	// 金币不足
	ERR_RESTAURANT_MAX_LEVEL		= 10,	// 已达到最高等级/阶段
	ERR_RESTAURANT_HAS_SWAP			= 11,	// 已经扫荡过
	ERR_RESTAURANT_LINE_NOT_EMPTY	= 12,	// 重复开始生产线
	ERR_RESTAURANT_NO_SOURCE		= 13,	// 材料不足
	ERR_RESTAURANT_MENU_LOCKED		= 14,	// 食谱未解锁
	ERR_RESTAURANT_NO_SP_ITEM		= 15,	// 特殊道具不足
	ERR_RESTAURANT_LOW_LINE_LEVEL	= 16,	// 生产线等级不足
	ERR_RESTAURANT_NO_EMPTY_LINE	= 17,	// 无空闲生产线
	ERR_RESTAURANT_NO_DISH			= 18,	// 食品不足
	ERR_RESTAURANT_NO_FRAGMENT		= 19,	// 碎片不足
	ERR_RESTAURANT_JUMP_UNLOCK		= 20,	// 前置未解锁
	ERR_RESTAURANT_HAS_QUICK_GAME	= 21,	// 已存在快速模式存档
	ERR_RESTAURANT_COLD_DOWN		= 22,	// CD中
	ERR_RESTAURANT_NO_DIAMOND		= 23,	// 钻石不足
	ERR_RESTAURANT_TIMES_LIMIT		= 24,	// 通用次数不足
	ERR_RESTAURANT_SOME_PARAM		= 25,	// 可接受的参数异常
	ERR_RESTAURANT_NO_QUICK_GAME	= 26,	// 快速模式未开始或已结束
};

enum LMFShopErrCode
{
	ERR_LMFSHOP_SUCCESS				= 0,
	ERR_LMFSHOP_OP					= 1,	// op号错误或不存在
	ERR_LMFSHOP_PARAM				= 2,	// 参数不正确
	ERR_LMFSHOP_RACK_LOCK			= 3,	// 货架未解锁
	ERR_LMFSHOP_SERVER				= 4,	// 服务器内部错误(配置)
	ERR_LMFSHOP_CASH			 	= 5,	// 点券不足
	ERR_LMFSHOP_BUY_LIMIT		 	= 6,	// 购买限次
	ERR_LMFSHOP_ITEM				= 7,	// 物品不存在
	ERR_LMFSHOP_BAG					= 8,	// 背包空间不足
	ERR_LMFSHOP_CD					= 9,	// 冷却中
	ERR_LMFSHOP_DIAMOND				= 10,	// 钻石不足
	ERR_LMFSHOP_HAS_DRAW			= 11,	// 领取过
	ERR_LMFSHOP_CAN_NOT_REFRESH	 	= 12,	// 刷新货架失败，通常是因为已经无道具可刷新
	ERR_LMFSHOP_ACTIVITY			= 13,	// 活动未开启
};

}

#pragma pack()

#endif

