#ifndef __GS_PLAYER_MISC_H__
#define __GS_PLAYER_MISC_H__
#include "gprotoc/player_corps_attr_config_t.pb.h"
#include "gprotoc/db_player_simple_stock.pb.h"
#include "gprotoc/player_corps_cache_t.pb.h"
#include "gprotoc/gp_vip_info.pb.h"
namespace PB { namespace ipt_amity_grade_sync_NS { class ipt_amity_grade_sync; } using namespace ipt_amity_grade_sync_NS; }
#include "gprotoc/db_player_park_data.pb.h"
namespace PB { namespace gp_career_shop_buyer_info_NS { class gp_career_shop_buyer_info; } using namespace gp_career_shop_buyer_info_NS; }
namespace PB { namespace gp_top_star_report_NS { class gp_top_star_report; } using namespace gp_top_star_report_NS; }
namespace PB { namespace gp_vip_operation_NS { class gp_vip_operation; } using namespace gp_vip_operation_NS; }
#include "gprotoc/player_universal_data_t.pb.h"
namespace PB { namespace gp_upgrade_chariot_NS { class gp_upgrade_chariot; } using namespace gp_upgrade_chariot_NS; }
namespace PB { namespace gp_adventure_task_list_NS { class gp_adventure_task_list; } using namespace gp_adventure_task_list_NS; }
#include "gprotoc/corp_attribute.pb.h"
namespace PB { namespace gp_roam_community_op_NS { class gp_roam_community_op; } using namespace gp_roam_community_op_NS; }
namespace PB { namespace gp_simple_stock_op_NS { class gp_simple_stock_op; } using namespace gp_simple_stock_op_NS; }
#include "gprotoc/wish_tree_data_t.pb.h"
#include "gprotoc/DRAW_TYPE.pb.h"
namespace PB { namespace gp_multi_exp_NS { class gp_multi_exp; } using namespace gp_multi_exp_NS; }
namespace PB { namespace ipt_role_unsave_data_NS { class ipt_role_unsave_data; } using namespace ipt_role_unsave_data_NS; }
#include "gprotoc/ipt_top_star_report.pb.h"
#include "gprotoc/db_player_ticket_info.pb.h"
#include "gprotoc/simple_stock_price_data_gs.pb.h"
namespace PB { namespace retrieve_info_str_NS { class retrieve_info_str; } using namespace retrieve_info_str_NS; }
namespace PB { namespace gp_top_star_data_NS { class gp_top_star_data; } using namespace gp_top_star_data_NS; }
namespace PB { namespace gp_retrieve_op_NS { class gp_retrieve_op; } using namespace gp_retrieve_op_NS; }
#include "gprotoc/player_overcook_info.pb.h"
#include "gprotoc/draw_seq_info.pb.h"
#include "gprotoc/db_player_misc.pb.h"
#include "gprotoc/bouquet_collect_data.pb.h"
#include "gprotoc/db_player_dice_card_data.pb.h"
#include "gprotoc/retrieve_info_t.pb.h"
#include "gprotoc/player_chatbox.pb.h"
namespace PB { namespace gp_small_bag_download_resource_NS { class gp_small_bag_download_resource; } using namespace gp_small_bag_download_resource_NS; }
#include "gprotoc/gp_video_game_info.pb.h"
namespace PB { namespace ipt_roam_community_op_re_NS { class ipt_roam_community_op_re; } using namespace ipt_roam_community_op_re_NS; }
namespace PB { namespace gp_corp_config_NS { class gp_corp_config; } using namespace gp_corp_config_NS; }
namespace PB { namespace gp_bouquet_op_NS { class gp_bouquet_op; } using namespace gp_bouquet_op_NS; }
namespace PB { namespace gp_retrieve_info_req_NS { class gp_retrieve_info_req; } using namespace gp_retrieve_info_req_NS; }
namespace PB { namespace gp_mount_space_operation_NS { class gp_mount_space_operation; } using namespace gp_mount_space_operation_NS; }
namespace PB { namespace gp_mount_space_operation_re_NS { class gp_mount_space_operation_re; } using namespace gp_mount_space_operation_re_NS; }
namespace PB { namespace gp_townlet_op_NS { class gp_townlet_op; } using namespace gp_townlet_op_NS; }
namespace PB { namespace gp_townlet_op_re_NS { class gp_townlet_op_re; } using namespace gp_townlet_op_re_NS; }
#include "gprotoc/ipt_remote_call.pb.h"
namespace PB { namespace role_trade_repu_info_NS { class role_trade_repu_info; } using namespace role_trade_repu_info_NS; }
namespace PB { namespace role_trade_other_info_NS { class role_trade_other_info; } using namespace role_trade_other_info_NS; }
#include "gprotoc/pray_data_t.pb.h"
#include "gprotoc/gp_player_pray_op.pb.h"
#include "gprotoc/gp_player_pray_op_re.pb.h"
#include "gprotoc/db_townlet_data.pb.h"
#include "gprotoc/TOWNLET_OP.pb.h"
#include "gprotoc/bingo_info.pb.h"
#include "gprotoc/share_box_info.pb.h"
namespace PB { namespace ipt_ai_chat_NS { class ipt_ai_chat; } using namespace ipt_ai_chat_NS; }
namespace PB { namespace gp_personal_card_op_NS { class gp_personal_card_op; } using namespace gp_personal_card_op_NS; }
#include "gprotoc/virtual_grow_up_data.pb.h"
namespace PB { namespace gp_grow_up_data_NS { class gp_grow_up_data; } using namespace gp_grow_up_data_NS; }

#include <unordered_map>
#include <staticmap.h>
#include "glog.h"
#include "reputation_template.h"
#include "addon_manager.h"
#include "activity_manager.h"
#include "remote_call.h"
#include "reputation_limit.h"
#include "idip_data_manager.h"
#include "interprocess_data.h"

class gcreature_imp;
class ginstance_imp;

struct  client_stat
{
	int lantency;			//服务器发起的保存的延迟
	int cli_lantency;		//客户端检测出来的延迟
	float fps;			//客户端的fps
	int mem_used;			//kb

	client_stat()
	{
		lantency = -1;
		cli_lantency = -1;
		fps = 0.f;
		mem_used = 0;
	}
	
};

struct player_stage_data
{
	int scene_tag;		//关卡的场景id
	int instance_tid;	//关卡的副本id，不一定有
	int difficulty;		//难度
	int start_time;		//开始的时间
	int need_vp;		//需要多少vp	
	int use_vp;		//使用了多少vp  使用vp和需要vp一致代表扣除了全部vp
	int complete;		//是否通关
	int revive_count;	//复活次数
	int player_count;	//初始玩家数量（只在第一次时更新，用于判定是否多人副本）
	A3DVECTOR3 guide_pos;
	rect battle_region;//版面战斗区域
	A3DVECTOR3 battle_region_pos;	//战斗区域指引点
	bool is_sweep_level;	//是否闯关副本
	bool vp_mode;		//是否消耗vp并发奖励
	bool can_auto_combat;   //可否自动战斗
	bool can_summon_hero;   //可否召唤名人 $$$$$$ 未限制
	bool can_summon_friend; //可否召唤朋友 $$$$$$ 未限制
	bool guide_pos_active;	//是否有自动目标点
	bool battle_region_active;//是否存在版面战斗区域
	player_stage_data()
	{
		memset(this, 0, sizeof(*this));
	}

	void InitStage(int tag, int ins_tid, int diff, bool sweep_level)
	{
		scene_tag = tag;
		instance_tid = ins_tid;
		difficulty = diff;
		is_sweep_level = sweep_level;
		start_time = gmatrix::GetInstance().GetSysTime();
		use_vp = 0;
		need_vp = 0;
		revive_count = 0;
		complete = 0;
	}

	void SetVPMode(bool mode)
	{
		vp_mode = mode;
	}

	bool IsReady() const
	{
		return scene_tag > 0;
	}

	bool IsSweepLevel() const 
	{
		return IsReady() && is_sweep_level;
	}

	bool Check(int tag, int diff)
	{
		if(scene_tag <= 0 || tag != scene_tag) return false;
		return diff == difficulty;
	}

	void Accomplish()
	{
		complete = 1;
	}

	bool IsComplete() const { return complete;}
};

class player_reputation
{
	ruid_t roleid;
	REPUTATION_M roam_reputations;	//玩家跨服时候缓存的声望
	unsigned short roam_reputation_max;	//跨服时候声望的索引最大值
public:
	player_reputation(): roleid(0), roam_reputation_max(0) { }

	inline void SetRoleID(ruid_t _roleid)
	{
		roleid = _roleid;
	}

	inline void Save(GNET::Octets& data)//实际上是个get, 不是保存（save）
	{
		INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
		if (!ipd)
		{    
			return;
		}    

		ipd->rolemap.lock_and_run<int>(roleid, [&data](INTERPROCESS::ip_roleinfo_t *pRole)->int
		{    
			if (!pRole)
			{
				return -1;
			}

			SaveReputation(pRole->get_reputation_data(), data, pRole->get_reputation_id_max(), true, true);
			return 0;
		});  
	}

	inline void Load(const char * buffer, size_t size)
	{
		INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
		if (!ipd)
		{    
			return;
		}    

		ipd->rolemap.lock_and_run<int>(roleid, [buffer, size](INTERPROCESS::ip_roleinfo_t *pRole)->int
		{
			if (!pRole)
			{
				return -1;
			}
			
			LoadReputation<INTERPROCESS::ip_roleinfo_t::ip_reputation_node_t, INTERPROCESS::ip_roleinfo_t::ip_reputation_node_t>(pRole->get_reputation_data(), buffer, size, pRole->get_reputation_id_max(), true, false);
			return 0;
		});
	}

	inline void SetRoamReputations(const std::map<int, int>& reputations)
	{
		if (reputations.empty())
		{
			roam_reputations.clear();
			return;
		}

		for (auto it = reputations.begin(), eit = reputations.end(); it != eit; ++it)
		{
			__PRINTF("player_reputation::SetRoamReputations id=%d value=%d\n", it->first, it->second);
			role_reputation_node & node = roam_reputations[it->first];
			node.value = it->second;
			if (GNET::IsPeriodReputation(it->first))
			{
				node.modify_time = gmatrix::GetInstance().GetSysTime();
			}
			if (roam_reputation_max < it->first)
			{
				roam_reputation_max = it->first;
			}
		}
	}

	inline bool Modify(unsigned int id, int offset, int &final_value)
	{
		if(id >= GNET::REPUID_MAX)
		{
			return false;
		}

		if (offset > 0)
		{
			if (GNET::IsIdipForbidRepuInc(id))
			{
				return false;
			}
		}
		else if (offset < 0)
		{
			if (GNET::IsIdipForbidRepuDec(id))
			{
				return false;
			}
		}
			
		INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
		if (!ipd)
		{    
			return false;
		}    

		bool ret = ipd->rolemap.lock_and_run<bool>(roleid, [id, offset, &final_value](INTERPROCESS::ip_roleinfo_t *pRole)->bool
		{
			if (!pRole)
			{    
				return false;
			}    

			int max = reputation_limit_manager::GetInstance().GetLimit(id);
			return pRole->modify_reputation(id, offset, max, final_value);
		});
			
		if (ret && GNET::IsDeliverReputation(id))
		{
			//通知DS声望被修改了
			RemoteCall::CallObjectRemoteFunc("GSReputationOperation", roleid, PB::ipt_remote_call::OBJECT_TYPE_DS_ROLEINFO, (void*)&roleid, (char)INTERPROCESS::IP_REPU_OP_MODIFY, (unsigned short)id, final_value);
		}

		return ret;
	}

	inline bool Set(unsigned int id, int rep, int &old_value, int &final_value)
	{
		if(id >= GNET::REPUID_MAX)
		{
			return false;
		}

		INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
		if (!ipd)
		{    
			return false;
		}

		bool ret = ipd->rolemap.lock_and_run<bool>(roleid, [id, rep, &old_value, &final_value](INTERPROCESS::ip_roleinfo_t *pRole)->bool
		{
			if (!pRole)
			{
				return false;
			}    
			old_value = pRole->get_reputation(id);

			if (rep > old_value)
			{
				if (GNET::IsIdipForbidRepuInc(id))
				{
					return false;
				}
			}
			else if (rep < old_value)
			{
				if (GNET::IsIdipForbidRepuDec(id))
				{
					return false;
				}
			}

			int max = reputation_limit_manager::GetInstance().GetLimit(id);
			return pRole->set_reputation(id, rep, max, final_value);
		});

		if (ret && GNET::IsDeliverReputation(id))
		{
			//通知DS声望被修改了
			RemoteCall::CallObjectRemoteFunc("GSReputationOperation", roleid, PB::ipt_remote_call::OBJECT_TYPE_DS_ROLEINFO, (void*)&roleid, (char)INTERPROCESS::IP_REPU_OP_SET, (unsigned short)id, final_value);
		}

		return ret;
	}

	inline int Get(unsigned int id) const
	{
		INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
		if (!ipd)
		{    
			return 0;
		}

		int value = ipd->rolemap.lock_and_run<int>(roleid, [id](INTERPROCESS::ip_roleinfo_t *pRole)->int
		{
			if (!pRole)
			{
				return 0;
			}

			return pRole->get_reputation(id);
		});
		return value;
	}

	typedef int REP_BUF[GNET::REPUID_MAX];
	inline const int* GetData(REP_BUF rep_buf)
	{
		memset(rep_buf, 0, sizeof(REP_BUF));
		//if (IS_LOCAL_ZONE_ID(roleid))
		{
			INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
			if (!ipd)
			{
				return rep_buf;
			}
			ipd->rolemap.lock_and_run<int>(roleid, [rep_buf](INTERPROCESS::ip_roleinfo_t *pRole)->int{
						if (!pRole)
						{
							return -1;
						}
						pRole->get_reputation_data(rep_buf);
						return 0;
					});
		}
		/*else
		{
			for (auto it = roam_reputations.begin(), eit = roam_reputations.end(); it != eit; ++it)
			{
				if (!NeedClear(it->first, it->second.modify_time, gmatrix::GetInstance().GetSysTime()))
				{
					rep_buf[it->first] = it->second.value;
				}
			}
		}*/
		return rep_buf;
	}
	inline size_t GetCount() 
	{
		//if (IS_LOCAL_ZONE_ID(roleid))
		{
			INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
			if (!ipd)
			{
				return 0;
			}
			return ipd->rolemap.lock_and_run<int>(roleid, [](INTERPROCESS::ip_roleinfo_t *pRole)->size_t{
						if (!pRole)
						{
							return 0;
						}
						//因为有0号声望的存在，所以一定要比声望最大值多1。
						return pRole->get_reputation_id_max() + 1;
					});
		}
		/*else
		{
			return roam_reputation_max;
		}*/
	}
	inline void DebugClear() 
	{
		//if (IS_LOCAL_ZONE_ID(roleid))
		{
			//如果roleid是本服的，可以直接修改共享内存里的声望
			INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
			if (!ipd)
			{
				return;
			}
			ipd->rolemap.lock_and_run<int>(roleid, [](INTERPROCESS::ip_roleinfo_t *pRole)->int{
					if (!pRole)
					{
						return -1;
					}
					pRole->debug_clear_reputation();
					return 0;
				});
		}
		/*else
		{
			//如果roleid是跨服过来的，不能直接修改共享内存里的值，等DS先改
		}*/

		RemoteCall::CallObjectRemoteFunc("GSReputationOperation", roleid, PB::ipt_remote_call::OBJECT_TYPE_DS_ROLEINFO, (void*)&roleid, 
				(char)INTERPROCESS::IP_REPU_OP_CLEAR, (unsigned short)0, (int)0);
	}
	void MakeRoleTradeRepuInfo(PB::role_trade_repu_info &r_info);
	void MakeRoleTradeOtherInfo(PB::role_trade_other_info &o_info);
};

//不加锁实现
struct FuncInfo;
class ModifyRepuObserver
{
public:
	ModifyRepuObserver();
	static ModifyRepuObserver& Instance()
	{
		static ModifyRepuObserver _instance;
		return _instance;
	}
public:
	//声望改变之后调用
	using OnModifyReputation = std::function<void(gplayer_imp*, int, int, int)>;
	OnModifyReputation _observers[GNET::REPUID_MAX];

	inline bool InsertObserver(int repu_id, OnModifyReputation&& observer)
	{
		if(repu_id <= 0 || repu_id >= GNET::REPUID_MAX) return false;
		if(_observers[repu_id]) return false;
		_observers[repu_id] = std::move(observer);
		return true;
	}
	inline void OnRepuModify(gplayer_imp* imp, int repu_id, int offset, int cur_repu)
	{
		if(repu_id <= 0 || repu_id >= GNET::REPUID_MAX) return;
		if(!_observers[repu_id]) return;
		_observers[repu_id](imp, offset, cur_repu, repu_id);
	}
public:
	//购买声望之前检查
	typedef bool CanPlayerBuyReputation(gplayer_imp* imp, int offset);
	CanPlayerBuyReputation* _buycheckers[GNET::REPUID_MAX];

	inline bool InsertBuyChecker(int repu_id, CanPlayerBuyReputation* checker)
	{
		if(repu_id <= 0 || repu_id >= GNET::REPUID_MAX) return false;
		if(_buycheckers[repu_id]) return false;
		_buycheckers[repu_id] = checker;
		return true;
	}
	inline bool CheckPlayerBuyRepu(gplayer_imp* imp, int repu_id, int offset)
	{
		if(repu_id <= 0 || repu_id >= GNET::REPUID_MAX) return false;
		if(!_buycheckers[repu_id]) return true;
		return _buycheckers[repu_id](imp, offset);
	}

public:
	//改变声望之前调用修正offset,主要用来更改特殊声望最大值
	using CPreRepuModify = std::function<void(gplayer_imp*, int&, int, const FuncInfo& fi, int)>;
	CPreRepuModify _pre_fix[GNET::REPUID_MAX];

	inline bool InsertPreRepuModify(int repu_id, CPreRepuModify&& checker)
	{
		if(repu_id <= 0 || repu_id >= GNET::REPUID_MAX) return false;
		if(_pre_fix[repu_id]) return false;
		_pre_fix[repu_id] = std::move(checker);
		return true;
	}
	inline void PreRepuModify(gplayer_imp* imp, int repu_id, int& offset, int old_value, const FuncInfo& fi)
	{
		if(repu_id <= 0 || repu_id >= GNET::REPUID_MAX) return;
		if(!_pre_fix[repu_id]) return;
		_pre_fix[repu_id](imp, offset, old_value, fi, repu_id);
	}

	void InitPresonalityRepuObserver();
    void InitStarFilmRepuObserver();
};

class player_repu_observer_manager
{
public:
	using OnModifyReputation = std::function<void(gplayer_imp* imp, int, int, int)>;
	using ObserverMap = std::map<int, OnModifyReputation>;

	player_repu_observer_manager() { }

	bool AddObserver(int repu_id, OnModifyReputation&& observer)
	{
		if(repu_id <= 0 || repu_id >= GNET::REPUID_MAX)
		{
			return false;
		}
		if (m_observers.find(repu_id) != m_observers.end())
		{
			return false;
		}

		m_observers[repu_id] = std::move(observer);
		return true;
	}

	void OnRepuModify(gplayer_imp* imp, int repu_id, int offset, int cur_repu)
	{
		if(repu_id <= 0 || repu_id >= GNET::REPUID_MAX)
		{
			return;
		}
		auto it = m_observers.find(repu_id);
		if (it == m_observers.end())
		{
			return;
		}

		auto& f = it->second;
		f(imp, offset, cur_repu, repu_id);
	}

private:
	ObserverMap m_observers;
};

class speed_history
{
	enum
	{
		SPEED_SEQ_LEN = 8, //NOTE: 必须是2的幂
	};

	float _speeds[SPEED_SEQ_LEN];
	float _max_speed;
	size_t _index;

public:
	speed_history() { Reset(); }

	void Reset()
	{
		memset(_speeds, 0, sizeof(_speeds));
		_max_speed = 0;
		_index = 0;
	}
	void Push(float spd)
	{
		_speeds[_index&(SPEED_SEQ_LEN-1)] = spd;
		_index++;

		if (spd+1e-3 > _max_speed)
			_max_speed = spd;
		else
		{
			_max_speed = 0;
			for (size_t i=0; i<SPEED_SEQ_LEN; i++)
			{
				if (_speeds[i] > _max_speed)
					_max_speed = _speeds[i];
			}
		}
	}
	float GetMax() const
	{
		return _max_speed;
	}
};

struct move_ctrl
{
	speed_history _speed_history;

	timeval _last_time;
	int64_t  _time_acc;
	int  _expect_time;
	int  _expect_milli_time;
	int  _move_counter;
	bool _first_check;
	bool _stop_flag;
	unsigned char _move_stamp;		//移动的时间戳，每次服务器向客户端同步位置时，这个值会增量 这样可以过滤客户端的无效数据
	unsigned char _stamp_err_count;		//移动的时间戳的错误记录，如果超过一个阈值，则再次同步给客户端消息
	int _last_sync_time;			//TODO 两个临时变量，调试用
	int _last_last_sync_time;
	timeval _last_move_irregular_time;		//最近的一次不规则移动

//	int _last_check_error;			//最近一次CheckTime/CheckMove失败的确切原因
	int _ignore_over_speed_move;		//非0则忽略超速move协议(即不回notify_pos)
	unsigned short _extra_sync_time;	//连续的客户端extra_sync的累计时间

	move_ctrl():_time_acc(0),_expect_time(0),_expect_milli_time(0),_move_counter(0),_first_check(true),_stop_flag(true),_move_stamp(0),_stamp_err_count(0),_last_sync_time(0),_last_last_sync_time(0),/*_last_check_error(0),*/_ignore_over_speed_move(0),_extra_sync_time(0) {
		_last_time = _last_move_irregular_time = (struct timeval){0};
	}

	enum { MAX_MOVE_USE_TIME = 1000 };
public:
	void SetTimeExpect(int t) { _expect_time = t;_expect_milli_time = 0;}
	bool CheckTime(gplayer_imp* imp,const timeval& cur_time,int timeuse,int timestamp,bool stop_flag);
	bool CheckStamp(unsigned char move_stamp);
	int CheckMove(gplayer_imp* imp,const A3DVECTOR3& orgin_pos,const A3DVECTOR3& target,const timeval& cur_time,int timeuse, unsigned int move_flags);
	void SetLastSyncTime(int timestamp);
	void SetStopFlag() { _stop_flag = true; }

	unsigned char GetMoveStamp() const { return _move_stamp;}
	unsigned char GetNextStamp() { _stamp_err_count = 0; return ++_move_stamp;}

//	void ClearLastCheckError() { _last_check_error = 0; }
//	bool CheckFailedByOverSpeed() const { return (_last_check_error==-4 || _last_check_error==-5); }

	void OnBroadcastMove() { _extra_sync_time = 0; }
	int GetUseTime() const { return _extra_sync_time;}
	bool SuggestBroadcast() const { return _extra_sync_time >= MAX_MOVE_USE_TIME;}

	void CountDownSyncFlag() {  if(_ignore_over_speed_move>0) --_ignore_over_speed_move;}
	void ResetSyncCountDown() { _ignore_over_speed_move = 5;}
	bool NeedSyncPos(unsigned char stamp) 
	{
		if(stamp == _move_stamp) return (_ignore_over_speed_move <= 0);
		return (++_stamp_err_count >= 8);
	}


	void ResetSpeedHistory() { _speed_history.Reset(); }
};

/*struct FU_value_ctrl	//定期刷新数据的逻辑， 用于刷新精力等数值
{
	typedef void FU_Modifier(gcreature_imp * imp, int64_t arg, int count);
	typedef void FU_Init(int id, int & timestmap, int cur_timestamp);
	struct FU_TYPE
	{
		enum
		{
			TYPE_DAY_FIX,		//每天定点
			TYPE_HOUR_FIX,		//每小时定点
			TYPE_PERIOD,		//指定时间间隔
		};
		int ID;
		int period_type;		//每天定点  每天定时 每周定点
		unsigned int period;		//根据period_type确定的参数
		int64_t arg;			//自定义参数
		FU_Modifier * modifier;		//修改器，使用这个修改器来修改属性
		FU_Init * ctor;			//初始化，用于最初属性

		bool operator <(const FU_TYPE & rhs) const { return ID < rhs.ID;}
	};

public:
	static bool AddDayFU(int ID, int second_in_day, int64_t arg, FU_Modifier * modifier, FU_Init * Init=NULL);			//每天指定时刻触发
	static bool AddHourFU(int ID, int second_in_hour, int64_t arg, FU_Modifier * modifier, FU_Init * Init=NULL);			//每小时指定时刻触发
	static bool AddRepeatFU(int ID, int seconds, int64_t arg, FU_Modifier * modifier, FU_Init * Init=NULL);			//定期触发（每30分钟等）0

private:
	static abase::vector<FU_TYPE> _ctrl_stub;

	static bool UpdateValue(gcreature_imp * imp, int cur_timestamp, int & last_timestamp, size_t idx);

public:
	struct FU_data
	{
		int id;			//id，和stub里的id顺序一致
		int last_timestamp;	//
	};
	abase::vector<FU_data> _data;
public:
	enum	//这里代表定期更新的数据类型ID，这里的ID不能修改，可以删除和添加，但删除后就不应该再继续使用了。这个ID会保存在数据库中，其实就是一种KEY
	{
		ID_VP_DAY_TIME1 	= 0,
		ID_VP_DAY_TIME2 	= 1,
		ID_VP_INTERVAL		= 2,
		ID_DAILY_UPDATE		= 3,		//每天0点
		ID_DAILY_MONRNING	= 4,		//每天早晨6点，注意，这个值必须在0点的后面，否则不正确
		ID_DAILY_MONRNING2	= 5,		//开服补偿的6点定时器
		ID_DAILY_NIGHT		= 6,		//每天9点检查并发放国战成就奖
		ID_DAILY_MOONE          = 7,            //每天12点刷新宝箱活动次数
	};

	void Init(time_t cur_timestamp);
	void Load(archive & ar);
	void Save(archive & ar);

	void UpdateValue(gcreature_imp * imp,int timestamp);
};*/

//玩家好友的召唤数据，需要存盘
/*class friend_summon_history
{
	abase::static_multimap<ruid_t, int> _history;		//每个好友本日的召唤次数
	int _total_count;					//所有好友召唤的总次数（上限与等级相关）
public:
	friend_summon_history():_total_count(0)
	{
	}

	void Load(archive & ar);
	void Save(archive & ar);

	void SendSummonInfo(gplayer_imp * imp);
	bool CheckSummon(gplayer_imp *imp, ruid_t friend_id);	//检查可否召唤
	bool CountSummon(gplayer_imp * imp, ruid_t friend_id);	//修正召唤次数 
	void RollbackSummon(gplayer_imp *imp, ruid_t friend_id);//召唤失败后的数字修正
	void Reset(gplayer_imp * imp);
};*/

class revive_data
{
	int stand_revive_times;		//原地复活次数
	int stand_revive_times_lianxu;	//连续原地复活次数
	int perfect_revive_times;	//完美复活次数
	int perfect_revive_times_lianxu;//连续完美复活次数
	int last_revive_type;		//上次复活的方式
public:
	enum{
		TPYE_REVIVE_NORMAL = 0,		//就是回复活点复活	
		TYPE_REVIVE_STAND,		//正常原地复活
		TYPE_REVIVE_PERFECT,
		TYPE_REVIVE_ALLIANCE,
		TYPE_REVIVE_NATION_WAR,
		//5
		TYPE_REVIVE_TEAM_INSTACNE,
		TYPE_REVIVE_RANDOM_POS_IN_INST,
		TYPE_REVIVE_CASH,		//花钱复活
		TYPE_REVIVE_DEBUG,		//调试命令
		TYPE_REVIVE_FAKE,		//死后续航
	};
	revive_data() { memset(this, 0, sizeof(*this)); }
	void Init(PB::db_player_misc & miscella)
	{
		auto * pdata = miscella.mutable_revive_data();
		stand_revive_times = pdata->stand_revive_times();
		stand_revive_times_lianxu = pdata->stand_revive_times_lianxu();
		perfect_revive_times = pdata->perfect_revive_times();
		perfect_revive_times_lianxu = pdata->perfect_revive_times_lianxu();
		last_revive_type = pdata->last_revive_type();
	}
	void Save(PB::db_player_misc & miscella)
	{
		auto * pdata = miscella.mutable_revive_data();
		pdata->set_stand_revive_times(stand_revive_times);
		pdata->set_stand_revive_times_lianxu(stand_revive_times_lianxu);
		pdata->set_perfect_revive_times(perfect_revive_times);
		pdata->set_perfect_revive_times_lianxu(perfect_revive_times_lianxu);
		pdata->set_last_revive_type(last_revive_type);

	}
	void DailyUpdate();
	int  GetReviveFee(int revive_type);
	void OnRevive(int revive_type);
	int  GetStandReviveTimes() const { return stand_revive_times; }
	int  GetStandReviveTimesLianxu() const { return stand_revive_times_lianxu; }
	int  GetPerfectReviveTimes() const { return perfect_revive_times; }
	int  GetPerfectReviveTimesLianxu() const { return perfect_revive_times_lianxu; }

};

struct creature_prop;
namespace GDB{
struct itemlist;
};
class  item;
int BuildCommonProperty( creature_prop & prop, int prof, int level, int prof_level, const void * prop_data, size_t prop_size, const void * shadow_equip, size_t se_size, 
					const GDB::itemlist * equip2, int (*item_callback) ( void * arg, item * , int index), void * arg, std::vector<int>& equip_skill);

item* GetPetBedge(const void * petdata,size_t petdata_size, const GDB::itemlist& pet_inv);

//COMMON_REP的定义类型
enum 
{
	PLAYER_CMREP_SWEEP_JUMP = 0,		//闯关跳关逻辑
};

enum
{
	TAC_DUEL_RESULT	= 1,
};

class task_after_changeworld_t
{
	struct _node_t
	{
		int _type;
		abase::octets _data;
	};
	std::vector<_node_t> _list;

public:
	void AddTask(int type, const abase::octets& data);
	void OnEnterWorld(gplayer_imp* imp);
	void UnSaveData(archive& ar);
	void LoadUnSave(archive& ar);
};

/*class player_universal_data
{
	std::map<int, abase::octets> _data_map;
	enum
	{
		MAGIC = 0x16e81d01
	};
public:
	void SetData(int key, raw_wrapper & ar);
	bool Save(raw_wrapper & ar);
	bool Load(raw_wrapper & ar);
	const abase::octets * GetData(int key);
};
enum
{
	//只能增加删除，不能重复使用index
	PUD_DIRECT_LOTTERY	= 0,
	PUD_PATA_DATA		= 1,
	PUD_VIP_INFO		= 2,
	PUD_BOT_CONFIG		= 3,
	PUD_BINGFA_INFO		= 4,
	PUD_CARD_INFO		= 5,
	PUD_STUNT_CONFIG	= 6,	//特技系统
	PUD_BLACK_MARKET	= 7,
	PUD_DUKE_INFO		= 8,
	PUD_HERO_TRIAL		= 9,
	PUD_CORP_ATTR		= 10,	//帮派附加
	PUD_CLIMB_TOWER		= 11,	
	PUD_CLIMBTOWER_SHOP	= 12,	//闯天关的神秘商店
	PUD_MIDAS_CASH		= 13,	//米大师钻石信息
	PUD_PLANT_DATA          = 14,   //种植
	PUD_RETRIEVE_DATA	= 15,
	PUD_WUHUN_INFO		= 16,	//武魂数据
	PUD_FASHION		= 17,	//时装
	PUD_CORP_CACHE		= 18,	//帮派缓存
	PUD_TALISMAN_INFO	= 19,	//法宝数据
	PUD_RETINUE_GROUP_INFO	= 20,	//随从组合数据
	PUD_RETINUE_INFO	= 21,	//随从数据
	PUD_PRACTICE_INFO	= 22,	//修炼数据
	PUD_EXP_LOTTERY		= 23,	//客栈经验
	PUD_PARK_INFO		= 24,	//乐园相关
	PUD_ARENA_GROUP		= 25,	//跨服使用的战队信息
	PUD_BABY_FASHION	= 26,	//孩子时装
	PUD_UNLOCKED_PHOTOIDS = 27, //解锁的头像列表
	PUD_MAFIA			= 28,	//帮派信息，给跨服使用
	PUD_COUNTER			= 29,	//计数器
	PUD_SWITCH			= 30,	//开关
	PUD_ENHANCE			= 31,	//部位强化数据
	PUD_KOTODAMA		= 32,	//言灵
};*/

enum VIP_LEVEL_CONFIG_INDEX
{
	VIP_EXP_NEED 		= 1,	//需要VIP点数
	VIP_AUTO_CLIMB_TOWER 	= 2,	//扫荡	
	VIP_LEVEL_REWARD	= 3,	//VIP礼包
	VIP_TASK_REFRESH1	= 4,	//帮派库任务刷橙色
	VIP_TASK_REFRESH_MONEY1	= 5,
	VIP_TASK_REFRESH_YUANBAO1 = 6,
	VIP_TASK_REFRESH2       = 7,//伙伴任务刷橙色
	VIP_TASK_REFRESH_MONEY2 = 8,
	VIP_TASK_REFRESH_YUANBAO2 = 9,
	VIP_TASK_REFRESH3       = 10,    
	VIP_TASK_REFRESH_MONEY3 = 11,
	VIP_TASK_REFRESH_YUANBAO3 = 12,
	VIP_TASK_REFRESH4       = 13,            
	VIP_TASK_REFRESH_MONEY4 = 14,
	VIP_TASK_REFRESH_YUANBAO4 = 15,
	VIP_TASK_REFRESH5       = 16,            
	VIP_TASK_REFRESH_MONEY5 = 17,
	VIP_TASK_REFRESH_YUANBAO5 = 18,
	VIP_RETRIEVE_LIMIT	= 21,
	VIP_MALL			= 22,	// 商城
	VIP_MONTH_SIGN_IN = 23,	//vip奖励月补签次数
	VIP_SIGN_IN		= 24,	//vip每日签到额外奖励
	//25-30存在
	VIP_BUY_REWARD_LIMIT	= 31,	//vip购买奖励限制
	VIP_LOTTERY_100 	= 32,	//彩票100连抽(客户端用）
	VIP_LEYUAN_100		= 33, 	//乐园百连抽
	//34-35存在
	VIP_ZHENBAO_100		= 36,	//珍宝阁百连抽,客户端用
	//37-67存在
	VIP_BN_BOX          = 68,   //大富翁允许激活宝箱栏位
	VIP_DAILY_GIFTBAG						= 69,	// 每日礼包
	VIP_WEEKLY_GIFTBAG						= 70,	// 每周礼包
	VIP_ACTIVE_FASHION_COLOR_ONE_BUTTON_UP	= 71,	// 一键开启时装所有色块
	VIP_MOUNT_TRAIN_SURFACE_ONE_BUTTON_UP	= 72,	// 一键进化座驾十次
	VIP_LADDER_CHALLENGE_COUNT				= 73,	// 屠龙考核挑战次数
	VIP_LADDER_PURCHASE_COUNT				= 74,	// 屠龙考核购买次数
	VIP_LADDER_PURCHASE_PRICE				= 75,	// 屠龙考核购买价格
	VIP_ENABLE_IDENTIFY_ORANGE_LONGYU = 76, // 启用鉴定装备出橙色龙语
};

int GetVIPLevelAmend(int config, int level, int init_value = 0);
bool GetVIPLevelValue(int vip_order_id, int level, int& value /*Out*/);

class player_vip_info
{
	PB::gp_vip_info data;
public:
	player_vip_info() {}
	PB::gp_vip_info& Data() { return data; }
	//vip设置
	bool UpdateRewardVIP(gplayer_imp* imp, int vip, int timeout);
	void UpdateVIPLevel(gplayer_imp* imp, int recharge_exp, bool isRecharge);
	unsigned char GetVIPLevel() const { return data.cur_vip_level(); }
	int GetVIPExp(gplayer_imp* imp) const;
	int CheckPlayerBuyRepu(gplayer_imp* imp, int repu, int offset);
	void OnPlayerOperation(gplayer_imp*imp, const PB::gp_vip_operation& cmd, int cost_retcode = -1, unsigned int cost_value = 0);
	void DailyUpdate(gplayer_imp *imp, int count);
	void DailyUpdate6(gplayer_imp *imp, int count);
	void Update(gplayer_imp* imp, time_t cur_t);
	void UpdateRewardLevel(gplayer_imp* imp);
	static bool LoadVIPConfig(const char* file);
	void OnSave(gplayer_imp* imp);
	void OnLogin(gplayer_imp* imp);
	bool Save(gplayer_imp* imp, PB::player_universal_data_t& pud);
	bool Load(gplayer_imp* imp, const PB::player_universal_data_t& pud);
	//发送数据
	void BroadcastVIPLevel(gplayer_imp* imp);	//广播
	void SendVIPInfo(gplayer_imp* imp);	//单人
	//vip修正
	int GetAmend(int config, int init_value = 0) const
	{
		return GetVIPLevelAmend(config, data.cur_vip_level(), init_value);
	}
};

class plat_vip_manager
{
private:
	typedef std::vector<int> AMEND;
	typedef std::vector<AMEND > PLAT_VIP_INFO;
	
	PLAT_VIP_INFO platVIPInfo;
	bool active;
public:
	plat_vip_manager() : active(false) {}
	static plat_vip_manager& Instance()
	{
		static plat_vip_manager _instance;
		return _instance;
	}
	bool LoadConfig(const char* file);
	int GetAmend(int index, int entrance = PLAT_VIP_NULL, int VIPKind = PLAT_VIP_NULL);
};

#define PLAYER_SWITCH_VERSION 0x02
//#define PLAYER_WECHAT_SWITCH_TYPE 5	//客户端微信聊天开关类型，这个值只允许客户端来修改
//#define PLAYER_CG_SWITCH_TYPE 101	//CG开关类型
//#define SERVER_SWITCH_TYPE 100	//服务器程序使用的开关类型，不允许在任务、关卡等地方修改
//#define SERVER_SWITCH_INDEX_YESTERDAY_ACTIVE 1	//昨日活跃玩家
//#define SERVER_SWITCH_INDEX_RETINUE_ACTIVE_SSS_CONFIRMED 2 //客户端是否清理过可发送状态的红点提示
//#define SERVER_SWITCH_INDEX_SOLUTION 3 // 总方案功能是否开启
//#define PLAYER_ABTEST_SWITCH_TYPE 102
//#define PLAYER_CAREER_STAR_TOPIC_TYPE 103 // 明星身份，热搜激活id列表
//#define PLAYER_ABTEST_INIT_INDEX 1
//#define PLAYER_SYSTEM_SWITCH_TYPE 4
//#define PLAYER_FACELIFT_SWITCH_ID 1
//#define PLAYER_SYSTEM_SWITCH_INDEX_BODY_CHANGED 10 // 是否已经转换过体型
#define PLAYER_PUBG_MADEL_SWITCH_TYPE 104 // type: 吃鸡勋章
#define PLAYER_RECHARGE_ACT_SWITCH_TYPE 105 // 付费动作解锁开关

//开关，可以理解为微型计数器，只存0和1，用bitmap
class player_switch
{
	typedef abase::bitmap<65536> PLAYER_SWITCH_MAP;
	typedef std::unordered_map<unsigned short, PLAYER_SWITCH_MAP> PLAYER_SWITCH_ALL_MAP;
	PLAYER_SWITCH_ALL_MAP switchs;
	gplayer_imp* _imp;

public:
	explicit player_switch(gplayer_imp* imp):_imp(imp) { }
	void Load(gplayer_imp* imp, const PB::player_universal_data_t& pud)
	{
		if (!pud.has_switch_data() || pud.switch_data().size() == 0)
		{
			return;
		}
		raw_wrapper ar(pud.switch_data().c_str(), pud.switch_data().size());
		unsigned char version = 0;
		ar >> version;
		if (version != PLAYER_SWITCH_VERSION)
		{
			return;
		}
		unsigned short count = 0, size = 0, type = 0;
		ar >> count;
		for (size_t i = 0; i < count; ++ i)
		{
			ar >> type;
			ar >> size;
			PLAYER_SWITCH_MAP & switch_map = switchs[type];
			switch_map.init((unsigned char *)ar.cur_data(), size);
			ar.shift(size);
		}
	}

	void Save(gplayer_imp* imp, PB::player_universal_data_t& pud) const
	{
		raw_wrapper ar(128);
		unsigned char version = PLAYER_SWITCH_VERSION;
		ar << version;
		ar << (unsigned short)switchs.size();
		for (auto it = switchs.begin(), eit = switchs.end(); it != eit; ++ it)
		{
			ar << it->first;
			size_t size = 0;
			const unsigned char *data = it->second.data(size);
			ar << (unsigned short)size;
			ar.push_back((const void *)data, size);
		}
		pud.set_switch_data(ar.data(), ar.size());
	}

	bool Get(int type, int index)
	{
		auto it = switchs.find(type);
		if (it == switchs.end())
			return false;
		//bitmap里有判断index的范围，超了的话返回false
		return it->second.get(index);
	}

	bool Set(int type, int index, bool open);

	bool ClientSet(int index, bool open)
	{
		if (index < 0 || index > 65535)
		{
			return false;
		}
		PLAYER_SWITCH_MAP & switch_map = switchs[PLAYER_WECHAT_SWITCH_TYPE];
		switch_map.set(index, open);
		return true;
	}

	bool ServerSet(int index, bool open)
	{
		if (index < 0 || index > 65535)
		{
			return false;
		}
		PLAYER_SWITCH_MAP & switch_map = switchs[SERVER_SWITCH_TYPE];
		switch_map.set(index, open);
		return true;
	}

	bool CGSet(int index, bool open)
	{
		if (index < 0 || index > 65535)
		{
			return false;
		}
		PLAYER_SWITCH_MAP & switch_map = switchs[PLAYER_CG_SWITCH_TYPE];
		switch_map.set(index, open);
		return true;
	}

	inline void ForEach(std::function<void(unsigned short, const unsigned char *, size_t)> func)
	{
		for (auto it = switchs.begin(), eit = switchs.end(); it != eit; ++ it)
		{
			size_t size = 0;
			const unsigned char *data = it->second.data(size);
			func(it->first, data, size);
		}
	}

public:
	using LISTENER = std::function<void(int, int, bool)>;
	inline void RegisterListener(int type, int index, LISTENER&& listener)
	{
		_listeners[type][index] = std::move(listener);
	}

private:
	using TYPE_LISTENER = std::unordered_map<int, LISTENER>;
	using ALL_LISTENER = std::unordered_map<int, TYPE_LISTENER>;
	ALL_LISTENER _listeners;
};

//计数器，玩家身上的简化版的声望，只存一些简单数据
class player_counter_manager
{
private:
	std::map<int, unsigned char> counter_config;
public:
	static player_counter_manager& GetInstance()
	{
		static player_counter_manager _instance;
		return _instance;
	}
	bool LoadFromScript();
	unsigned char GetLimit(int id) const
	{
		auto it = counter_config.find(id);
		if(it != counter_config.end())
			return it->second;

		return UINT8_MAX;
	}
};

class player_counter
{
	unsigned char counter[GNET::COUNTER_MAX];

public:
	player_counter()
	{
		memset(counter, 0, sizeof(counter));
	}

	void Load(gplayer_imp* imp, const PB::player_universal_data_t& pud);

	void Save(gplayer_imp* imp, PB::player_universal_data_t& pud) const
	{
		pud.set_counter_data(counter, GNET::COUNTER_MAX);
	}

	inline unsigned char Modify(int id, int offset)
	{   
		if (id < 0 || id >= GNET::COUNTER_MAX)
		{
			return 0;
		}
		if (offset < -UINT8_MAX || offset > UINT8_MAX)
		{
			return counter[id];
		}

		unsigned char val = (unsigned char)abs(offset);
		if (offset > 0)
		{
			unsigned char limit = player_counter_manager::GetInstance().GetLimit(id);
			if(limit == UINT8_MAX)
			{
				val += counter[id];
				if(val < counter[id])
				{
					val = UINT8_MAX;
				}
			}
			else
			{
				if(limit - counter[id] < val)
				{
					val = limit;
				}
				else
				{
					val += counter[id];
				}
			}
		}   
		else
		{   
			val = counter[id] - val;
			if(val > counter[id])
			{
				val = 0;
			}
		}   
		counter[id] = val;
		return val;
	}   

	inline unsigned char Set(int id, unsigned char val)
	{   
		if (id < 0 || id >= GNET::COUNTER_MAX)
		{
			return 0;
		}
			
		unsigned char limit = player_counter_manager::GetInstance().GetLimit(id);
		if (val > limit)
		{
			val = limit;
		}
		counter[id] = val;
		return val;
	}   

	inline unsigned char Get(int id) const
	{   
		if (id < 0 || id >= GNET::COUNTER_MAX)
		{
			return 0;
		}
		return counter[id];
	}   

	inline const unsigned char* GetData()
	{   
		return counter;
	}   
	inline size_t GetCount()
	{
		return GNET::COUNTER_MAX;
	}
};

class equipment_suit_manager
{
	struct _suit_template_info
	{
		int suit_id;
		int addon_group;
		int gfx_id;
		int bp_id;
		int kotodama_index; // 言灵index
	};
	typedef abase::hash_map<int, struct _suit_template_info> SUIT_MAP;
	SUIT_MAP quality_suit_map;
	SUIT_MAP star_suit_map;
	SUIT_MAP child_suit_map;
	SUIT_MAP gem_suit_map;
	SUIT_MAP spirit_gem_suit_map;
	SUIT_MAP enhance_suit_map;

	typedef std::map<int, int,std::greater<int>> SUIT_CONFIG;
	std::map<int, int> star_config[2];
	SUIT_CONFIG child_config;
	SUIT_CONFIG enhance_config;
	SUIT_CONFIG gem_config;
	SUIT_CONFIG spirit_gem_config;
	int level_config[4][5][2];	//[level:1-4][quality:1-5][count:5,10]

	typedef std::map<int/*suit_tid*/, int/*level*/> SUIT_LEVEL_MAP;
	SUIT_LEVEL_MAP gem_suit_level_map;
	SUIT_LEVEL_MAP spirit_gem_suit_level_map;

public:
	static equipment_suit_manager & GetInstance() { static equipment_suit_manager rhs; return rhs; }
	bool LoadTemplate(const MIX_SUITE_CONFIG & config);
	bool LoadSuitConfig();
	int GetStarSuit(int star_level, int count, int & addon_group, bool & has_gfx);
	int GetQualitySuit(int level, int quality, int count, int & add_group);
	int GetChildSuitAddon(int child_level) const;

	int GetGemSuit(int gem_level, int & addon_group, int & gfx_id, int * kotodama_index = nullptr) const;
	void GetGemSuitAddonGroupsBetween(int gem_level_lower, int gem_level_upper,
		std::vector<int> & addon_groups) const; // 获取(lower, upper]范围内的附加属性组
	void GetSpiritGemSuitAddonGroupsBetween(int gem_level_lower, int gem_level_upper,
		std::vector<int> & addon_groups) const; // 获取(lower, upper]范围内的附加属性组
	int GetGemSuitLevel(int gem_suit_tid) const; // 获取宝石套装模板的解锁等级 找不到则返回INT_MAX

	int GetEnhanceSuit(int level, int & addon_group, bool & has_gfx) const;
	void GetEnhanceSuitAddonGroupsBetween(int level_lower, int level_upper,
		std::vector<int> & addon_groups) const; // 获取(lower, upper]范围内的附加属性组
};

class player_suit_info
{
	//int _quality_suit_id;
	//int _quality_suit_addon_group;
	//int _star_suit_level;
	//int _star_suit_id;
	//int _star_suit_addon_group;

	void GetStarSuitInfo(gplayer_imp * imp, int & star_suit_level, int & star_suit_count);
public:
	player_suit_info() /*: _quality_suit_id(0), _quality_suit_addon_group(0), _star_suit_level(0), _star_suit_id(0), _star_suit_addon_group(0)*/ {}
	void InitSuitInfo(gplayer_imp * imp);
	void UpdateSuitInfo(gplayer_imp * imp);
	bool UpdateQualitySuit(gplayer_imp * imp);
	bool UpdateStarSuit(gplayer_imp * imp, bool init);
	//int GetQualitySuitID() { return _quality_suit_id; }
	//int GetStarSuitID() { return _star_suit_id; }
	int SelectStarGFXSuit(gplayer_imp *imp, int star);
};

class creature_enhance_if;
//在线addon，换线保存，上线重新添加不存盘的属性
class player_corps_attribute
{
	typedef std::vector<int> ADDONS;
	typedef std::vector<ADDONS> LEVEL_ADDONS;	//每个等级的addon
	PB::corp_attribute _attr;
	struct corps_addons
	{
		bool _active;
		LEVEL_ADDONS _addons;
		corps_addons() : _active(false) {}
	} _corps_addons;
	PB::player_corps_attr_config_t player_config;
	PB::player_corps_cache_t corps_cache;
	int join_time;
	int lastweek_pos;
	int fighter_pos;
	int m_RentCost[2];

	void Active(const creature_enhance_if& pef);
	void DeActive(const creature_enhance_if& pef);
public:
	player_corps_attribute()
	{
		join_time = 0;
		lastweek_pos = 0;
		fighter_pos = 0;
		memset(m_RentCost, 0, sizeof(m_RentCost));
	}
	void LoadUnSave(gplayer_imp* imp, PB::ipt_role_unsave_data& pb);
	void UnSaveData(gplayer_imp* imp, PB::ipt_role_unsave_data& pb);
	void OnEnterScene(gplayer_imp* imp);
	void OnUpdate(gplayer_imp* imp, const PB::corp_attribute& data);
public:
	void Load(gplayer_imp* imp, const PB::player_universal_data_t& pud);
	void Save(gplayer_imp* imp, PB::player_universal_data_t& pud) const;
	void PlayerCommand(gplayer_imp* imp, const PB::gp_corp_config& cmd);
	void SendConfig(gplayer_imp* imp);
	void OnPlayerLeaveCorp(gplayer_imp* imp);
	void OnPlayerJoinCorp(gplayer_imp* imp);

public:
	static const FACTION_PROP_CONFIG& GetTemplate()
	{
		return MAFIA_CONFIG;
	}
	static float GetPosProfitFactor(int pos);
	int GetCorpsShopRebate() const;
	int GetShopLevel() const;
	int GetCoffersLevel() const;
	int GetSkillRoomLevel() const;
	int64_t GetBonusLastWeek() const;
public:
	int64_t GetCorpReputation(int64_t key) const;
	void GetCorpWineRate(int& exp_rate_permille);	//经验千分比
	int PlayerUpgradeChariot(gplayer_imp* imp, const PB::gp_upgrade_chariot& cmd, int cost_retcode = 0, unsigned int cost_value = 0);
	void UpgradeChariotLimit(gplayer_imp* imp, int up_type);
	void UpdatePlayerCorpsInfo(gplayer_imp* imp, int joinTime, int lastweek_pos, int fighter_pos);
	int RentChariot(gplayer_imp* imp, int tid, int index);
	void RentChariotResult(gplayer_imp* imp, int tid, int level, int result);
	PB::player_corps_attr_config_t& GetPlayerCorpsAttrConfig() { return player_config; }
	PB::player_corps_cache_t& GetCorpsCache() { return corps_cache; }
	int GetLastWeekPos() { return lastweek_pos; }
	int GetFighterPos() { return fighter_pos; }
	void UpdateCorpsLuckyValue(gplayer_imp *imp);
};
class CorpsShopManager
{
private:
	std::map<int, int> sell_item;
	CorpsShopManager()
	{

	}
public:
	static CorpsShopManager& GetInstance()
	{
		static CorpsShopManager instance;
		return instance;
	}

	bool LoadFromScript();
	bool CanSell(int item_id, int shop_level) const
	{
		auto it = sell_item.find(item_id);
		if(it == sell_item.end())
		{
			return false;
		}
		if(it->second > shop_level)
		{
			return false;
		}
		return true;
	}
};
/*class player_corp_farm
{
	PB::db_corp_farm_data farm_data;
public:
	void OnPlayerLogin(gplayer_imp* imp);
	void Load(gplayer_imp* imp,player_universal_data& pud);
	void Save(gplayer_imp* imp,player_universal_data& pud);	
	void PlayerCommand(gplayer_imp* imp, const PB::gp_corp_farm_op& cmd);	//对应操作
	void SendConfig(gplayer_imp* imp, int index =  -1);
};*/

class ChariotTemplMan
{
public:
        CHARIOT_BUILD m_ChariotBuild;
        CHARIOT_RECEIVE m_ChariotReceive;

        typedef std::map<int, struct CHARIOT_ESSENCE> TEMP_MAP;
        TEMP_MAP m_ChariotEssenceMap;
        ChariotTemplMan() {}; 
public:
        static ChariotTemplMan & GetInstance() { static ChariotTemplMan rhs; return rhs; }    
        bool LoadTemplate(elementdataman& data_man);
};

class hejiu_config_manager
{
	struct hejiu_config
	{
		int nearby_npc_tid;
		int nearby_distance;
		int drink_exp;
		int drink_interval;
		int drink_count;
		int exp_adjust_config;
	};
	typedef abase::hash_map<int, hejiu_config> HEJIU_CONFIG_MAP;
	HEJIU_CONFIG_MAP _hejiu_config_map;
public:
	void LoadTemplate(const HEJIU_CONFIG & cfg);
	bool GetWineConfig(int config_id, int & npc_nearby, int & npc_distance, int & drink_interval, int & drink_exp);
	bool GetWineConfig(int config_id, int & drink_count);
	int GetWineExpCfgId(int config_id);
	static hejiu_config_manager& GetInstance()
	{
		static hejiu_config_manager _s_instance;
		return _s_instance;
	}
};

class equip_chaijie_config_manager
{
public:
	struct chaijie_config
	{
		float gain_material_count_odds[3] = {0};  
		struct 
		{
			int grant_reward_id = 0; 
			float odds = 0;   
		} gain_materials[8];
	};
	typedef abase::hash_map<int, chaijie_config> CHAIJIE_CONFIG_MAP;
	CHAIJIE_CONFIG_MAP _chaijie_config_map;
public:
	bool LoadTemplate(const EXTERIOR_DECOMPOSE_CONFIG& cfg);
	chaijie_config* GetChaiJieConfig(int config_id);
	void CheckAward();
	static equip_chaijie_config_manager& GetInstance()
	{
		static equip_chaijie_config_manager _s_instance;
		return _s_instance;
	}

};

/*class player_activity_man
{
public:
	static bool LoadConf(const char * file);
	void RereshPlayerActivity(gplayer_imp* imp);
};*/

class reward_template;
class player_retrieve_man
{
public:
	player_retrieve_man()
	{
	}
	//所有需要统计的副本
	static abase::static_set<int> _instance_set;
	//所有需要统计的通用使用限制
	static abase::static_set<int> _cmn_limit_set;
	//map<tid, activityID>
	static abase::static_map<int, int, 64> _tid_activity_list;
#ifdef USE_CONVEX 
	static abase::static_map<int, RECOVER_REWARD_CONFIG_CONFIGS, 64> recover_template;
	static const RECOVER_REWARD_CONFIG_CONFIGS* GetTemplate(int activity_id);
#else
	static abase::static_map<int, RECOVER_REWARD_CONFIG::ONE_CONFIG, 64> recover_template;
	//获取指定活动的模板数据
	static const RECOVER_REWARD_CONFIG::ONE_CONFIG* GetTemplate(int activity_id);
#endif
	//从模板读取配置数据,存储到recover_template，如果失败返回错误
	static bool LoadTemplate(const RECOVER_REWARD_CONFIG& cfg);

	static bool InstCanRetrieve(int tid)
	{
		return _instance_set.find(tid) != _instance_set.end();
	}
	static bool CmnlNeedRetrive(int tid)
	{
		return _cmn_limit_set.find(tid) != _cmn_limit_set.end();
	}
	static int GetActivityID(int tid)
	{
		abase::static_map<int, int, 64>::iterator itrFind = _tid_activity_list.find(tid);
		if (itrFind != _tid_activity_list.end())
		{
			return itrFind->second;
		}
		return 0;
	}

	//玩家的上次数据
	PB::retrieve_info_t data;
	//加载
	void Load(gplayer_imp* imp, const PB::player_universal_data_t& pud);
	//保存
	void Save(gplayer_imp* imp, PB::player_universal_data_t& pud) const;
	//玩家领奖
	int OnPlayerOperation(gplayer_imp* imp, const PB::gp_retrieve_op& op, int cost_retcode = 0, unsigned int cost_value = 0);
	// 玩家请求找回数据
	int OnPlayerRetrieveInfoReq(gplayer_imp* imp, const PB::gp_retrieve_info_req& req);
	//玩家登陆
	void OnLogin(gplayer_imp *imp);
	//发送全部数据
	void SendInfo(gplayer_imp *imp);
	//发送单条数据
	void SendOne(gplayer_imp *imp, const PB::retrieve_info_str& str);
	//刷新活动数据
	void RefreshRetrieve(gplayer_imp* imp, int type, int tid, int left_count, int timestamp, int activityID, bool notify = true);
	void DailyUpdate(gplayer_imp* imp);
	void UpdateInvalidTid();

	// 设置玩家奖励找回次数
	void InitRetrieveRemainNum(gplayer_imp *imp);
	void SetRetrieveRemainNum(gplayer_imp *imp, int common_num = -1, int vip_extra_num = -1);
	// 获取玩家奖励找回次数，返回总次数
	int GetRetrieveRemainNum(gplayer_imp *imp, bool is_vip_retrieve_open, int &common_num, int &vip_extra_num);

	// 设置找回活动周期性经验(为存储经验)
	void SetActivityFactorExp(gplayer_imp *imp, bool yesterday_online);
	// debug
	void DebugSetActivityFactorExp(gplayer_imp *imp, int64_t exp);

	// 设置隔天没登录经验
	void SetMultiNoLoginFactorExp(gplayer_imp *imp, int nologin_count);

	//老玩家回归海量经验活动
	void PlayerGetOnceReturnExp(gplayer_imp*imp); //玩家取得一次经验
	void CheckAndSendResetReturnMassiveExp(gplayer_imp*imp); // 检查经验是否需要重置并且发送
	void OnPlayerModifyReputation(gplayer_imp*imp, int repu_id);

public:
	static player_retrieve_man& GetInstance()
	{
		static player_retrieve_man _s_instance;
		return _s_instance;
	}

private:
	// 玩家领取全部奖励
	int OnPlayerRetrieveAll(gplayer_imp* imp, const PB::gp_retrieve_op& op, int cost_retcode = 0, unsigned int cost_value = 0);
	// 通过存储经验得到可找回次数
	void GetRetrieveCountByStoreExp(gplayer_imp* imp, const reward_template *pTemplate, int &leftRetrieveCount, int64_t &store_exp_add_max);
	// 领取找回活动周期性经验(为存储经验)
	int GetActivityFactorExp(gplayer_imp *imp);
	int CheckRetrieveLevel(gplayer_imp *imp, const RECOVER_REWARD_CONFIG_CONFIGS &config);
	// 活动控制，返回false表示不可领取
	bool CheckActivityIdControl(int activtiy_id_control);

	void ResetReturnMassiveExp(gplayer_imp*imp);// 老玩家回归海量经验重置经验
};


class player_red_packet
{
	ruid_t m_roleid;
	int m_tid;
	int m_timestamp;
	int m_timeout;
	int m_count;
	ruid_t m_src;
	unsigned char m_player_name_size;         //玩家名字长度
	char m_player_name_buf[MAX_PLAYER_NAME_LENGTH]; //玩家名字
	int m_heartbeat;

	void Init();
	int AddReward(gplayer_imp*, int reward_tid, int extra_tid, int speak_id);
	bool HasValidRedPacket(gplayer_imp*) const;
	void LogIngoreReward();
public:
	static static_vector<int, 64> red_packet_cmn_limits;
	static bool InsertCMNCheck(int cmnlimit, uint64_t* pmask)
	{
		if(cmnlimit <= 0) return true;
		for(int i = 0; i < (int)red_packet_cmn_limits.size(); i ++)
		{
			if(red_packet_cmn_limits[i] == cmnlimit)
			{
				if(pmask) *pmask = 1UL << i;
				return true;
			}
		}
		if(red_packet_cmn_limits.size() >= 64)
		{
			fprintf(stderr, "TOO MONEY Red Packet cmn use limit\n");
			return false;
		}
		red_packet_cmn_limits.push_back(cmnlimit);
		if(pmask)
		{
			*pmask = 1UL << (red_packet_cmn_limits.size() - 1);
			fprintf(stderr, "red packet cmnlimit:%d mask:%lx\n", cmnlimit, *pmask);
		}
		return true;
	}

	player_red_packet() : m_heartbeat(0) { Init(); } 
	~player_red_packet();
	void OnHeartbeat(gplayer_imp*);
	void OnReciveRedPacket(gplayer_imp*, const XID& src, int tid, int count, unsigned char name_sz, const char* name_buff);
	int OpenRedPacket(gplayer_imp*, bool system=false);
};


class PlayerCommonInterface
{
public:
	virtual ~PlayerCommonInterface() {}
	virtual void OnPlayerLogin(gplayer_imp* imp) {}
	virtual void OnPlayerLogout(gplayer_imp* imp) {}
	virtual void SendInfoOnEnterScene(gplayer_imp *imp) {}
	virtual void OnPlayerHeartbeat(gplayer_imp* imp) {}
	virtual void SendTotalData(gplayer_imp* imp) {}
	virtual void OnLoad(gplayer_imp* imp) {}
	virtual void OnSave(gplayer_imp* imp) {}
	bool Active();
};

class CommonInterfaceManager
{
	std::vector<PlayerCommonInterface* > _insterfaces;
public:
	
	static CommonInterfaceManager& Instance()
	{
		static CommonInterfaceManager _instance;
		return _instance;
	}
	bool InsertInterface(PlayerCommonInterface* inter)
	{
		if(!inter) return false;
		const std::string inter_name = typeid(*inter).name();
		for(auto it = _insterfaces.begin(); it != _insterfaces.end(); ++it)
		{
			PlayerCommonInterface *tmp = *it;
			if(typeid(*tmp).name() ==  inter_name)
				return false;
		}
		_insterfaces.push_back(inter);
		__PRINTF("CommonInterfaceManager Insert:%s with total:%zu\n", inter_name.c_str(), _insterfaces.size());
		return true;
	}
	virtual ~CommonInterfaceManager()
	{
		/*
		for(auto it = _insterfaces.begin(); it != _insterfaces.end(); ++it)
			delete (*it);
		*/
	}
	void RegisterAll();

public:
	void OnPlayerLogin(gplayer_imp* imp)
	{
		for(auto it = _insterfaces.begin(); it != _insterfaces.end(); ++it)
			(*it)->OnPlayerLogin(imp);
	}
	void OnPlayerLogout(gplayer_imp* imp)
	{
		for(auto it = _insterfaces.begin(); it != _insterfaces.end(); ++it)
			(*it)->OnPlayerLogout(imp);
	}
	void SendInfoOnEnterScene(gplayer_imp *imp)
	{
		for(auto it = _insterfaces.begin(); it != _insterfaces.end(); ++it)
			(*it)->SendInfoOnEnterScene(imp);
	}
	void OnPlayerHeartbeat(gplayer_imp* imp)
	{
		for(auto it = _insterfaces.begin(); it != _insterfaces.end(); ++it)
			(*it)->OnPlayerHeartbeat(imp);
	}
	/*
	void SendTotalData(gplayer_imp* imp)
	{
		for(auto it = _insterfaces.begin(); it != _insterfaces.end(); ++it)
			(*it)->SendTotalData(imp);
	}*/
	void OnLoad(gplayer_imp* imp)
	{
		for(auto it = _insterfaces.begin(); it != _insterfaces.end(); ++it)
			(*it)->OnLoad(imp);
	}
	void OnSave(gplayer_imp* imp)
	{
		for(auto it = _insterfaces.begin(); it != _insterfaces.end(); ++it)
			(*it)->OnSave(imp);
	}
};

class PlayerMultiExp : public PlayerCommonInterface
{
	PlayerMultiExp() {}
public:
	static PlayerMultiExp& Interface()
	{
		static PlayerMultiExp _instance;
		return _instance;
	}
	virtual void OnPlayerLogin(gplayer_imp* imp) override;
	virtual void OnSave(gplayer_imp* imp) override;
	virtual void SendInfoOnEnterScene(gplayer_imp *imp) override;
	virtual void OnPlayerHeartbeat(gplayer_imp* imp) override;
	virtual void SendTotalData(gplayer_imp* imp) override;

	PB::gp_multi_exp& Data(gplayer_imp *imp);
	void ReCalTimeout(gplayer_imp *imp);
	int SetMultiData(gplayer_imp* imp, int rate, int sec, int tid);
};

/*
class PlayerRefuseFight : public PlayerCommonInterface
{
	PlayerRefuseFight() {}
public:
	static PlayerRefuseFight& Interface()
	{
		static PlayerRefuseFight _instance;
		return _instance;
	}
	virtual void SendInfoOnEnterScene(gplayer_imp *imp) override;
	virtual void OnPlayerHeartbeat(gplayer_imp* imp) override;
	virtual void SendTotalData(gplayer_imp* imp) override;

	void CheckStatus(gplayer_imp *imp);
	PB::gp_refuse_fight& Data(gplayer_imp *imp);
	int SetData(gplayer_imp* imp, int sec, int tid, int cancel_in_war);
	int PlayerEnd(gplayer_imp* imp);
};
*/

class WishTreeTemplate
{
public:
	struct FruitInfo
	{
		int weight = 0;
		EXP_SHOPSELL_MODE income_type = EXPSHOPSELLMODE_MUSTBE_BOUNDCASH;
		int income_value = 0;
		int speak_id = 0;
	};
	typedef std::vector<FruitInfo> FruitList;

	static WishTreeTemplate& Instance()
	{
		static WishTreeTemplate _instance;
		return _instance;
	}

	bool Load();
	int GetDayOffset(); 
	bool CanIrrigate() { return GetDayOffset() < irrigate_days; }
	bool CanHarvest() { return !CanIrrigate(); }
	int GetRandomFruit(int& speakID);
	FruitInfo* GetFruitByID(int id);

	int irrigate_wday_begin = 0;
	int irrigate_wday_end = 0;
	int irrigate_days = 0;
	EXP_SHOPSELL_MODE  irrigate_cost_type = EXPSHOPSELLMODE_BOUNDMONEY_FIRST;
	int irrigate_cost_value = 0;

	int daily_irrigate_count = 0;
	int fruit_total_weight = 0;
	int activity_id = 0;

	FruitList fruit_list;
};

class PlayerWishTree : public PlayerCommonInterface
{
	PlayerWishTree() : _last_check_time(0) {}
public:
	static PlayerWishTree& Interface()
	{
		static PlayerWishTree _instance;
		return _instance;
	}
	virtual void SendInfoOnEnterScene(gplayer_imp *imp) override;
	virtual void SendTotalData(gplayer_imp* imp) override;
	virtual void OnPlayerHeartbeat(gplayer_imp* imp) override;

	PB::wish_tree_data_t& Data(gplayer_imp *imp);
	void DailyUpdate(gplayer_imp *imp, int day_interval);

	int Irrigate(gplayer_imp* imp, int& fruit);
	int Harvest(gplayer_imp* imp);
	void Debug(gplayer_imp* imp, int cmd, int arg);

private:
	bool VerifyCheckTime();

	time_t _last_check_time;
};


class player_park_info
{
	PB::db_player_park_data _park_data;

	void CheckParkInfo(gplayer_imp* imp);
public:
	player_park_info()
	{
	}
	bool LoadScript();
	void OnPlayerLogin(gplayer_imp* imp);
	void Load(gplayer_imp* imp, const PB::player_universal_data_t& pud);
	void Save(gplayer_imp* imp, PB::player_universal_data_t& pud) const;	
	bool GetDoubleState() const { return _park_data.double_status(); }
	void SetDoubleState(bool st) { _park_data.set_double_status(st); }
};

class player_dice_card_info 
{
	PB::db_player_dice_card_data card_data;

public:
	void OnPlayerLogin(gplayer_imp* imp);
	void Load(gplayer_imp* imp, const PB::player_universal_data_t& pud);
	void Save(gplayer_imp* imp, PB::player_universal_data_t& pud);	
	void AddPos(gplayer_imp* imp, int card_id, int pos, int itemid, int item_index);
	int GetPos(int card_id, int pos);
	void SendData(gplayer_imp* imp, int card_id);
	int GetCardRewardFlag(gplayer_imp* imp, int card_id);
	void SetCardRewardFlag(gplayer_imp* imp, int card_id, int reward_flag);

	void DebugClearData(gplayer_imp* imp, int card_id);
};

// 活动彩票声望
typedef std::map<int, std::tuple<int, std::set<int>>> ACTIVITY_TICKET_REPU_MAP;
class activity_ticket_repu_manager
{
public:
	activity_ticket_repu_manager() {}

	static activity_ticket_repu_manager& Instance()
	{
		static activity_ticket_repu_manager instance;
		return instance;
	}

public:
	bool Load();
	const ACTIVITY_TICKET_REPU_MAP& GetActivityTicketRepu()
	{
		return m_data;
	}

protected:
	bool LoadConf(ACTIVITY_TICKET_VALUE_KEY atvk, const std::string& path, const std::string& table_name);

public:
	ACTIVITY_TICKET_REPU_MAP	m_data;
};

// 新彩票
class player_ticket_info
{
	PB::db_player_ticket_info info_data;

	// 绘梨衣的游戏机伤害记录
	PB::gp_video_game_info hurt_records;

public:
	void Load(gplayer_imp* imp, const PB::player_universal_data_t& pud);
	void Save(gplayer_imp* imp, PB::player_universal_data_t& pud);	

// 彩票声望版本
public:
	bool ClearTicketRepu(gplayer_imp* imp, int key, int old_value, int value);
	void ClearTicketRepuVersion(gplayer_imp* imp);

// 活动彩票声望
public:
	bool ClearActivityTicketRepu(gplayer_imp* imp, int key, int old_activity_id, int activity_id, const std::set<int>& repu_id);
	void ClearActivityTicketRepu(gplayer_imp* imp);

// 绘梨衣的游戏机
public:
	void VideoGameSendInfoData(gplayer_imp* imp, int activity_id, const std::vector<lottery_prize_item_t> &reward = std::vector<lottery_prize_item_t>());
	bool VideoGameGetInfoData(int activity_id, int &big_level, int &small_level, std::vector<int>& small_level_hp, std::vector<int>& level_award, int &total_draw_count, std::vector<int> &baodi_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts);
	bool VideoGameGetInfoData(int activity_id, int &total_draw_count, int64_t &total_draw_count_has_get);
	void VideoGameSetLevel(gplayer_imp* imp, int activity_id, int big_level, int small_level);
	void VideoGameSetTotalDrawCount(gplayer_imp* imp, int activity_id, int total_draw_count);
	void VideoGameSetTotalDrawCountHasGet(gplayer_imp* imp, int activity_id, int64_t total_draw_count_has_get);
	void VideoGameInitSmallLevelHp(gplayer_imp* imp, int activity_id, const std::vector<int> &small_level_hp_list);
	void VideoGameSetSmallLevelHp(gplayer_imp* imp, int activity_id, int small_level, int small_level_hp);
	void VideoGameSetLevelAward(gplayer_imp* imp, int activity_id, int big_level, int level_award);
	void VideoGameInitFanjinliInfo(gplayer_imp *imp, int activity_id, const std::vector<int> &fanjinli_info);
	void VideoGameSetFanjinliInfo(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);
	void VideoGameAddHurtRecord(gplayer_imp* imp, int activity_id, int big_level, int small_level, int hurt_type, int skill_index, int hurt);
	void VideoGameSetBaodiInfo(gplayer_imp* imp, int activity_id, int count_min, int count_max);

// 巨龙宝库
public:
	bool DragonHouseDoubleRate(gplayer_imp* imp, int activity_id, int tid, PB::DRAW_TYPE dt, int repu_id, int double_rate, int double_threshold);
	void DragonHouseSendInfoData(gplayer_imp* imp, int activity_id);
	bool DragonHouseGetInfoData(int activity_id, int &total_draw_count, std::vector<int> &baodi_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts);
	bool DragonHouseGetInfoData(int activity_id, int &total_draw_count, int64_t &total_draw_count_has_get);
	void DragonHouseSetTotalDrawCount(gplayer_imp* imp, int activity_id, int total_draw_count);
	void DragonHouseSetTotalDrawCountHasGet(gplayer_imp* imp, int activity_id, int64_t total_draw_count_has_get);
	void DragonHouseInitFanjinliInfo(gplayer_imp *imp, int activity_id, const std::vector<int> &fanjinli_info);
	void DragonHouseSetFanjinliInfo(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);
	void DragonHouseSetBaodiInfo(gplayer_imp* imp, int activity_id, int count_min, int count_max);

// 王之宝库
public:
	bool KingHouseGetInfoData(int activity_id, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts);
	void KingHouseInitFanjinliInfo(gplayer_imp *imp, int activity_id, const std::vector<int> &fanjinli_info);
	void KingHouseSetFanjinliInfo(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);

// 春节彩票 
	void SpringSendInfoData(gplayer_imp* imp, int activity_id);
	bool SpringGetInfoData(int activity_id, int &total_draw_count, std::vector<int> &baodi_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts);
	bool SpringGetInfoData(int activity_id, int &total_draw_count, int64_t &total_draw_count_has_get);
	void SpringGetInfoData(int activity_id, std::vector<int>& params, int64_t &inner_award_has_get);
	void SpringSetTotalDrawCount(gplayer_imp* imp, int activity_id, int total_draw_count);
	void SpringSetTotalDrawCountHasGet(gplayer_imp* imp, int activity_id, int64_t total_draw_count_has_get);
	void SpringInitFanjinliInfo(gplayer_imp *imp, int activity_id, const std::vector<int> &fanjinli_info);
	void SpringSetFanjinliInfo(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);
	void SpringSetBaodiInfo(gplayer_imp* imp, int activity_id, int count_min, int count_max);
	void SpringSetParams(gplayer_imp* imp, int activity_id,  std::vector<int>& params, std::vector<int>& re);
	void SpringSetInnerAwardHasGet(gplayer_imp* imp, int activity_id, int64_t inner_award_has_get);
	void SpringSetDecodeCount(gplayer_imp* imp, int activity_id, int success_count, const std::map<int, std::vector<int>> &weights, const std::map<int, std::vector<int>> &values);
	void SpringModifyDecodeCount(gplayer_imp* imp, int activity_id, int success_count);
	bool SpringCanInner(int activity_id, int draw_count);
	void SpringDebugParams(int activity_id, int type, int param1);

// 趣味夺宝
	void PleasureSendInfoData(gplayer_imp* imp, int activity_id);
	bool PleasureGetInfoData(int activity_id, int &total_draw_count, std::vector<int> &baodi_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts);
	bool PleasureGetInfoData(int activity_id, int &total_draw_count, int64_t &total_draw_count_has_get);
	void PleasureSetTotalDrawCount(gplayer_imp* imp, int activity_id, int total_draw_count);
	void PleasureSetTotalDrawCountHasGet(gplayer_imp* imp, int activity_id, int64_t total_draw_count_has_get);
	void PleasureInitFanjinliInfo(gplayer_imp *imp, int activity_id, const std::vector<int> &fanjinli_info);
	void PleasureSetFanjinliInfo(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);
	void PleasureSetBaodiInfo(gplayer_imp* imp, int activity_id, int count_min, int count_max);

	// 神迹探索
	void GodExploreSendInfoData(gplayer_imp* imp, int activity_id);
	bool GodExploreGetInfoData(int activity_id, int& opt_reward_id, int& level, int& level_count, int &total_draw_count, std::vector<int> &baodi_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts);
	bool GodExploreGetInfoData(int activity_id, int &total_draw_count, int64_t &total_draw_count_has_get);
	bool GodExploreGetInfoData1(int activity_id, int& level, int& level_count, int& total_draw_count);
	void GodExploreSetTotalDrawCount(gplayer_imp* imp, int activity_id, int total_draw_count);
	void GodExploreSetTotalDrawCountHasGet(gplayer_imp* imp, int activity_id, int64_t total_draw_count_has_get);
	void GodExploreInitFanjinliInfo(gplayer_imp *imp, int activity_id, const std::vector<int> &fanjinli_info);
	void GodExploreSetFanjinliInfo(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);
	void GodExploreSetBaodiInfo(gplayer_imp* imp, int activity_id, int count_min, int count_max);
	void GodExploreSetLevelAndDrawCount(gplayer_imp* imp, int activity_id,  int level, int draw_count);
	void GodExploreSelectLevelReward(gplayer_imp* imp, int activity_id, int reward_idx, int ess_id);
	int GodExploreGetEvent(gplayer_imp* imp, int activity_id);
	void GodExploreSetEvent(gplayer_imp* imp, int activity_id, int event_id);

	// 缘金绮匣
	void CasketRefreshInner(gplayer_imp* imp, int activity_id, std::vector<int>& inner_boxes);
	void CasketSendInfoData(gplayer_imp* imp, int activity_id);
	int CasketRandBox(gplayer_imp* imp, int activity_id, int& pool_type, int& pb_idx, int& used_count);
	void CasketSetInnerBoxesInfo(gplayer_imp* imp, int activity_id, int idx, std::map<int, int> items);
	int CasketGetAllBox(gplayer_imp* imp, int activity_id, std::map<int, int>& idx_types, int& used_count);

	// 千寻奇遇
	void QxqySendInfoData(gplayer_imp* imp, int activity_id);
	bool QxqyGetInfoData(int activity_id, int& level, int& level_point, int &total_draw_count, std::vector<int> &baodi_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts, int& last_crit_draw_count);
	bool QxqyGetInfoData(int activity_id, int &total_draw_count, int64_t &total_draw_count_has_get);
	bool QxqyGetLevelData(int activity_id, int &level, int &point, int &total_draw_count);
	void QxqySetTotalDrawCount(gplayer_imp* imp, int activity_id, int total_draw_count);
	void QxqySetTotalDrawCountHasGet(gplayer_imp* imp, int activity_id, int64_t total_draw_count_has_get);
	void QxqySetLevelAndPoint(gplayer_imp *imp, int activity_id, int level, int level_point);
	void QxqySetMustCrit(gplayer_imp *imp, int activity_id, int crit);
	int  QxqyGetMustCrit(int activity_id);
	void QxqySetMustThrowDouble(gplayer_imp *imp, int activity_id, int throw_double);
	int  QxqyGetMustThrowDouble(int activity_id);

	void QxqyInitFanjinliInfo(gplayer_imp *imp, int activity_id, const std::vector<int> &fanjinli_info);
	void QxqySetFanjinliInfo(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);
	void QxqySetBaodiInfo(gplayer_imp* imp, int activity_id, int count_min, int count_max);
	int  QxqyGetEvent(int activity_id);
	void QxqySetEvent(gplayer_imp* imp, int activity_id, int event_id, int section_index);
	int  QxqySectionHasOccurEvent(int activity_id, int section_index);
	int  QxqyGetCritRate(int activity_id);
	void QxqySetCritRate(gplayer_imp* imp, int activity_id, int crit_rate);
	void QxqySetLastCritDrawCount(gplayer_imp* imp, int activity_id, int last_crit_draw_count);
	int  QxqyGetSupperCrit(int activity_id);
	void QxqySetSupperCrit(gplayer_imp* imp, int activity_id, int value);


	// 宝箱
	void ShareBoxSendInfoData(gplayer_imp* imp, int activity_id);
	bool ShareBoxGetInfoData(int activity_id, int &total_draw_count, std::vector<int> &baodi_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts, int& level, int &exp, int quality, int& add_draw_times_unlock_draw_count, int& last_draw_out_box_draw_times, int& today_draw_count, int& today_has_draw_out, int& draw_box_times);
	bool ShareBoxGetInfoData(int activity_id, int& total_draw_count, int64_t& total_draw_count_has_get);
	void ShareBoxInitFanjinliInfo(gplayer_imp *imp, int activity_id, int quality, const std::vector<int> &fanjinli_info);
	void ShareBoxSetFanjinliInfo(gplayer_imp* imp, int activity_id, int quality, int item_tid, int draw_count);
	void ShareBoxSetBaodiInfo(gplayer_imp* imp, int activity_id, int quality, int count_min, int count_max);
	void ShareBoxSetTotalDrawCount(gplayer_imp* imp, int activity_id, int quality, int total_draw_count);
	void ShareBoxSetTotalDrawCountHasGet(gplayer_imp* imp, int activity_id, int64_t total_draw_count_has_get);
	void ShareBoxSetAddDrawTimesUnlockDrawCount(gplayer_imp* imp, int activity_id, int add_draw_times_unlock_draw_count);
	void ShareBoxSetTreasureLevelAndExp(gplayer_imp* imp, int activity_id, int level, int exp);
	void ShareBoxAddBox(gplayer_imp* imp, int activity_id, int quality, int last_draw_out_box_draw_times);
	void ShareBoxSetTodayDrawCount(gplayer_imp* imp, int activity_id, int today_draw_count);
	PB::share_box_info* ShareBoxGetShareInfo(int activity_id);


	// 街头游戏机
	void LotteryMachineSendInfoData(gplayer_imp* imp, int activity_id);
	void LotteryMachineGetMailNeedData(gplayer_imp* imp, int activity_id, int& total_draw_count, int& last_draw_time, int& last_mail_time);
	void LotteryMachineSetLastMailTime(gplayer_imp* imp, int activity_id, int last_mail_time);
	bool LotteryMachineGetInfoData(int activity_id, int &total_draw_count, int64_t &total_draw_count_has_get, int& everyday_draw_count, int64_t& everyday_reach_reward_has_get);
	bool LotteryMachineGetInfoData(int activity_id, int &total_draw_count, std::vector<int> &baodi_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts, std::vector<int>& extra_item_tids, std::vector<int>& extra_last_draw_counts, int& has_draw_ratio_up_item, int& everyday_draw_count);
	void LotteryMachineSetTotalDrawCount(gplayer_imp* imp, int activity_id, int total_draw_count);
	void LotteryMachineSetTotalDrawCountHasGet(gplayer_imp* imp, int activity_id, int64_t total_draw_count_has_get);
	void LotteryMachineInitFanjinliInfo(gplayer_imp *imp, int activity_id, const std::vector<int> &fanjinli_info);
	void LotteryMachineSetFanjinliInfo(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);
	void LotteryMachineSetBaodiInfo(gplayer_imp* imp, int activity_id, int count_min, int count_max);
	void LotteryMachineSetExtraItemLastDrawCount(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);
	void LotteryMachineSetRatioUpItemDrawState(gplayer_imp* imp, int activity_id, int has_draw);
	void LotteryMachineSetEverydayDrawCount(gplayer_imp* imp, int activity_id, int draw_count);
	void LotteryMachineSetEverydayReachHasGet(gplayer_imp* imp, int activity_id, int64_t has_get);

	bool LotteryMachineGetInnerInfoData(int activity_id, int &total_draw_count, std::vector<int> &baodi_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts, std::vector<int>& has_draw_out_index, int& cur_rand_draw_count);
	void LotteryMachineSetInnerTotalDrawCount(gplayer_imp* imp, int activity_id, int total_draw_count, int cur_round_draw_count);
	void LotteryMachineAddInnerHasDrawOut(gplayer_imp* imp, int activity_id, int index);
	void LotteryMachineResetInnerHasDrawOut(gplayer_imp* imp, int activity_id);
	void LotteryMachineInitInnerFanjinliInfo(gplayer_imp *imp, int activity_id, const std::vector<int> &inner_fanjinli_info);
	void LotteryMachineSetInnerFanjinliInfo(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);
	void LotteryMachineSetInnerBaodiInfo(gplayer_imp* imp, int activity_id, int count_min, int count_max);

	// 英灵彩票
	bool HolyGhostDiceIsSpecialPoolInit(gplayer_imp* imp, int activity_id);
	void HolyGhostDiceRefreshSpecialPool(gplayer_imp* imp, int activity_id, int refresh_times, int no_crit_refresh_times, const std::vector<int>& special_pool);
	void HolyGhostDiceSendInfoData(gplayer_imp* imp, int activity_id);
	bool HolyGhostDiceSpeicalGetInfoData(int activity_id, int &total_draw_count, int64_t& has_get, std::vector<int>& reward_pool, int& cur_draw_count, int& refresh_times, int& no_crit_refresh_times);
	void HolyGhostDiceSpeicalSetTotalDrawCount(gplayer_imp* imp, int activity_id, int total_draw_count, int cur_draw_count);
	void HolyGhostDiceSpecialAddDrawOut(gplayer_imp* imp, int activity_id, int reward_pool_index);
	void HolyGhostDiceNormalSetBaodiInfo(gplayer_imp* imp, int activity_id, int count_min, int count_max);
	void HolyGhostDiceNormalInitFanjinliInfo(gplayer_imp *imp, int activity_id, const std::vector<int> &fanjinli_info);
	void HolyGhostDiceNormalSetTotalDrawCount(gplayer_imp* imp, int activity_id, int total_draw_count);
	void HolyGhostDiceNormalSetFanjinliInfo(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);
	bool HolyGhostDiceNormalGetTotalInfo(int activity_id, int &total_draw_count, int64_t& has_get);
	bool HolyGhostDiceNormalGetInfoData(int activity_id, int &total_draw_count, std::vector<int> &baodi_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts);
	void HolyGhostDiceNormalSetTotalDrawCountHasGet(gplayer_imp* imp, int activity_id, int64_t has_get);


	// 组队充值
	void TeamRechargeSetTotalDrawCount(gplayer_imp* imp, int activity_id, int total_draw_count);
	void TeamRechargeSetTotalDrawCountHasGet(gplayer_imp* imp, int activity_id, int64_t total_draw_count_has_get);
	void TeamRechargeInitFanjinliInfo(gplayer_imp *imp, int activity_id, const std::vector<int> &fanjinli_info);
	void TeamRechargeSetFanjinliInfo(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);
	void TeamRechargeSetBaodiInfo(gplayer_imp* imp, int activity_id, int count_min, int count_max);
	bool TeamRechargeGetInfoData(int activity_id, int &total_draw_count, std::vector<int> &baodi_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts);

	// 星海密藏
	void LotterySeaSendInfoData(gplayer_imp* imp, int activity_id);
	bool LotterySeaGetInfoData(int activity_id, int &total_draw_count, std::vector<int> &baodi_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts);
	bool LotterySeaGetInfoData(int activity_id, int &total_draw_count, int64_t &total_draw_count_has_get);
	void LotterySeaSetTotalDrawCount(gplayer_imp* imp, int activity_id, int total_draw_count);
	void LotterySeaSetTotalDrawCountHasGet(gplayer_imp* imp, int activity_id, int64_t total_draw_count_has_get);
	void LotterySeaInitFanjinliInfo(gplayer_imp *imp, int activity_id, const std::vector<int> &fanjinli_info);
	void LotterySeaSetFanjinliInfo(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);
	void LotterySeaSetBaodiInfo(gplayer_imp* imp, int activity_id, int count_min, int count_max);

	// 宾果彩票
	void BingoSendInfoData(gplayer_imp* imp, int activity_id, int func_code);
	bool BingoGetInfoData(int activity_id, int &total_draw_count, std::vector<int> &baodi_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts, std::vector<int>& slot_states, int& next_up_item_event_index, int& has_self_select_event, int& cur_card_index, int& has_open_all_event);
	bool BingoGetTotalInfo(int activity_id, int &total_draw_count, int64_t &has_get, int& cur_card_index);
	bool BingoIsCardPoolInit(gplayer_imp *imp, int activity_id);
	PB::bingo_info* BingoGetDiceData(int activity_id);

	void BingoSetTotalDrawCount(gplayer_imp* imp, int activity_id, int total_draw_count);
	void BingoSetTotalDrawCountHasGet(gplayer_imp* imp, int activity_id, int64_t total_draw_count_has_get);
	void BingoInitFanjinliInfo(gplayer_imp *imp, int activity_id, const std::vector<int> &fanjinli_info);
	void BingoSetFanjinliInfo(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);
	void BingoSetBaodiInfo(gplayer_imp* imp, int activity_id, int count_min, int count_max);
	void BingoOnRefreshCardReward(gplayer_imp* imp, int activity_id, int next_card_idx, const std::vector<int> &reward_item, const std::vector<int> &reward_item_count, const std::vector<int> &icon_set, const std::vector<int> &reward_item_speak_id);
	void BingoSetOpenedSlot(gplayer_imp* imp, int activity_id, int open_index);
	void BingoSetUpItemEvent(gplayer_imp* imp, int activity_id, int pool_idx);
	void BingoSetSelfSelectEvent(gplayer_imp* imp, int activity_id, int state);
	void BingoSetOpenAllEvent(gplayer_imp* imp, int activity_id, int state);

	// 冰海行动
	void LotteryShipSendInfoData(gplayer_imp* imp, int activity_id);
	bool LotteryShipGetInfoData(int activity_id, int &total_draw_count, std::vector<int> &baodi_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts);
	bool LotteryShipGetInnerInfoData(int activity_id, int &total_draw_count, std::vector<int> &baodi_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts);
	bool LotteryShipGetInfoData(int activity_id, int &total_draw_count, int64_t &total_draw_count_has_get);
	void LotteryShipSetTotalDrawCount(gplayer_imp* imp, int activity_id, int total_draw_count);
	void LotteryShipSetInnerTotalDrawCount(gplayer_imp* imp, int activity_id, int total_draw_count);
	void LotteryShipSetTotalDrawCountHasGet(gplayer_imp* imp, int activity_id, int64_t total_draw_count_has_get);
	void LotteryShipSetInnerTotalDrawCountHasGet(gplayer_imp* imp, int activity_id, int64_t total_draw_count_has_get);
	void LotteryShipInitFanjinliInfo(gplayer_imp *imp, int activity_id, const std::vector<int> &fanjinli_info);
	void LotteryShipInitInnerFanjinliInfo(gplayer_imp *imp, int activity_id, const std::vector<int> &inner_fanjinli_info);
	void LotteryShipSetFanjinliInfo(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);
	void LotteryShipSetInnerFanjinliInfo(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);
	void LotteryShipSetBaodiInfo(gplayer_imp* imp, int activity_id, int count_min, int count_max);
	void LotteryShipSetInnerBaodiInfo(gplayer_imp* imp, int activity_id, int count_min, int count_max);


	// 小彩票
	void LittleCardGetInfoData(int activity_id, int& cur_level, std::vector<int64_t>& lottery_level_info);
	void LittleCardGetCurLevelInfo(int activity_id, int& cur_level, int64_t& reward_flag);
	void LittleCardSetRewardWithLevel(gplayer_imp *imp, int activity_id, int level, int reward_flag);
	void LittleCardSetCurLevel(gplayer_imp *imp, int activity_id, int cur_level);
	int LittleCardSetNextLevel(gplayer_imp *imp, int activity_id);
	int LittleCardGetCurLevelRewardCount(gplayer_imp *imp, int activity_id);
	void LittleCardSendInfoData(gplayer_imp *imp, int activity_id);
	bool LittleCardAlreadyGetAllBigReward(gplayer_imp *imp, int activity_id);

	// 绘梦星空
	void DrawSendInfoData(gplayer_imp* imp, int activity_id);
	bool DrawGetInfoData(int activity_id, int &total_draw_count, std::vector<int> &baodi_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts, int special_pool_idx, std::vector<int>& draw_out_idxs);
	int DrawGetDrawOutSize(int activity_id, int special_pool_idx);
	bool DrawGetTotalInfo(int activity_id, int &total_draw_count, int64_t &has_get);

	void DrawSetTotalDrawCount(gplayer_imp* imp, int activity_id, int total_draw_count);
	void DrawSetTotalDrawCountHasGet(gplayer_imp* imp, int activity_id, int64_t total_draw_count_has_get);
	void DrawSetBaodiInfo(gplayer_imp* imp, int activity_id, int count_min, int count_max);
	void DrawInitFanjinliInfo(gplayer_imp *imp, int activity_id, const std::vector<int> &fanjinli_info);
	void DrawSetFanjinliInfo(gplayer_imp* imp, int activity_id, int item_tid, int draw_count);
	void DrawSetDrawOutIdx(gplayer_imp* imp, int activity_id, int pool_idx, int idx);

// 简易彩票
public:
	bool EasyDiceGetInfoData(int dice_id, int &total_draw_count, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts);
	void EasyDiceSetTotalDrawCount(gplayer_imp* imp, int dice_id, int total_draw_count);
	void EasyDiceInitFanjinliInfo(gplayer_imp *imp, int dice_id, const std::vector<int> &fanjinli_info);
	void EasyDiceSetFanjinliInfo(gplayer_imp* imp, int dice_id, int item_tid, int draw_count);

public:
	bool GetSelectRewardPoolData(int activity_id, PB::self_select_reward_pool_info* &pool_ptr);
	bool GetStarryNightData(int activity_id, PB::starry_night_info* &pool_ptr);
	void SendStarryNightData(gplayer_imp* imp, int activity_id);

	bool GetMiningData(gplayer_imp* imp, int activity_id, PB::mining_info* &pool_ptr, int seq_idx);
// 通用
public:
	void DebugClearInfoData(gplayer_imp* imp, int ticket_type, int activity_id);
	bool DebugGetInfoData(gplayer_imp* imp, int ticket_type, int activity_id, std::string &str);
};

// 幸运值
class LuckyValueManager
{
	struct lucky_value_t
	{
		int value_repu = 0;							// 幸运值声望id
		int circuit_changer_repu = 0;				// 开关声望id
		int value_repu_threshold = 0;				// 幸运值阈值
		int open_circuit_changer_period = 0;		// 打开开关声望的周期
		int last_open_circuit_changer_repu = 0;		// 上次打开开关声望时间的声望id

		void BuildFromLuaAnyValue(const LuaAnyValue &v);
	};
	std::map<int, lucky_value_t> m_lucky_value;

private:
	LuckyValueManager() { }
	LuckyValueManager(const LuckyValueManager&) = delete;
	LuckyValueManager& operator = (const LuckyValueManager&) = delete;

protected:
	bool LoadConf(int dice_type, const std::string &path, const std::string& table_name);

public:
	static LuckyValueManager& GetInstance()
	{
		static LuckyValueManager _instance;
		return _instance;
	}

	bool Load();
	bool HasLucky(gplayer_imp *pImp, int lucky_index);
	void AddLuckyValue(gplayer_imp *pImp, int lucky_index, int value);
	void ResetLuckyValue(gplayer_imp *pImp, int lucky_index, bool close_circuit_change_repu);
	void OpenCircuitChanger(gplayer_imp *pImp);
};

struct draw_seq_t
{
	int size = 0;
	std::vector<int> first_seq_index;
	std::vector<int> seq_index;
	std::map<int, std::vector<int>> seq;
	int reward_index_min = 0;
	int reward_index_max = 0;

	void BuildFromLuaAnyValue(const LuaAnyValue &v, bool is_seq);
};
struct draw_seq_conf_t
{
	draw_seq_t ssr_reward_seq;
	draw_seq_t ssr_1_seq;
	draw_seq_t ssr_2_seq;
	draw_seq_t ssr_3_seq;

	void BuildFromLuaAnyValue(const LuaAnyValue &v);
};
class DrawSeqManager
{
private:
	DrawSeqManager() { }
	DrawSeqManager(const DrawSeqManager&) = delete;
	DrawSeqManager& operator = (const DrawSeqManager&) = delete;

	std::map<int, draw_seq_conf_t> m_draw_seq_conf;
	std::map<int, int> m_draw_seq_index_conf;

public:
	static DrawSeqManager& GetInstance()
	{
		static DrawSeqManager _instance;
		return _instance;
	}
	bool LoadConf();

	int GetRealDrawSeqIndex(int draw_seq_index);
	bool GetDrawSeqConf(int draw_seq_index, draw_seq_conf_t &draw_seq_conf);
	bool GetRewardIndexRange(int draw_seq_index, int& min, int& max);
	int GetSSRSeqIndex(const draw_seq_t &ssr_seq, bool is_first);
	bool HasSSR(const draw_seq_t &ssr_seq, int seq_index, int draw_count);
	int GetSSRRewardIndex(const draw_seq_t &ssr_seq, int ssr_reward_seq_index, int ssr_draw_count);
};

class player_draw_seq_info
{
public:
	void Load(gplayer_imp* imp, const PB::player_universal_data_t& pud);
	void Save(gplayer_imp* imp, PB::player_universal_data_t& pud);
	int Draw(gplayer_imp* imp, int draw_seq_index);

	void DebugClear(gplayer_imp* imp, int draw_seq_index);
	void DebugGetStr(gplayer_imp* imp, std::string &str);

private:
	std::map<int, PB::draw_seq_info> m_info;
};

enum
{
	CHATBOX_BUBBLE		= 0,
	CHATBOX_FONT		= 1,
	CHATBOX_BACKGROUND	= 2,
	CHATBOX_NAME_BOARD 	= 3,

	CHATBOX_COUNT,
	CHATBOX_BEGIN		= CHATBOX_BUBBLE,
};

struct chatbox_info
{
	int type;
	int index;
	void BuildFromLuaAnyValue(const LuaAnyValue &v);

	chatbox_info() : type(-1), index(-1) {}
	chatbox_info(int t, int i) : type(t), index(i) {}
	chatbox_info(const chatbox_info &rhs) : type(rhs.type), index(rhs.index) { }
	chatbox_info& operator = (const chatbox_info& rhs)
	{
		type = rhs.type;
		index = rhs.index;
		return *this;
	}
	bool operator<(const chatbox_info &rh) const
	{
		if (type < rh.type) return true;
		else if (type == rh.type)
		{
			if(index > rh.index) return true;
		}
		return false;
	}
};

struct chatbox_addon_info
{
	int type = 0;
	int index = 0;
	int addon = 0;
	void BuildFromLuaAnyValue(const LuaAnyValue &v)
	{
		type = v["position"];
		index = v["index"];
		addon = v["addon"];
	}
};

bool operator==(const chatbox_info& lhs, const chatbox_info &rhs);

struct chatbox_info_hasher
{
	std::size_t operator()(const chatbox_info& info) const
	{
		return std::hash<int>()(info.type) ^ std::hash<int>()(info.index);
	}
};

class chatbox_template_manager
{
public:
	static chatbox_template_manager& Instance()
	{
		static chatbox_template_manager _;
		return _;
	}
	const std::map<int, int>& GetWorldSpeakBgCost();
	int WorldSpeakBGCost(int index);
	void OnVIPLevelChanged(gplayer_imp *imp);
	void OnAddRetinue(gplayer_imp *imp, int retinue_id);
	void OnFriendChanged(gplayer_imp *imp, PB::ipt_amity_grade_sync &data);
	void ActivateDefault(gplayer_imp* imp);
	int GetChatboxAddon(const chatbox_info &chatbox) const
	{
		auto it = addon_list.find(chatbox);
		if (it != addon_list.end()) return it->second;
		return 0;
	}
	bool LoadScript();
private:
	std::vector<chatbox_info> default_activated;
	typedef std::map<int, std::vector<chatbox_info> > CHATBOX_CONFIG_MAP;
	CHATBOX_CONFIG_MAP vip_activated;
	CHATBOX_CONFIG_MAP retinue_activated;
	std::map<int, int> world_speak_bg_cost;
	std::map<int, std::map<int, chatbox_info>> friend_activated;
	std::map<chatbox_info, int> addon_list;
};

class player_once_item
{
public:
	player_once_item(){}
	void Load(const PB::player_universal_data_t& pud);
	void Save(PB::player_universal_data_t& pud);
public:
	void setItemUsed(int itemTid);
	bool isItemUsed(int itemTid);
protected:
private:
	std::set<int> _setUsedItem;
};

class player_virtual_grow_up_data
{
public:
	player_virtual_grow_up_data(){}
	void Load(const PB::player_universal_data_t& pud);
	void Save(PB::player_universal_data_t& pud);
public:
	void HandleRequest(gplayer_imp* imp, PB::gp_grow_up_data& cmd);
private:
	std::vector<PB::virtual_grow_up_data> _grow_up_vec;
};

class player_chatbox
{
public:
	player_chatbox(gplayer_imp* imp);
	void Load(gplayer_imp* imp, const PB::player_universal_data_t& pud);
	void Save(gplayer_imp* imp, PB::player_universal_data_t& pud);
	void OnSave();
	void OnLogin();
	virtual void SendInfoOnEnterScene();
	virtual void OnPlayerHeartbeat();
	virtual void SendTotalData();

	inline int SelectedBubbleIndex() { return selected[CHATBOX_BUBBLE]; }
	inline int SelectedFontIndex() { return selected[CHATBOX_FONT]; }
	inline int SelectedNameBoardIndex() { return selected[CHATBOX_NAME_BOARD]; }

	void Activate(int type, int index, int interval, bool log = true, bool notify_client = true);
	void Activate(const chatbox_info &info, int interval, bool log = true, bool notify_client = true);
	void Select(int type, int index);
	void ActiveAddon(const chatbox_info &info);
	void Del(int type, int index);

	void UpdateExpireTM();
	void NotifyDSSelected(int type, int index);

private:
	std::unordered_map<chatbox_info, int, chatbox_info_hasher> activated;
	std::set<chatbox_info> addon_actived;
	int selected[CHATBOX_COUNT];
	int latest_expire_tm;
	chatbox_info latest_expire_item;
	PB::player_chatbox data;
	bool dirty;
	gplayer_imp* imp;
};

class player_adventure_task
{
	using TaskMap = std::unordered_map<int, std::pair<int, int>>; //taskid -> (finish_time, rank)
public:
	player_adventure_task() {}
	void Load(const PB::player_universal_data_t& pud);
	void Save(PB::player_universal_data_t& pud);
	void MakeClientData(PB::gp_adventure_task_list& proto);
	void SendClient(gplayer_imp* imp);
	void OnTaskFinish(gplayer_imp *imp, int taskid);
	void OnTaskRank(gplayer_imp *imp, int taskid, int finish_time, int rank);
private:
	TaskMap _task_map;
};

class player_stock
{
	PB::db_player_simple_stock _data;
	std::unordered_map<int/*stock_id*/, PB::simple_stock_price_data_gs> _price_data;
public:
	void InitStock(gplayer_imp *imp); //初始化股票数据 未初始化不能买卖
	void Heartbeat(gplayer_imp* imp);
	void SendClientList(gplayer_imp* imp);
	int Buy(gplayer_imp* imp, int stock_id, int count, int client_price);
	int Sell(gplayer_imp* imp, int stock_id, int count, int client_price);
	void HandleOperation(gplayer_imp* imp, PB::gp_simple_stock_op &cmd);
	void Load(const PB::player_universal_data_t& pud);
	void Save(PB::player_universal_data_t& pud);
	void OnClose(gplayer_imp* imp);
	void DebugSayPrices(gplayer_imp* imp, int stock_id);
private:
	const PB::simple_stock_price_data_gs* GetStockInfo(int stock_id, int now_time);
};

class player_simplified_client
{
	std::set<int> without_download_resource_ids;

public:
	void LoadUnSave(const PB::gp_small_bag_download_resource& pb);
	void UnSaveData(PB::gp_small_bag_download_resource& pb);

	void ClearData();
	void Report(const PB::gp_small_bag_download_resource& pb);
	bool WithoutDownload(int resource_id);
	void SendWithoutDownloadResource(gplayer_imp *imp, scene_tag_t scene_tag);
};

// 明星应援奖励类型
enum TOP_STAR_AWARD_TYPE
{
	TSAT_NONE		= 0,
	TSAT_DAILY		= 1,
	TSAT_TOTAL		= 2,
};
class player_top_star : public PlayerCommonInterface
{
	player_top_star() {}
public:
	static player_top_star& Interface()
	{
		static player_top_star _instance;
		return _instance;
	}
	virtual void SendInfoOnEnterScene(gplayer_imp *imp) override;
	virtual void SendTotalData(gplayer_imp* imp) override;
	virtual void OnPlayerHeartbeat(gplayer_imp* imp) override;

	PB::gp_top_star_data& Data(gplayer_imp *imp);
	void DailyUpdate(gplayer_imp *imp);
	void DebugClear(gplayer_imp *imp);
	void DebugOutput(gplayer_imp *imp);
	bool SendReportMail(gplayer_imp* imp);

	void OnUseLottery(gplayer_imp *imp, int item_id, int item_count, int star_id);
	void ReqeustGetAward(gplayer_imp *imp, bool daily, int award_id);
	int GetDailyCheer(gplayer_imp *imp) { return Data(imp).daily_cheer(); }
	int GetTotalCheer(gplayer_imp *imp)  { return Data(imp).total_cheer(); }
	int GetStarCheer(gplayer_imp *imp, int star_id, bool daily);

	void GetFanjinliInfo(gplayer_imp *imp, std::vector<int>& fanjinli_item_tids, std::vector<int>& fanjinli_draw_counts);
	void InitFanjinliInfo(gplayer_imp *imp, const std::vector<int> &fanjinli_info);
	void SetFanjinliInfo(gplayer_imp* imp, int item_tid, int draw_count);
	bool CanGetAward(gplayer_imp *imp, int top_star_award_type, int top_star_award_id);
	void SetAward(gplayer_imp *imp, int top_star_award_type, int top_star_award_id);
	void IdipSetTotalCount(gplayer_imp *imp, int repu_id);

private:
	int GetMostSupportStar(gplayer_imp* imp);
};

struct god_explore_level_cfg
{
	int optional_reward_tid = 0;	// 层特殊自选奖励模板ID
	int diamond_pool_total = 0;		// 层奖池钻石总量
	int diamond_downlimit = 0;		// 层奖池钻石保底
	int lottery_count = 0;			// 每层能开奖次数
};

struct god_explore_cfg
{
	int open_level_num = 0; // 开放层数,超过这个层数后就是个普彩票了，没有自选奖励了
	std::vector<int> default_optional_reward_tids; //自选奖励
	std::vector<int> default_reward_open_level; //自选奖励开放层数
	std::vector<god_explore_level_cfg> level_cfg_vec;
	const god_explore_level_cfg* GetLevelCfg(int level) const;
};

#define GOD_EXPLORE_CFG god_explore_cfg_manager::GetInstance()

struct god_explore_event_cfg
{
	int activity_id = 0;
	int employ_cost_ticket = 0;		// 佣兵事件花费点券
	std::vector<int> employ_lottery_counts;	// 佣兵事件抽奖次数
	int black_boss_item_id = 0;		// 黑市商人事件给的道具
	int black_boss_item_count = 0;	// 黑市商人事件给的道具数量
	void BuildFromLuaAnyValue(const LuaAnyValue& v);
};

class god_explore_cfg_manager
{
	std::map<int, god_explore_cfg> m_god_explore_cfg;
	god_explore_event_cfg m_god_explore_event_cfg;
public:
	bool LoadEventConf();
	void LoadTemplate(const LOTTERY_GOD_CFG& cfg);
	const god_explore_cfg* GetConfig(int ess_id);
	int GetMaxDrawLevel(int ess_id);
	int GetLevelDrawCount(int ess_id, int level);
	const god_explore_level_cfg* GetLevelCfg(int ess_id, int level);
	static god_explore_cfg_manager& GetInstance()
	{
		static god_explore_cfg_manager _s_instance;
		return _s_instance;
	}
	const god_explore_event_cfg& GetEventConf();
};

#define LITTLE_CARD_CONFIG little_card_cfg_manager::Instance()
class little_card_cfg_manager
{
private:
	little_card_cfg_manager& operator= (const little_card_cfg_manager& rhs) = delete;
	little_card_cfg_manager(const little_card_cfg_manager& rhs) = delete;
	little_card_cfg_manager() : m_dice_id(0)
	{
	}
public:
	~little_card_cfg_manager()
	{}

	bool LoadConfig();

	bool GetLotteryCostTicket(int& need_tiket, int& draw_count, int cur_level, int cur_level_lottery_count, bool is_whole_level = false);
	int GetLotteryCostTicket(int cur_level, int cur_level_lottery_count, int draw_count);
	int GetLevelCostSize(int cur_level);
	bool IsGetCurLevelAllReward(int cur_level, int reward_flag);
	bool IsGetCurLevelBigReward(int cur_level, int reward_flag);

	static little_card_cfg_manager& Instance()
	{
		static little_card_cfg_manager _instance;
		return _instance;
	}

	std::vector<std::vector<int> > m_cost_ticket;
	std::vector<int64_t> m_big_reward; // 稀有奖励，用来判断当前是否达到跳层标准
	int m_dice_id = 0; // 小彩票本期骰子id
	int m_get_all_big_reward_speak_id = 0; //抽到全部稀有奖励的喊话id
};

#define QXQY_CONFIG qxqy_cfg_manager::Instance()
class qxqy_cfg_manager
{
private:
	qxqy_cfg_manager& operator= (const qxqy_cfg_manager& rhs) = delete;
	qxqy_cfg_manager(const qxqy_cfg_manager& rhs) = delete;
	qxqy_cfg_manager() : _max_level(0), _activity_id(0), _crit_rate(0)
	{
	}
public:
	~qxqy_cfg_manager()
	{}
	static qxqy_cfg_manager& Instance()
	{
		static qxqy_cfg_manager _instance;
		return _instance;
	}


	bool LoadConfig();

	int _max_level = 0; // 最大层数
	int _activity_id = 0; // 本期活动id
	int _crit_rate = 0; // 初始暴击几率（千分比)
};

class top_star_manager
{
	top_star_manager& operator= (const top_star_manager& rhs) = delete;
	top_star_manager(const top_star_manager& rhs) = delete;
	top_star_manager() : _report_timestamp(0), _activity_id(0), _campaign_id(0)
	{
	}

public:
	~top_star_manager()
	{
	}
	static top_star_manager& Instance() 
	{ 
		static top_star_manager instance; 
		return instance;
	}

	void LoadReport(const PB::ipt_top_star_report& report);
	bool GetReport(PB::gp_top_star_report& client_report);
	void OnCampaignBegin(int camp_id);
	void OnCampaignEnd(int camp_id);
	int GetActivityID() { return _activity_id; }

private:
	abase::RWLock _report_lock;
	PB::ipt_top_star_report _report;
	int _report_timestamp;
	int _activity_id;
	int _campaign_id;
};

class player_career_shop_buyer : public PlayerCommonInterface
{
	player_career_shop_buyer() {}
public:
	static  player_career_shop_buyer& Interface()
	{
		static player_career_shop_buyer _instance;
		return _instance;
	}
	virtual void SendInfoOnEnterScene(gplayer_imp *imp) override;
	virtual void SendTotalData(gplayer_imp* imp) override;

	PB::gp_career_shop_buyer_info& Data(gplayer_imp *imp);
	void DailyUpdate(gplayer_imp *imp);
	int GetOwnerCount(gplayer_imp *imp);
	bool FindOwner(gplayer_imp *imp, int64_t owner_id);
	void AddOwner(gplayer_imp *imp, int64_t owner_id);
	void DebugClear(gplayer_imp *imp);
};

class player_emoji_manager
{
public:
	player_emoji_manager() { }

	void Load(gplayer_imp* imp, const PB::player_universal_data_t& pud);
	void Save(gplayer_imp* imp, PB::player_universal_data_t& pud);
	void SendInfoOnEnterScene(gplayer_imp *imp);
	bool CheckAddEmoji(int emoji_id, int period);
	bool AddEmoji(gplayer_imp *imp, int emoji_id, int period);
	bool CheckEmoji(int emoji_id) const;

private:
	std::unordered_map<int/*emoji_id*/, int/*timestamp*/> m_emojis;
};

class player_overcook
{
public:
	player_overcook() { }
	void Load(gplayer_imp* imp, const PB::player_universal_data_t& pud);
	void Save(gplayer_imp* imp, PB::player_universal_data_t& pud);
	void SendInfo2Client(gplayer_imp *imp);
	int CheckEnterInstance(gplayer_imp *imp, int level_id);
	int GetReward(gplayer_imp *imp, int tid);
	int FinishGame(gplayer_imp *imp, int level_id, int score, int recipe_count);
	bool IsInstanceUnlock(int instance_id);
	void OnNewWeek(gplayer_imp *imp);
	int GetHighScore(int level_id)
	{
		auto it = _level_data.find(level_id);
		if (it != _level_data.end()) return it->second.high_score();
		return 0;
	}

private:
	void MakePbData(PB::player_overcook_info &data);

	std::map<int, PB::player_overcook_info::level_t> _level_data;
	std::set<int> _reward_data;
	int _lucky_level_id = 0;
	bool _lucky_award = false;
};

class player_bouquet
{
public:
	player_bouquet(gplayer_imp* imp) : _imp(imp) { }
	void Load(const PB::player_universal_data_t& pud);
	void Save(PB::player_universal_data_t& pud);
	bool IsCollected(tid_t tid);
	void Collect(tid_t tid);
	void GMCollect(tid_t tid, int op);
	void SendInfo2Client();
	void OnLogin();

	void GpOp(PB::gp_bouquet_op* request);
	void GpOp_Use(PB::gp_bouquet_op* request);
	void GpOp_Send(PB::gp_bouquet_op* request);
	void GpOp_IsCollect(PB::gp_bouquet_op* request);

	void ActiveBouquetAttrGroup();
	void TryActiveBouquetAttrGroupOne(tid_t flower_tid);
	std::map<int, PB::bouquet_collect_data>& GetColletData() { return _collect_data; }

private:
	std::map<int, PB::bouquet_collect_data> _collect_data;
	gplayer_imp* _imp;
};

class player_roam_community
{
public:
	player_roam_community(gplayer_imp* imp) : _imp(imp), _uniqueid(0) { }
	bool IsSameCommunity(int64_t uniqueid);
	int64_t GetUniqueId();
	void GpOp(PB::gp_roam_community_op* request);
	void GpOp_RequestCreate(PB::gp_roam_community_op* request);
	void GpOp_RequestGetAward(PB::gp_roam_community_op* request);
	void IptOp(PB::ipt_roam_community_op_re* request_re);
	void OnRequestCreate(PB::ipt_roam_community_op_re* request_re);
	void OnSync(PB::ipt_roam_community_op_re* request_re);
	void OnDayActivityChange(int cur_value, int real_offset);

private:
	gplayer_imp* _imp;
	int64_t _uniqueid;

};

class player_mount_space
{
public:
	player_mount_space(gplayer_imp* imp) : _imp(imp) { }
	void HandleClientOp(const PB::gp_mount_space_operation* request);
	void HandleClientOp_Enter(const PB::gp_mount_space_operation* request);
	void HandleClientOp_Put(const PB::gp_mount_space_operation* request);
	void HandleClientOp_Cancel(const PB::gp_mount_space_operation* request);
	void HandleClientOp_Turn(const PB::gp_mount_space_operation* request);

	void OnEnterScene();
	bool InSelfMountSpace();
	void BroadcastOpRes(const PB::gp_mount_space_operation_re& notify);

private:
	gplayer_imp* _imp;
};

class player_townlet_config_manager
{
	player_townlet_config_manager() { }

public:
	struct townlet_level_up_config_t
	{
		int exp = 0;
		int coin = 0;
		int task_id = 0;
		int building_limit = 0;
		int days_limit = 0;
		int reward_id = 0;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			exp = v["exp"];
			coin = v["coin"];
			task_id = v["task_id"];
			building_limit = v["building_limit"];
			days_limit = v["days_limit"];
			reward_id = v["reward_id"];
		}
	};

	struct townlet_building_config_t
	{
		struct skin
		{
			int coin = 0;

			void BuildFromLuaAnyValue(const LuaAnyValue& v)
			{
				coin = v["coin"];
			}
		};
		std::map<int, skin> skins;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			v["skin"].Fetch(skins);
		}
	};

	struct townlet_shop_item_config_t
	{
		int tid	= 0;
		int count = 0;
		int price_min = 0;
		int price_max = 0;
		int day_limit = 0;
		int count_per_day = 0;
		int odds = 0;
		int is_rare = 0;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			tid = v["tid"];
			count = v["count"];
			price_min = v["price_min"];
			price_max = v["price_max"];
			day_limit = v["day_limit"];
			count_per_day = v["count_per_day"];
			odds = v["odds"];
			is_rare = v["is_rare"];

			ASSERT(0 != tid);
			ASSERT(0 != count);
			ASSERT(0 != price_min && 0 != price_max && price_min < price_max);
			ASSERT(0 != day_limit);
			ASSERT(0 != count_per_day);
			ASSERT(0 != odds);
			ASSERT(0 == is_rare || 1 == is_rare);
		}
	};

	struct townlet_shop_level_up_config_t
	{
		int level_up_cost = 0;
		int buy_item_cost = 0;
		int buy_item_id = 0;
		int townlet_level = 0;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			level_up_cost = v["level_up_cost"];
			buy_item_cost = v["buy_item_cost"];
			buy_item_id = v["buy_item_id"];
			townlet_level = v["townlet_level"];

			ASSERT(level_up_cost != 0 && (buy_item_cost != 0 || buy_item_id != 0) && townlet_level != 0);
		}
	};

	struct townlet_partner_level_up_config_t
	{
		int level_up_cost = 0;
		int chat_event_count = 0;
		int townlet_level = 0;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			level_up_cost = v["level_up_cost"];
			chat_event_count = v["chat_event_count"];
			townlet_level = v["townlet_level"];

			ASSERT(level_up_cost != 0 && townlet_level != 0);
		}
	};

	// 宠物馆升级配置
	struct townlet_pet_level_up_config_t
	{
		int level_up_cost = 0;
		int pet_quality_limit = 0;
		int pet_count_limit = 0;
		int townlet_level = 0;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			level_up_cost = v["level_up_cost"];
			pet_quality_limit = v["pet_quality_limit"];
			pet_count_limit = v["pet_count_limit"];
			townlet_level = v["townlet_level"];
		}
	};

	//宠物配置
	struct townlet_pet_config_t
	{
		int quality = 0;
		int element = 0;
		int character_min = 0;
		int character_max = 0;
		int total_max = 0;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			quality = v["quality"];
			element = v["element"];
			character_min = v["character_min"];
			character_max = v["character_max"];
			total_max = v["total_max"];

			ASSERT(total_max >= character_max);
		}
	};

	//宠物食物配置
	struct townlet_pet_food_config_t
	{
		int element = 0;
		int add_hungry = 0;
		int add_health = 0;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			element = v["element"];
			add_hungry = v["add_hungry"];
			add_health = v["add_health"];
		}
	};

	//宠物刷新配置
	struct townlet_pet_controller_config_t
	{
		int day_begin_time = 0;
		std::vector<int> controllers;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			int hour = v["hour"];
			int min = v["min"];
			v["controllers"].Fetch(controllers);

			ASSERT(hour >=0 && hour <= 23);
			ASSERT(min >= 0 && min <= 59);
			ASSERT(!controllers.empty() && controllers.size() <= 8);

			day_begin_time = hour * 3600 + min * 60;
		}
	};

	//宠物生产配置
	struct townlet_pet_produce_config_t
	{
		int duration = 0;
		struct townlet_pet_produce_cost_config_t
		{
			int produce_cost_item_tid = 0;
			int produce_cost_item_count = 0;

			void BuildFromLuaAnyValue(const LuaAnyValue& v)
			{
				produce_cost_item_tid = v["produce_cost_item_tid"];
				produce_cost_item_count = v["produce_cost_item_count"];
			}
		};
		std::vector<townlet_pet_produce_cost_config_t> costs;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			duration = v["duration"];
			v["costs"].Fetch(costs);

			ASSERT(duration > 0 && duration <= 86400);
		}
	};

	struct townlet_item_config_t
	{
		std::map<int, int> add_visit_rate = {};
		std::map<int, int> add_travel_event_rate = {};
		std::set<int> can_effect_retinue_ids = {};

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			if (v.isMember("add_visit_rate"))
			{
				v["add_visit_rate"].Fetch(add_visit_rate);
			}
			if (v.isMember("add_travel_event_rate"))
			{
				v["add_travel_event_rate"].Fetch(add_travel_event_rate);
				std::vector<int> v_tmp;
				v["can_effect_retinue_ids"].Fetch(v_tmp);
				for (auto r_id : v_tmp)
				{
					can_effect_retinue_ids.insert(r_id);
				}
			}
		}
	};

	struct townlet_travel_event_config_t
	{
		int duration = 0;
		std::set<int> postcard_id = {};

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			duration = v["duration"];
			v["postcard_id"].Fetch(postcard_id);
		}
	};

	struct townlet_travel_postcard_config_t
	{
		std::vector<int> retinue_con = {};
		int weight = 1;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			v["retinue_con"].Fetch(retinue_con);
			weight = v["weight"];
		}
	};

	struct postcard_suit_level_config_t
	{
		int count = 0;
		int base_prop_tid = 0;
		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			count = v["count"];
			base_prop_tid = v["base_prop_tid"];
		}
	};

	struct travel_postcard_suit_config_t
	{
		int count = 0;
		std::vector<postcard_suit_level_config_t> add_prop = {};
		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			count = v["count"];
			v["add_prop"].Fetch(add_prop);
		}
		int GetPropId(int count) const
		{
			if (count <= 0)
			{
				return 0;
			}
			if (add_prop.empty() || add_prop[0].count > count)
			{
				return 0;
			}
			for (int i = 1; i < add_prop.size(); i++)
			{
				if (add_prop[i].count > count)
				{
					return add_prop[i - 1].base_prop_tid;
				}
			}
			return add_prop[add_prop.size() - 1].base_prop_tid;
		}
	};

	struct travel_postcard_accum_reward_config_t
	{
		int postcard_count = INT_MAX;
		int reward_id  = 0;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			postcard_count = v["postcard_count"];
			reward_id = v["reward_id"];
		}
	};

	struct townlet_get_coin_config_t
	{
		int coin_per_hour = 0;
		int coin_get_max = 0;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			coin_per_hour = v["coin_per_hour"];
			coin_get_max = v["coin_get_max"];
		}
	};

	struct retinue_chat_config_t
	{
		std::string aibot_id = "";
		std::string aibot_token = "";
		int amity_repu_id = 0;
		std::vector<std::vector<int>> amity_event = {};
		std::map<int, int> pre_events = {}; //缓存一下前置事件

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			aibot_id = v["aibot_id"].c_str();
			aibot_token = v["aibot_token"].c_str();
			amity_repu_id = v["amity_repu_id"];
			v["amity_event"].Fetch(amity_event);
			for (int i = 0; i < amity_event.size(); i++)
			{
				ASSERT(amity_event[i].size() == 2);
				if (i >= 1)
				{
					pre_events[amity_event[i][1]] = amity_event[i - 1][1];
				}
			}
		}
	};

	struct retinue_chat_event_config_t
	{
		int accepttask = 0;
		int endtask = 0;
		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			accepttask = v["accepttask"];
			endtask = v["endtask"];
		}
	};

//#ifndef USE_TENCENT
	struct retinue_chat_config //伙伴对话的配置
	{
		int retinue_id = 0;
		int start_msg_id = 0; //对话起始位置
		int normal_msglist = 0;
	};
	
	struct retinue_chat_msg_config //对话内容的配置
	{
		int next_msg_state = 0; //下一句由谁来接
		std::set<int> next_msg;
		int amity_add = 0; //本句完成后可获得的好友度
	};

	struct foo
	{
		static bool Check(const std::map<int, retinue_chat_msg_config>& msg_list)
		{
			std::set<int> leaf_msg, root_msg;
	
			leaf_msg.clear();
			root_msg.clear();
			for (auto it = msg_list.begin(); it !=  msg_list.end(); ++it)
			{
				if (it->second.next_msg.size() == 0)
				{
					leaf_msg.insert(it->first);
				}
				else
				{
					root_msg.insert(it->first);
				}
			}
	
			while (root_msg.size())
			{
				if (!ClearEdgeToLeaf(msg_list, root_msg, leaf_msg))
				{
					return false;
				}
			}
			return true;
		}
	
		static bool ClearEdgeToLeaf(const std::map<int, retinue_chat_msg_config>& msg_list, std::set<int>& root, std::set<int>& leaf)
		{
			for (auto it = root.begin(); it != root.end();)
			{
				auto lit = msg_list.find(*it);
				if (lit == msg_list.end())
				{
					return false;
				}
	
				auto& a = lit->second;
				auto nit = a.next_msg.begin();
				for (; nit != a.next_msg.end(); ++nit)
				{
					if (leaf.find(*nit) == leaf.end())
					{
						break;
					}
				}
	
				if (nit == a.next_msg.end())
				{
					leaf.insert(*it);
					root.erase(it);
					return true;
				}
				++it;
			}
			return false;
		}
	
		static void Split(const std::string& str, std::set<int>& ret)
		{
			size_t start = 0, index = str.find_first_of(",", 0);
			while (index != str.npos)
			{
				if (start != index)
				{
					int i = std::stoi(str.substr(start, index - start));
					ASSERT(i > 0 && i <= RETINUE_CHAT_MAX_COUNT);
					ret.insert(i);
				}
				start = index + 1;
				index = str.find_first_of(",", start);
			}
			if (!str.substr(start).empty())
			{
				int i = std::stoi(str.substr(start));
				ASSERT(i > 0 && i <= RETINUE_CHAT_MAX_COUNT);
				ret.insert(i);
			}
		}
	};
//#endif

public:
	static player_townlet_config_manager& GetInstance()
	{
		static player_townlet_config_manager man;
		return man;
	}

	bool LoadConfig(const char *path);

	void SetUnlimitCoinMode()
	{
		_debug_unlimit_coin = true;
	}

	const townlet_level_up_config_t* GetLevelUpConfig(int level) const
	{
		if (level < 1 || level > m_level_up_configs.size())
		{
			return NULL;
		}
		return &(m_level_up_configs[level - 1]);
	}
	const townlet_building_config_t* GetBuildingConfig(int building_index) const
	{
		auto it = m_building_configs.find(building_index);
		if (it == m_building_configs.end())
		{
			return NULL;
		}
		return &(it->second);
	}
	const townlet_shop_item_config_t* GetShopItemConfig(int item_index) const
	{
		auto it = m_shop_item_configs.find(item_index);
		if (it == m_shop_item_configs.end())
		{
			return nullptr;
		}
		return &(it->second);
	}
	int GetShopLevelItemNum(int level) const
	{
		auto it = m_shop_level_item_num.find(level);
		if (it == m_shop_level_item_num.end())
		{
			return 0;
		}
		return it->second;
	}
	const int GetGainPostcardRate() const
	{
		return m_gain_postcard_rate;
	}
	const int GetTravelCommonReward() const
	{
		return m_travel_common_reward;
	}
	const int GetTravelDuration() const
	{
		return m_travel_duration;
	}
	const int GetExchangeTimeoutTime() const
	{
		return m_exchange_timeout_time;
	}
	const int GetExchangeLimitId() const
	{
		return m_exchange_limit_id;
	}
	const int GetMaxVisitItemCount(int lv) const
	{
		if (lv < 0 || m_max_visit_item_count.size() < lv)
		{
			return 0;
		}
		return m_max_visit_item_count[lv - 1];
	}
	const int GetMaxEventItemCount(int lv) const
	{
		if (lv < 0 || m_max_event_item_count.size() < lv)
		{
			return 0;
		}
		return m_max_event_item_count[lv - 1];
	}
	int GetPetMaxCount(int level) const
	{
		auto it = m_level_pet_count_max.find(level);
		if (it == m_level_pet_count_max.end())
		{
			return 0;
		}
		return it->second;
	}
	const int GetRetinueChatCULId(int lv) const
	{
		if (lv < 0 || m_retinue_chat_common_use_limit_id.size() < lv)
		{
			return 0;
		}
		return m_retinue_chat_common_use_limit_id[lv - 1];
	}
	const int GetRetinueChatAddRepu(int lv) const
	{
		if (lv < 0 || m_retinue_chat_positive_add_repu.size() < lv)
		{
			return 0;
		}
		return m_retinue_chat_positive_add_repu[lv - 1];
	}
	const int GetRetinueChatReduRepu(int lv) const
	{
		if (lv < 0 || m_retinue_chat_negative_redu_repu.size() < lv)
		{
			return 0;
		}
		return m_retinue_chat_negative_redu_repu[lv - 1];
	}
	const int GetRetinueChatRepuMaxChangeCount() const
	{
		return m_retinue_chat_max_change_count;
	}
	const townlet_travel_event_config_t* GetTralvelEventConfig(int id) const
	{
		auto iter = m_travel_event_config.find(id);
		if (iter == m_travel_event_config.end())
		{
			return nullptr;
		}
		return &(iter->second);
	}
	const townlet_travel_postcard_config_t* GetTravelPostcardConfig(int id) const
	{
		auto iter = m_postcard_config.find(id);
		if (iter == m_postcard_config.end())
		{
			return nullptr;
		}
		return &(iter->second);
	}
	const travel_postcard_suit_config_t* GetTravelPostcardSuitConfig(int id) const
	{
		auto iter = m_postcard_suit_config.find(id);
		if (iter == m_postcard_suit_config.end())
		{
			return nullptr;
		}
		return &(iter->second);
	}
	const travel_postcard_accum_reward_config_t* GetPostcardAccumRewardConfig(int id) const
	{
		if (id < 1 || id > m_postcard_accum_reward_config.size())
		{
			return nullptr;
		}
		return &(m_postcard_accum_reward_config[id - 1]);
	}
	const townlet_item_config_t* GetTravelItemConfig(int id) const
	{
		auto iter = m_item_config.find(id);
		if (iter == m_item_config.end())
		{
			return nullptr;
		}
		return &(iter->second);
	}
	static int RandomOneByMap(std::map<int, int>& m_id_weight)
	{
		int total_weight = 0;
		for (auto& kv : m_id_weight)
		{
			total_weight += kv.second;
		}
		if (total_weight <= 0)
		{
			return 0;
		}
		int randnum = abase::Rand(0, total_weight - 1);
		for (auto& kv : m_id_weight)
		{
			randnum -= kv.second;
			if (randnum < 0)
			{
				return kv.first;
			}
		}
		return 0;
	}
	const void CalcVisitRetinueId(const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >& v_items, std::set<int>& r_ids) const
	{
		for (auto item_id : v_items)
		{
			std::map<int, int> visit_map = m_default_visit_ids;
			auto iter = m_item_config.find(item_id);
			if (iter != m_item_config.end())
			{
				for (auto& kv : iter->second.add_visit_rate)
				{
					visit_map[kv.first] += kv.second;
				}
			}
			for (auto id : r_ids) //不可重复
			{
				visit_map[id] = 0;
			}
			int t_r_id = RandomOneByMap(visit_map);
			if (t_r_id)
			{
				r_ids.insert(t_r_id);
			}
		}
	}
	const int CalcTravelEventId(const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >& v_items) const
	{
		std::map<int, int> te_map = m_default_travel_event_ids;
		for (auto item_id : v_items)
		{
			auto iter = m_item_config.find(item_id);
			if (iter != m_item_config.end())
			{
				for (auto& kv : iter->second.add_travel_event_rate)
				{
					te_map[kv.first] += kv.second;
				}
			}
		}
		return RandomOneByMap(te_map);
	}
	int CalcTravelPostcardId(std::set<int>& s_r_ids, int event_id) const
	{
		auto iter_e = m_travel_event_config.find(event_id);
		if (iter_e == m_travel_event_config.end())
		{
			return 0;
		}
		auto &s_p = iter_e->second.postcard_id;
		std::map<int, int> p_map = {};
		for (auto iter_p = s_p.begin(); iter_p != s_p.end(); iter_p++)
		{
			int p_id = *iter_p;
			auto *p_config = GetTravelPostcardConfig(p_id);
			if (!p_config || p_config->retinue_con.size() > s_r_ids.size())
			{
				continue;
			}
			bool flag = true;
			for (auto iter_c = p_config->retinue_con.begin(); iter_c != p_config->retinue_con.end(); iter_c++)
			{
				if (s_r_ids.count(*iter_c) == 0)
				{
					flag = false;
					break;
				}
			}
			if (flag)
			{
				p_map[p_id] += p_config->weight;
			}
		}
		return RandomOneByMap(p_map);
	}
	void CalcShopItems(std::set<int>& tmp, std::vector<int>& new_items, int count) const
	{
		auto random_one = [this](std::set<int>& tmp)->int
		{
			int total_odds = 0;
			std::vector<std::pair<int, int>> _;
			for (auto kv: m_shop_item_configs)
			{
				if (tmp.find(kv.first) != tmp.end())
				{
					continue;
				}
				total_odds += kv.second.odds;
				_.push_back(std::make_pair(kv.first, kv.second.odds));
			}
			if (_.empty())
			{
				return -1;
			}
			int odds = abase::Rand(1, total_odds);
			for (auto v: _)
			{
				odds -= v.second;
				if (odds <= 0)
				{
					return v.first;
				}
			}
			return -2;
		};

		for (int i = 0; i < count; i ++)
		{
			int item_id = random_one(tmp);
			if (item_id <= 0)
			{
				return;
			}
			new_items.push_back(item_id);
			tmp.insert(item_id);
		}
	}

	const townlet_shop_level_up_config_t* GetShopLevelUpConfig(int level) const
	{
		auto it = m_shop_level_up_configs.find(level);
		if (it == m_shop_level_up_configs.end())
		{
			return nullptr;
		}
		return &(it->second);
	}

	const townlet_partner_level_up_config_t* GetPartnerLvelUpConfig(int level) const
	{
		auto it = m_partner_level_up_configs.find(level);
		if (it == m_partner_level_up_configs.end())
		{
			return nullptr;
		}
		return &(it->second);
	}

	const townlet_get_coin_config_t* GetGetCoinConfig(int level) const
	{
		auto it = m_get_coin_configs.find(level);
		if (it == m_get_coin_configs.end())
		{
			return nullptr;
		}
		return &(it->second);
	}

	std::string GetChatAibotId(int retinue_id) const
	{
		auto iter = m_retinue_chat_config.find(retinue_id);
		if (iter == m_retinue_chat_config.end())
		{
			return "";
		}
		return iter->second.aibot_id;
	}

	std::string GetChatAibotToken(int retinue_id) const
	{
		auto iter = m_retinue_chat_config.find(retinue_id);
		if (iter == m_retinue_chat_config.end())
		{
			return "";
		}
		return iter->second.aibot_token;
	}

	int GetChatRepuId(int retinue_id) const
	{
		auto iter = m_retinue_chat_config.find(retinue_id);
		if (iter == m_retinue_chat_config.end())
		{
			return 0;
		}
		return iter->second.amity_repu_id;
	}
	int GetChatEventRepuCount(int retinue_id, int event_id) const
	{
		auto iter = m_retinue_chat_config.find(retinue_id);
		if (iter == m_retinue_chat_config.end())
		{
			return INT32_MAX;
		}
		for (int i = 0; i < iter->second.amity_event.size(); i++)
		{
			if (iter->second.amity_event[i][1] == event_id)
			{
				return iter->second.amity_event[i][0];
			}
		}
		return INT32_MAX;
	}
	void CalcChatUnlockEvent(int retinue_id, int old_repu_value, int new_repu_value, std::vector<int>& unlock_events)
	{
		auto iter = m_retinue_chat_config.find(retinue_id);
		if (iter == m_retinue_chat_config.end())
		{
			return ;
		}
		auto &amity_event = iter->second.amity_event;
		for (int i = 0; i < amity_event.size(); i++)
		{
			int repu_value = amity_event[i][0];
			if (repu_value > old_repu_value && repu_value <= new_repu_value)
			{
				unlock_events.push_back(amity_event[i][1]);
			}
		}
	}
	float GetLevelExpRadio(int level) const
	{
		auto it = m_level_exp_radio_configs.find(level);
		if (it == m_level_exp_radio_configs.end())
		{
			return 0.f;
		}
		return it->second;
	}

	int GetLevelExpLimit(int level) const
	{
		if (_debug_unlimit_coin)
		{
			return INT_MAX;
		}
		auto it = m_level_exp_limit_configs.find(level);
		if (it == m_level_exp_limit_configs.end())
		{
			return 0;
		}
		return it->second;
	}
	int GetPreChatEvent(int retinue_id, int event_id)
	{
		auto iter = m_retinue_chat_config.find(retinue_id);
		if (iter == m_retinue_chat_config.end())
		{
			return 0;
		}
		auto iter_1 = iter->second.pre_events.find(event_id);
		if (iter_1 == iter->second.pre_events.end())
		{
			return 0;
		}
		return iter_1->second;
	}
	int GetRetinueIdByChatEventId(int event_id)
	{
		auto iter = _chat_event_2_retinue.find(event_id);
		if (iter == _chat_event_2_retinue.end())
		{
			return 0;
		}
		return iter->second;
	}
	const retinue_chat_event_config_t* GetChatEventConfig(int event_id)
	{
		auto iter = m_retinue_chat_event_config.find(event_id);
		if (iter == m_retinue_chat_event_config.end())
		{
			return 0;
		}
		return &(iter->second);
	}

//#ifndef USE_TENCENT
	const retinue_chat_config *GetRetinueChatConfig(int chat_id)
	{
		auto it = m_retinue_chat_map.find(chat_id);
		if(it == m_retinue_chat_map.end()) return NULL;
		return &(it->second);
	}

	const std::map<int, retinue_chat_msg_config>* GetRetinueChatMsgConfig(int msg_id)
	{
		auto it = m_retinue_chat_msg_map.find(msg_id);
		if(it == m_retinue_chat_msg_map.end()) return NULL;
		return &(it->second);
	}

	void RandomSelectChat(int retinue_id, std::vector<int> &ids)
	{
		ids.clear();
		auto iter = m_retinue_chat_ids.find(retinue_id);
		if (iter == m_retinue_chat_ids.end())
		{
			return;
		}
		std::vector<int> tmp_ids = iter->second;
		std::random_shuffle(tmp_ids.begin(), tmp_ids.end());
		if (m_random_chat_count >= tmp_ids.size())
		{
			ids = tmp_ids;
		}
		else if (m_random_chat_count > 0)
		{
			ids.resize(m_random_chat_count);
			std::copy(tmp_ids.begin(), tmp_ids.begin() + m_random_chat_count, ids.begin());
		}
	}
//#endif
	const townlet_pet_level_up_config_t* GetPetLevelUpConfig(int level) const
	{
		auto it = m_pet_level_up_configs.find(level);
		if (it == m_pet_level_up_configs.end())
		{
			return nullptr;
		}
		return &(it->second);
	}

	const townlet_pet_config_t* GetPetConfig(int tid) const
	{
		auto it = m_pet_configs.find(tid);
		if (it == m_pet_configs.end())
		{
			return nullptr;
		}
		return &(it->second);
	}

	const townlet_pet_food_config_t* GetPetFoodConfig(int tid) const
	{
		auto it = m_pet_food_configs.find(tid);
		if (it == m_pet_food_configs.end())
		{
			return nullptr;
		}
		return &(it->second);
	}

	int GetPetHealthReduce(int hungry) const
	{
		auto it = m_pet_health_reduce_configs.lower_bound(hungry);
		if (it == m_pet_health_reduce_configs.end())
		{
			return 0;
		}
		return it->second;
	}

	int GetPetControllerID(int level, int day_time, int active_controller_id) const
	{
		day_time += m_debug_pet_controller_day_time_offset;

		auto it = m_pet_controller_configs.find(level);
		if (it == m_pet_controller_configs.end())
		{
			return 0;
		}

		for (auto& v: it->second)
		{
			if (day_time - v.day_begin_time >= 0 && day_time - v.day_begin_time <= 3600)
			{
				// 判断是否处于开始时间段
				if (active_controller_id != 0)
				{
					// 如果已经开过了控制器，那么还返回相同的
					return active_controller_id;
				}
				else
				{
					// 随机一个控制器
					return v.controllers[abase::Rand(0, v.controllers.size() - 1)];
				}
			}
		}
		return 0;
	}

	const std::map<int, int>& GetAutoUnlockBuildings() const
	{
		return m_auto_unlock_building_configs;
	}

	const townlet_pet_produce_config_t* GetPetProduceConfig(int produce_type, int produce_id) const
	{
		auto it = m_pet_produce_configs.find(produce_type);
		if (it == m_pet_produce_configs.end())
		{
			return nullptr;
		}
		auto tit = it->second.find(produce_id);
		if (tit == it->second.end())
		{
			return nullptr;
		}
		return &(tit->second);
	}

	int GetPetProduceCountLimit(int level, int produce_type) const
	{
		auto it = m_pet_produce_count_limit_configs.find(level);
		if (it == m_pet_produce_count_limit_configs.end())
		{
			return 0;
		}
		if (produce_type <= 0 || produce_type > it->second.size())
		{
			return 0;
		}
		return (it->second)[produce_type - 1];
	}

	void DebugSetPetControllerDayTime(int day_time_offset)
	{
		m_debug_pet_controller_day_time_offset = day_time_offset;
	}

private:
	bool _debug_unlimit_coin = false;

	std::vector<townlet_level_up_config_t> m_level_up_configs;
	std::map<int, townlet_building_config_t> m_building_configs;
	int m_visit_item_max_count = 0;
	int m_travel_item_max_count = 0;
	int m_retinue_visit_cd_time = 0; //废弃
	int m_retinue_visit_duration = 0; //废弃
	int m_postcard_max_count = 0;
	int m_postcard_show_max_count = 0;
	int m_gain_postcard_rate = 0;
	int m_travel_common_reward = 0;
	int m_travel_duration = 0;
	int m_exchange_timeout_time = 30;
	int m_exchange_limit_id = 0;
	int m_debug_pet_controller_day_time_offset = 0;
	std::vector<int> m_max_visit_item_count = {};
	std::vector<int> m_max_event_item_count = {};
	std::vector<int> m_retinue_chat_common_use_limit_id = {};
	std::vector<int> m_retinue_chat_positive_add_repu = {};
	std::vector<int> m_retinue_chat_negative_redu_repu = {};
	int m_retinue_chat_max_change_count = 0;
	std::map<int, int> m_default_visit_ids = {};
	std::map<int, int> m_default_travel_event_ids = {};
	std::map<int, townlet_item_config_t> m_item_config = {};
	std::map<int, townlet_travel_event_config_t> m_travel_event_config = {};
	std::map<int, townlet_travel_postcard_config_t> m_postcard_config = {};
	std::map<int, travel_postcard_suit_config_t> m_postcard_suit_config = {};
	std::vector<travel_postcard_accum_reward_config_t> m_postcard_accum_reward_config = {};
	std::map<int, townlet_shop_item_config_t> m_shop_item_configs;
	std::map<int, int> m_shop_level_item_num;
	std::map<int, townlet_shop_level_up_config_t> m_shop_level_up_configs;
	std::map<int, townlet_partner_level_up_config_t> m_partner_level_up_configs;
	std::map<int, townlet_get_coin_config_t> m_get_coin_configs;
	std::map<int, retinue_chat_config_t> m_retinue_chat_config = {};
	std::map<int, retinue_chat_event_config_t> m_retinue_chat_event_config = {};
	std::map<int, float> m_level_exp_radio_configs;
	std::map<int, int> m_level_exp_limit_configs;
	std::map<int, int> _chat_event_2_retinue = {};
//#ifndef USE_TENCENT
	int m_random_chat_count = 0;
	std::map<int/*chat_id*/, retinue_chat_config> m_retinue_chat_map = {};
	std::map<int, std::map<int, retinue_chat_msg_config>> m_retinue_chat_msg_map = {};
	std::map<int, std::vector<int>> m_retinue_chat_ids = {};
//#endif
	std::map<int, int> m_level_pet_count_max;
	std::map<int, townlet_pet_level_up_config_t> m_pet_level_up_configs;
	std::map<int, townlet_pet_config_t> m_pet_configs;
	std::map<int, townlet_pet_food_config_t> m_pet_food_configs;
	std::map<int, int> m_pet_health_reduce_configs;
	std::map<int, std::vector<townlet_pet_controller_config_t>> m_pet_controller_configs;
	std::map<int, int> m_auto_unlock_building_configs;
	std::map<int/*produce_type*/, std::map<int/*produce_id*/, townlet_pet_produce_config_t>> m_pet_produce_configs;
	std::map<int/*level*/, std::vector<int/*count*/>> m_pet_produce_count_limit_configs;
};

#define TOWNLET_CONFIG_MANAGER player_townlet_config_manager::GetInstance()

class player_townlet
{
public:
	player_townlet(gplayer_imp* imp) : _imp(imp) { }

	void HandleClientOp(const PB::gp_townlet_op* request);
	void HandleClientOp_Enter(const PB::gp_townlet_op* request);
	void HandleClientOp_LevelUp(const PB::gp_townlet_op* request);
	void HandleClientOp_ChangeSkin(const PB::gp_townlet_op* request);

	//旅行
	void HandleClientOp_TravelItemSell(const PB::gp_townlet_op *request);
	void HandleClientOp_TravelStart(const PB::gp_townlet_op *request);
	void HandleClientOp_TravelGetReward(const PB::gp_townlet_op *request);
	void HandleClientOp_TravelSendGift(const PB::gp_townlet_op *request);
	void HandleClientOp_TravelLockReciveGift(const PB::gp_townlet_op *request);
	void HandleClientOp_TravelTakeOutGift(const PB::gp_townlet_op *request);
	void HandleClientOp_PostcardSell(const PB::gp_townlet_op *request);
	void HandleClientOp_PostcardGetReward(const PB::gp_townlet_op *request);
	void HandleClientOp_PostcardExchangeInvite(const PB::gp_townlet_op *request);
	void HandleClientOp_PostcardExchangeAgree(const PB::gp_townlet_op *request);
	void HandleClientOp_PostcardExchangeSelect(const PB::gp_townlet_op *request);
	void HandleClientOp_PostcardExchangeConfirm(const PB::gp_townlet_op *request);
	void HandleClientOp_PostcardExchangeCancel(const PB::gp_townlet_op *request);


	void HandleClientOp_BuildingLevelUp(const PB::gp_townlet_op* request);
	void HandleClientOp_ShopBuy(const PB::gp_townlet_op* request);
	void HandleClientOp_GetCoin(const PB::gp_townlet_op* request);
	void HandleClientOp_ChatChoose(const PB::gp_townlet_op *request);
	void HandleClientOp_ChatEventUnlock(const PB::gp_townlet_op *request);
	void HandleClientOp_ChatSendContent(const PB::gp_townlet_op *request);
	void HandleClientOp_DelPet(const PB::gp_townlet_op *request);
	void HandleClientOp_FeedPet(const PB::gp_townlet_op *request);
	void HandleClientOp_PetProduceBegin(const PB::gp_townlet_op *request);
	void HandleClientOp_PetProduceEnd(const PB::gp_townlet_op *request);

	bool Create();
	void OnLogin();
	void OnReconnect();
	void OnEnterScene();
	bool InSelfTownlet();
	bool CanSkinUnlock(int building_index, int skin_index);
	bool SkinUnlock(int building_index, int skin_index);
	void BroadcastOpRes(const PB::gp_townlet_op_re& notify);
	bool BuildingUnlock(int building_index);
	bool AddPet(int pet_id);

	void Load(gplayer_imp* imp, const PB::player_universal_data_t& pud);
	void Save(PB::player_universal_data_t& pud);
	void DailyUpdate();

	void OnHeartbeat(gplayer_imp*);
	void SendResponse(PB::TOWNLET_OP op_type, int retcode = 0, std::vector<int> notify_p_idxs = std::vector<int>());
	
	void OnChatContentRsp(PB::ipt_ai_chat& msg);
	bool IsChatEventComplete(int retinue_id, int event_id);
	bool IsChatEventUnlocked(int retinue_id, int event_id);
	void ChatEventComplete(int event_id);
	int GetCurChatEventId()
	{
		return _townlet_data.chat().cur_event_id();
	}
	int GetChatEventCompleteCount();
	void TryFixChatEvent();

	void PostcardDec(int postcard_id, int count = 1); //减少
	void PostcardAdd(int postcard_id, int count = 1, bool by_travel = false); //增加
	void PostcardSuitCountChange(int suit_id, int old_count, int new_count);
	void OnExchangeMsg(const MSG &msg);
	int GetPostcardCount(int postcard_id);
	void ExchangeFinish(int add_postcard_id, int del_postcard_id);
	void OnLeaveScene();
	void OnLostConnection();
	void TravelDebugSet(int op, int param1, int param2);
	void DebugRefreshShop();
	void CheckUnlockBuilding();
	void DebugSetProduceTime(int pet_index, int time_offset);

	static void CheckRefreshShop(ginstance_imp* pInst, ruid_t roleid);
	static void CheckPet(ginstance_imp* pInst, ruid_t roleid, int& active_pet_controller);
private:
	gplayer_imp* _imp;
	PB::db_townlet_data _townlet_data;
	bool _is_created = false;

	int _debug_visit_duration = 0;
	int _debug_gain_postcard_rate = 0;

	int _cur_chat_limit_id = 0; //当前聊天限次id
	int _cur_partner_build_lv = 0; //当前伙伴馆等级
	std::map<int, int> _m_postcard = {}; //明信片id->index
	std::map<int, int> _m_postcard_suit_count = {}; //明信片套装id->已有数量
	bool _is_postcard_prop_active = false;

	int __GetPostcardIdx(int postcard_id)
	{
		auto iter = _m_postcard.find(postcard_id);
		if (iter == _m_postcard.end())
		{
			return -1;
		}
		return iter->second;
	}
	void __TravelFinish();
	void __UpdateProp(int inc_id, int dec_id);

	bool __SyncInitData();
	int __ShopLevelUp(gplayer_imp* imp, int townlet_level, INTERPROCESS::ipd_townlet_zonedata_t& zonedata,
			const player_townlet_config_manager::townlet_building_config_t* pConfig, PB::gp_townlet_op_re& re);
	int __PartnerLevelUp(gplayer_imp* imp, int townlet_level, INTERPROCESS::ipd_townlet_zonedata_t& zonedata,
			const player_townlet_config_manager::townlet_building_config_t* pConfig, PB::gp_townlet_op_re& re);
	int __PetLevelUp(gplayer_imp* imp, int townlet_level, INTERPROCESS::ipd_townlet_zonedata_t& zonedata,
			const player_townlet_config_manager::townlet_building_config_t* pConfig, PB::gp_townlet_op_re& re);
	int __GetCoin(int level);
	void __ChatEventUnlock(int retinue_id, int event_id);
};

class player_pray_config_manager
{
public:
	enum pray_reward_type_t
	{
		PRT_REWARD_ID = 1,
		PRT_SKILL_ID = 2,

		PRT_MAX,
	};
	struct pray_config_t
	{
		int reward_type = 0;
		int reward_id	= 0;
		int odds		= 0;
		int activity_id	= 0;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			reward_type = v["reward_type"];
			reward_id = v["reward_id"];
			odds = v["odds"];
			activity_id = v["activity_id"];
		}
	};

	player_pray_config_manager(){ }

	static player_pray_config_manager& GetInstance()
	{
		static player_pray_config_manager man;
		return man;
	}

	bool LoadConfig(const char *path);

	const pray_config_t* GetConfig(int id) const
	{
		auto it = m_pray_configs.find(id);
		if (it == m_pray_configs.end())
		{
			return NULL;
		}
		return &(it->second);
	}
	int GetRandomID() const;

private:
	std::unordered_map<int, pray_config_t> m_pray_configs;
};

class player_pray
{
public:
	player_pray(gplayer_imp* imp) : _imp(imp) { }

	void Load(const PB::player_universal_data_t& pud);
	void Save(PB::player_universal_data_t& pud);
	void HandleClientOp(const PB::gp_player_pray_op &op);
	void OnLogin();
	void Reset();

private:
	int __Pray();
	int __Share(ruid_t target_roleid);
	int __Recv(int recv_index);
	bool __CheckIsFriend(ruid_t roleid);

private:
	gplayer_imp* _imp;
	PB::pray_data_t m_pray_data;
};

class player_personal_card_config_manager
{
public:
	struct player_personal_card_config_t
	{
		int enhance_id;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			enhance_id = v["enhance_id"];
		}
	};

private:
	player_personal_card_config_manager(){ }

public:
	static player_personal_card_config_manager& GetInstance()
	{
		static player_personal_card_config_manager man;
		return man;
	}

	bool LoadConfig(const char *path);
	const player_personal_card_config_t* GetConfig(int index) const
	{
		auto it = m_player_personal_card_configs.find(index);
		if (it == m_player_personal_card_configs.end())
		{
			return nullptr;
		}
		return &(it->second);
	}

private:
	std::map<int, player_personal_card_config_t> m_player_personal_card_configs;
};

// 个人名片
class player_personal_card
{
public:
	player_personal_card(gplayer_imp* imp) :
		_imp(imp), m_actived_cards(0), m_selected(0), m_actived(false) { }

	void Load(const PB::player_universal_data_t& pud);
	void Save(PB::player_universal_data_t& pud);
	void OnLogin();
	void OnEnterScene();
	void OnReconnect();

	bool CheckCanAdd(int personal_card_index) const;
	bool AddPersonalCard(int personal_card_index);

	void HandleClientOp(const PB::gp_personal_card_op &op);

	inline int GetSelected() const
	{
		return m_selected;
	}

	void SendClientData();

private:
	inline int GetPersonalCardIndexMax() const
	{
		return 64;
	}
	inline bool IsPersonalCardIndexValid(int personal_card_index) const
	{
		return personal_card_index >= 1 && personal_card_index <= GetPersonalCardIndexMax();
	}
	inline bool IsPersonalCardIndexActived(int personal_card_index) const
	{
		if (!IsPersonalCardIndexValid(personal_card_index))
		{
			return false;
		}
		if (m_actived_cards & (1 << (personal_card_index-1)))
		{
			return true;
		}
		return false;
	}

	void SelectPersonalCard(int personal_card_index);
	void ActiveEnhanceAll();

private:
	gplayer_imp* _imp;
	int64_t m_actived_cards;
	int m_selected;
	bool m_actived;
};

/*
 * 满足某些条件时对战斗数据进行抑制的辅助类
 */
class ConditionSuppressCombatStat
{
public:
	/*
	 * 战斗数据抑制系统枚举
	 * 需要与skillconvex.h中的functional_label保持一致
	 */
	enum
	{
		RETINUE = 6,        // 伙伴系统 SKILL_FUNCTIONAL_LABEL_06
		HOLY_GHOST = 7,     // 英灵系统 SKILL_FUNCTIONAL_LABEL_07
		DRAGONBORN = 8,     // 龙裔系统 SKILL_FUNCTIONAL_LABEL_08
		PET = 9,            // 宠物系统 SKILL_FUNCTIONAL_LABEL_09
		RUNESTONE = 10,     // 龙茧系统 SKILL_FUNCTIONAL_LABEL_10
		LONGWEN = 11,       // 龙纹系统 SKILL_FUNCTIONAL_LABEL_11
		SEVEN_CRIME = 15,   // 七戒系统 SKILL_FUNCTIONAL_LABEL_15
	};

	ConditionSuppressCombatStat(gcreature_imp* imp);

	ConditionSuppressCombatStat& CheckFunctionalLabel(int functional_label);
	ConditionSuppressCombatStat& Then(const std::function<void(gcreature_imp*)>& func, const std::string& desc);
	ConditionSuppressCombatStat& Else(const std::function<void(gcreature_imp*)>& func, const std::string& desc);
private:
	// 获取所在场景tag对应的抑制系统
	static void collectSuppressFunctionalLabelBySceneTag(std::set<int>& functional_label_set, gcreature_imp* imp);
	// 获取所在副本对应的抑制系统
	static void collectSuppressFunctionalLabelByInstance(std::set<int>& functional_label_set, gcreature_imp* imp);

	gcreature_imp* const _imp;
	std::set<int> _functional_label_set;
	bool _check_flag = false;
};

#endif

