#include "player_tetrahedron.h"
#include "luaconfread.h"
#include "gprotoc/role_complicated_data_tetrahedron.pb.h"
#include "gprotoc/gp_tetrahedron_op.pb.h"
#include "gprotoc/role_complicated_data.pb.h"
#include "gprotoc/player_tetrahedron_t.pb.h"
#include "gprotoc/kotodama_t.pb.h"
#include "gprotoc/player_universal_data_t.pb.h"
#include "gprotoc/gp_tetrahedron_op_result.pb.h"
#include "gprotoc/role_trade_tetrahedron_info.pb.h"

#include "player.h"

#include "item/item_estone.h"
#include "gmatrix.h"
#include "slog.h"
#include "item/item_estone.h"

/*
 * tetrahedron_config
 */
bool tetrahedron_config::Load(const char *path)
{
	LuaStateScoped k(path);
	if (!k.L)
	{
		return false;
	}
	LuaTableNode root;
	if (!LuaConfReader::parseFromLuaStack(k.L, root))
	{
		return false;
	}

	try
	{
		_hole_max_level = root["hole_max_level"];

		if (root.isMember(LuaAnyValue("spirit_gem_unlock_level")))
		{
			_spirit_gem_unlock_level = root["spirit_gem_unlock_level"];
		}

		auto& surface_tab = root["surface"].Tab();
		for (auto cit_surface = surface_tab.begin(); cit_surface != surface_tab.end(); ++cit_surface)
		{
			int surface_idx = cit_surface->first;
			auto& lua_surface = cit_surface->second.Tab();

			if (surface_idx <= 0 || surface_idx > SURFACE_COUNT)
			{
				printf("ERROR 魔盒配置错误: 无效的surface_idx(%d)\n", surface_idx);
				return false;
			}

			auto& surface = _surfaces[surface_idx];
			surface._type = lua_surface["type"];
			if (surface._type < 0 || surface._type >= EXPESTONETYPE_COUNT)
			{
				printf("ERROR 魔盒配置错误: 无效的surface_type(%d)\n", surface._type);
				return false;
			}
		}

		auto& vertex_tab = root["vertex"].Tab();
		for (auto cit_vertex = vertex_tab.begin(); cit_vertex != vertex_tab.end(); ++cit_vertex)
		{
			int vertex_idx = cit_vertex->first;
			auto& lua_vertex = cit_vertex->second.Tab();

			if (vertex_idx <= 0 || vertex_idx > VERTEX_COUNT)
			{
				printf("ERROR 魔盒配置错误: 无效的vertex_idx(%d)\n", vertex_idx);
				return false;
			}

			auto& vertex = _vertexes[vertex_idx];
			lua_vertex["surfaces"].Fetch(vertex._surfaces);
			vertex._unlock_level = lua_vertex["unlock_level"];

			// 推导查询数据
			for (int surface_idx : vertex._surfaces)
			{
				auto& surface = _surfaces[surface_idx];
				vertex._gem_types.insert(surface._type);
			}
		}

		auto& arris_tab = root["arris"].Tab();
		for (auto cit_arris = arris_tab.begin(); cit_arris != arris_tab.end(); ++cit_arris)
		{
			int arris_idx = cit_arris->first;
			auto& lua_arris = cit_arris->second.Tab();

			if (arris_idx <= 0 || arris_idx > ARRIS_COUNT)
			{
				printf("ERROR 魔盒配置错误: 无效的arris_idx(%d)\n", arris_idx);
				return false;
			}

			auto& arris = _arrises[arris_idx];
			lua_arris["surfaces"].Fetch(arris._surfaces);
			for (int i = 1; i <= HOLE_COUNT_PER_ARRIS; ++i)
			{
				arris._holes[i]._unlock_level = lua_arris[1]["unlock_level"];
			}

			// 推导查询数据
			for (int surface_idx : arris._surfaces)
			{
				auto& surface = _surfaces[surface_idx];
				arris._gem_types.insert(surface._type);
			}
		}

		// 升级所需经验整数数组
		root["refine_gem_lv_limit"].Fetch(_refine_gem_lv_limit);
		std::vector<int> refine_lv_exp_cfg_id_list;
		root["refine_lv_exp_cfg_id_list"].Fetch(refine_lv_exp_cfg_id_list);
		int cur_refine_lv = 0;
		int cur_refine_lv_total_exp = 0;
		for (auto refine_lv_exp_cfg_id : refine_lv_exp_cfg_id_list)
		{
			auto p_exp_list = INTEGER_ARRAY_CONFIG::get(refine_lv_exp_cfg_id);
			if (!p_exp_list)
			{
				printf("ERROR 魔盒配置错误: 精炼经验配置数组id=%d不存在\n", refine_lv_exp_cfg_id);
				return false;
			}
			for (auto exp : p_exp_list->data)
			{
				if (exp <= 0)
				{
					break;
				}
				++cur_refine_lv;
				// 按照遍历顺序，读取的经验值必须单调递增
				if (exp <= cur_refine_lv_total_exp)
				{
					printf("ERROR 魔盒配置错误: 精炼经验值必须单调递增，lv=%d, exp=%d\n", cur_refine_lv, exp);
					return false;
				}
				cur_refine_lv_total_exp = exp;
				_refine_lv_exp[cur_refine_lv_total_exp] = cur_refine_lv;
			}
		}
		root["refine"].Fetch(_refine);
		root["refine_stage"].Fetch(_refine_stage);
		_gem_upgrade_cost_item_id = root["gem_upgrade_cost_item_id"];
	}
	catch (std::exception& e)
	{
		printf("ERROR 魔盒配置(%s)加载错误: what=%s\n", path, e.what());
		return false;
	}
	return true;
}
void tetrahedron_config::OnMakeEstone(const item_estone *estone)
{
	if (!estone)
	{
		return;
	}
	const auto *ptempl = estone->GetTemplate();
	if (!ptempl || ptempl->GetTemplateType() != ITT_ESTONE)
	{
		return;
	}
	auto *estone_templ = (const item_template_estone *)ptempl;
	if (estone_templ->_stone_type < 0 || (int)estone_templ->_stone_type >= (int)SURFACE_COUNT)
	{
		return;
	}
	_gem_map[estone_templ->_stone_type][estone_templ->_stone_level] = ptempl->GetTID();
}

bool tetrahedron_config::CanGemAttach(int arris_idx, int hole_idx, int gem_type, int role_level, int max_gem_level) const
{
	if (arris_idx == 0) // 顶点
	{
		if (hole_idx <= 0 || hole_idx > VERTEX_COUNT)
		{
			return false;
		}

		auto& vertex = _vertexes[hole_idx];

		if (role_level < vertex._unlock_level) // 未到达解锁等级
		{
			return false;
		}
		if (vertex._gem_types.find(gem_type) == vertex._gem_types.end()) // 宝石类型不匹配
		{
			return false;
		}
		return true;
	}
	else if (arris_idx >= 1 && arris_idx <= ARRIS_COUNT)
	{
		if (hole_idx <= 0 || hole_idx > HOLE_COUNT_PER_ARRIS)
		{
			return false;
		}

		auto& arris = _arrises[arris_idx];
		auto& hole = arris._holes[hole_idx];

		if (role_level < hole._unlock_level) // 孔位未到达解锁等级
		{
			return false;
		}
		if (arris._gem_types.find(gem_type) == arris._gem_types.end()) // 宝石类型不匹配
		{
			return false;
		}
		return true;
	}
	else if (arris_idx == SPIRIT_GEM_ARRIS)
	{
		if (hole_idx <= 0 || hole_idx > SPIRIT_GEM_COUNT)
		{
			return false;
		}
		if (max_gem_level < _spirit_gem_unlock_level) // 孔位未到达解锁等级
		{
			return false;
		}
		return true;
	}
	else
	{
		return false;
	}
}
bool tetrahedron_config::IsIdxValid(int arris_idx, int hole_idx) const
{
	if (arris_idx < 0)
	{
		return false;
	}
	else if (arris_idx == 0) // 顶点
	{
		return hole_idx >= 1 && hole_idx <= VERTEX_COUNT;
	}
	else if (arris_idx <= ARRIS_COUNT) // 棱
	{
		return hole_idx >= 1 && hole_idx <= HOLE_COUNT_PER_ARRIS;
	}
	else if (arris_idx == SPIRIT_GEM_ARRIS) // 精神圣核插槽
	{
		return hole_idx >= 1 && hole_idx <= SPIRIT_GEM_COUNT;
	}
	else
	{
		return false;
	}
}
bool tetrahedron_config::CanGemUpgrade(int arris_idx, int hole_idx, int target_level) const
{
	if (!IsIdxValid(arris_idx, hole_idx))
	{
		return false;
	}
	if (target_level > _hole_max_level)
	{
		return false;
	}
	return true;
}
int tetrahedron_config::GetGemTID(int type, int level) const
{
	if (type < 0 || type >= SURFACE_COUNT)
	{
		return 0;
	}
	auto it = _gem_map[type].find(level);
	if (it == _gem_map[type].end())
	{
		return 0;
	}
	return it->second;
}
int tetrahedron_config::GetGemType(int gem_tid) const
{
	const item_template *ptempl = item_manager::GetInstance().GetItemTemplate(gem_tid);
	if (!ptempl || ptempl->GetTemplateType() != ITT_ESTONE)
	{
		return -1;
	}

	const item_template_estone *gem_templ = (const item_template_estone *)ptempl;
	if (!gem_templ)
	{
		return -1;
	}
	return (int)gem_templ->_stone_type;
}

int tetrahedron_config::CalcRefineLv(int refine_exp) const
{
	// _refine_lv_exp是降序排列的，配合std::greater<int>，lower_bound返回的是第一个大于等于refine_exp的元素
	auto it = _refine_lv_exp.lower_bound(refine_exp);
	if (it == _refine_lv_exp.end())
	{
		return 0;
	}
	return it->second;
}
int tetrahedron_config::CalcRefineExp(int refine_lv) const
{
	if (_refine_lv_exp.empty())
	{
		return 0;
	}
	if (refine_lv <= 0)
	{
		return 0;
	}
	const int max_lv = _refine_lv_exp.size();
	if (refine_lv > max_lv)
	{
		refine_lv = max_lv;
	}
	auto it = _refine_lv_exp.begin();
	std::advance(it, max_lv - refine_lv);
	if (it->second != refine_lv)
	{
		// 按理说不可能出现，防御一下
		return 0;
	}
	return it->first;
}

/*
 * player_tetrahedron
 */
void player_tetrahedron::tetrahedron_data_t::BuildFrom(const PB::player_tetrahedron_t& pb)
{
	// 顶点
	for (int vertex_idx = 0;
	        vertex_idx < tetrahedron_config::VERTEX_COUNT + 1 && vertex_idx < pb.vertexes_size();
	        ++vertex_idx)
	{
		_vertexes[vertex_idx] = pb.vertexes(vertex_idx);
	}
	// 棱
	for (int arris_idx = 0;
	        arris_idx < tetrahedron_config::ARRIS_COUNT + 1 && arris_idx < pb.arrises_size();
	        ++arris_idx)
	{
		auto& arris = pb.arrises(arris_idx);
		for (int hole_idx = 0;
		        hole_idx < tetrahedron_config::HOLE_COUNT_PER_ARRIS + 1 && hole_idx < arris.holes_size();
		        ++hole_idx)
		{
			_arrises[arris_idx]._holes[hole_idx] = arris.holes(hole_idx);
		}
	}
	// 精神核心
	for (int spirit_gem_idx = 0;
	        spirit_gem_idx < tetrahedron_config::SPIRIT_GEM_COUNT + 1 && spirit_gem_idx < pb.spirit_gems_size();
	        ++spirit_gem_idx)
	{
		_spirit_gems[spirit_gem_idx] = pb.spirit_gems(spirit_gem_idx);
	}

	_max_gem_level = pb.max_gem_level();
	_gfx_gem_level = pb.gfx_gem_level();
	_enable_gfx    = pb.enable_gfx();
	_max_spirit_gem_level = pb.max_spirit_gem_level();

	if (!_enable_gfx || _gfx_gem_level < 0) // 修正一下旧数据
	{
		_gfx_gem_level = 0;
	}

	// 精炼经验值
	for (int i = 0; i < pb.refine_exps().size(); i++)
	{
		_refine_exps.push_back(pb.refine_exps(i));
	}
}
void player_tetrahedron::tetrahedron_data_t::ExportTo(PB::player_tetrahedron_t& pb) const
{
	// 顶点
	pb.clear_vertexes();
	for (int vertex_idx = 0; vertex_idx < tetrahedron_config::VERTEX_COUNT + 1; ++vertex_idx)
	{
		pb.add_vertexes(_vertexes[vertex_idx]);
	}

	// 棱
	pb.clear_arrises();
	for (int arris_idx = 0; arris_idx < tetrahedron_config::ARRIS_COUNT + 1; ++arris_idx)
	{
		auto *arris = pb.add_arrises();
		for (int hole_idx = 0; hole_idx < tetrahedron_config::HOLE_COUNT_PER_ARRIS + 1; ++hole_idx)
		{
			arris->add_holes(_arrises[arris_idx]._holes[hole_idx]);
		}
	}

	// 精神核心
	pb.clear_spirit_gems();
	for (int spirit_gem_idx = 0; spirit_gem_idx < tetrahedron_config::SPIRIT_GEM_COUNT + 1; ++spirit_gem_idx)
	{
		pb.add_spirit_gems(_spirit_gems[spirit_gem_idx]);
	}

	pb.set_max_gem_level(_max_gem_level);
	pb.set_gfx_gem_level(_gfx_gem_level);
	pb.set_enable_gfx(_enable_gfx);
	pb.set_max_spirit_gem_level(_max_spirit_gem_level);

	// 精炼经验值
	for (int i = 0; i < _refine_exps.size(); i++)
	{
		pb.add_refine_exps(_refine_exps[i]);
	}

}
void player_tetrahedron::tetrahedron_data_t::ExportTo(PB::role_complicated_data_tetrahedron& pb) const
{
	// 顶点
	pb.clear_vertexes();
	for (int vertex_idx = 0; vertex_idx < tetrahedron_config::VERTEX_COUNT + 1; ++vertex_idx)
	{
		pb.add_vertexes(_vertexes[vertex_idx]);
	}

	// 棱
	pb.clear_arrises();
	for (int arris_idx = 0; arris_idx < tetrahedron_config::ARRIS_COUNT + 1; ++arris_idx)
	{
		auto *arris = pb.add_arrises();
		for (int hole_idx = 0; hole_idx < tetrahedron_config::HOLE_COUNT_PER_ARRIS + 1; ++hole_idx)
		{
			arris->add_holes(_arrises[arris_idx]._holes[hole_idx]);
		}
	}

	// 精神核心
	pb.clear_spirit_gems();
	for (int spirit_gem_idx = 0; spirit_gem_idx < tetrahedron_config::SPIRIT_GEM_COUNT + 1; ++spirit_gem_idx)
	{
		pb.add_spirit_gems(_spirit_gems[spirit_gem_idx]);
	}

	pb.set_max_gem_level(_max_gem_level);
	pb.set_gfx_gem_level(_gfx_gem_level);
	pb.set_max_spirit_gem_level(_max_spirit_gem_level);
}

void player_tetrahedron::Load(gcreature_imp *pimp, const PB::player_universal_data_t& pud)
{
	_data.BuildFrom(pud.tetrahedron());

	// 初始化套装光效
	if (_data._gfx_gem_level)
	{
		int suit_addon = 0;
		int gfx_id = 0;
		int suit_id = equipment_suit_manager::GetInstance().GetGemSuit(_data._gfx_gem_level, suit_addon, gfx_id);
		pimp->GetParent()->gem_gfx_suit_id = suit_id;
	}
}
void player_tetrahedron::Save(gplayer_imp *pimp, PB::player_universal_data_t& pud) const
{
	_data.ExportTo(*pud.mutable_tetrahedron());
}
void player_tetrahedron::SendClientData(gplayer_imp *pimp) const
{
	PB::gp_tetrahedron_op_result notify;
	notify.set_retcode(S2C::ERR_TETRAHEDRON_SUCCESS);
	notify.set_op_type(PB::gp_tetrahedron_op::OT_DATA);
	_data.ExportTo(*notify.mutable_data()->mutable_base_data());

	SendToClient(pimp, notify);
}
void player_tetrahedron::OnPlayerLogin(gcreature_imp *pimp)
{
	if (!_activated)
	{
		ActivateAllGem(pimp);
		UpdateGemLevel(pimp);

		for (int i = 0; i < _data._refine_exps.size(); i++)
		{
			if (_data._refine_exps[i] == 0)
			{
				continue;
			}
			int new_lv = tetrahedron_config::GetInstance().CalcRefineLv(_data._refine_exps[i]);
			if (new_lv > 0)
			{
				__UpdateRefineProp(pimp, i + 1, 0, new_lv);
			}
		}
		int stage_lv = GetRefineStageLv();
		auto *p_stage_cfg = tetrahedron_config::GetInstance().GetRefineStageConfig(stage_lv);
		if (p_stage_cfg && p_stage_cfg->_add_group_id)
		{
			creature_enhance_if cef(pimp);
			addon_manager::GetInstance().ActivateGroup(cef, p_stage_cfg->_add_group_id);
		}

		_activated = true;
	}
}

int player_tetrahedron::GetRefineStageLv()
{
	if (_data._refine_exps.size() != tetrahedron_config::GetInstance().GetRefineSize())
	{
		return 0;
	}
	int min_refine_lv = INT32_MAX;
	for (int i = 0; i < _data._refine_exps.size(); i++)
	{
		if (_data._refine_exps[i] == 0)
		{
			return 0;
		}
		int new_lv = tetrahedron_config::GetInstance().CalcRefineLv(_data._refine_exps[i]);
		if (min_refine_lv > new_lv)
		{
			min_refine_lv = new_lv;
		}
	}
	if (min_refine_lv == INT32_MAX)
	{
		return 0;
	}
	int stage_lv = tetrahedron_config::GetInstance().CalcRefineStageLV(min_refine_lv);
	return stage_lv;
}

void player_tetrahedron::MakeComplicatedData(PB::role_complicated_data_tetrahedron& pb) const
{
	_data.ExportTo(pb);
}

void player_tetrahedron::ClientOperation(gplayer_imp *pimp, const PB::gp_tetrahedron_op& cmd)
{
	CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeTetrahedron, pimp);

	if (!pimp || (pimp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid)))
	{
		return;
	}

	PB::gp_tetrahedron_op_result result;
	result.set_retcode(S2C::ERR_TETRAHEDRON_SUCCESS);
	result.set_op_type(cmd.op_type());

	switch (cmd.op_type())
	{
	case PB::gp_tetrahedron_op::OT_ATTACH:
	{
		auto& args = cmd.attach();
		result.set_retcode(AttachGem(pimp, args.arris_index(), args.hole_index(), args.gem_index(), args.gem_tid()));

		auto *res = result.mutable_attach();
		res->set_arris_index(args.arris_index());
		res->set_hole_index(args.hole_index());
		res->set_gem_tid(args.gem_tid());

		if (result.retcode() == S2C::ERR_TETRAHEDRON_SUCCESS)
		{
			UpdateGemLevel(pimp);
			pimp->GetAchievement().OnEuipStone(pimp); // 宝石成就检查要在UpdateGemLevel之后
		}
	}
	break;
	case PB::gp_tetrahedron_op::OT_DETACH:
	{
		auto& args = cmd.detach();
		result.set_retcode(DetachGem(pimp, args.arris_index(), args.hole_index(), args.gem_tid()));

		auto *res = result.mutable_detach();
		res->set_arris_index(args.arris_index());
		res->set_hole_index(args.hole_index());
		res->set_gem_tid(args.gem_tid());

		if (result.retcode() == S2C::ERR_TETRAHEDRON_SUCCESS)
		{
			UpdateGemLevel(pimp);
		}
	}
	break;
	case PB::gp_tetrahedron_op::OT_SELECT_GFX_SUIT:
	{
		auto& args = cmd.select_gfx_suit();
		result.set_retcode(SelectGfxSuit(pimp, args.level()));

		auto *res = result.mutable_select_gfx_suit();
		res->set_level(args.level());
	}
	break;
	case PB::gp_tetrahedron_op::OT_REFINE_UPGRADE:
	{
		auto& args = cmd.refine();
		result.set_retcode(RefineUpgrade(pimp, args.refine_id(), args.refine_old_exp(), args.refine_new_exp()));
		result.mutable_refine()->CopyFrom(args);
	}
	break;
	case PB::gp_tetrahedron_op::OT_COMPOSE:
	{
		auto& args = cmd.refine();
		result.set_retcode(GemCompose(pimp, args.up_item()));
		result.mutable_refine()->CopyFrom(args);
	}
	break;
	case PB::gp_tetrahedron_op::OT_DECOMPOSE:
	{
		auto& args = cmd.refine();
		result.set_retcode(GemDecompose(pimp, args.item_list()));
		result.mutable_refine()->CopyFrom(args);
	}
	break;
	default:
		SLOG(ERR, "GS::player_tetrahedron::ClientOperation: 无效的操作类型")
		.P("roleid", pimp->Parent()->ID.id)
		.P("op_type", cmd.op_type());

		result.set_retcode(S2C::ERR_TETRAHEDRON_FAIL);
		break;
	}

	SendToClient(pimp, result);
}

int player_tetrahedron::UpgradeGemInBackpack(gplayer_imp *pimp, int gem_idx, int target_tid)
{
	if (!pimp || (pimp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid)))
	{
		return 1;
	}
	if (target_tid <= 0)
	{
		return 2;
	}
	item_list& backpack = pimp->GetInventory().GetInventory(GNET::IL_BACKPACK);
	if (gem_idx < 0 || gem_idx >= backpack.Size())
	{
		return 3;
	}
	item *gem_it = backpack[gem_idx];
	if (!gem_it)
	{
		return 4;
	}
	int cur_tid = gem_it->GetTID();
	auto *ptempl = item_manager::GetInstance().GetItemTemplate(cur_tid);
	if (!ptempl || ptempl->GetTemplateType() != ITT_ESTONE)
	{
		return 5;
	}
	auto *ptempl_target = item_manager::GetInstance().GetItemTemplate(target_tid);
	if (!ptempl_target || ptempl_target->GetTemplateType() != ITT_ESTONE)
	{
		return 6;
	}
	auto *gem_templ = dynamic_cast<const item_template_estone *>(ptempl);
	auto *gem_templ_target = dynamic_cast<const item_template_estone *>(ptempl_target);
	if (!gem_templ || !gem_templ_target || gem_templ->_for_kids || gem_templ_target->_for_kids)
	{
		return 7;
	}
	int need_energy = gem_templ_target->_upgrade_energy - gem_templ->_upgrade_energy;
	if (need_energy <= 0 || pimp->GetReputation(GNET::REPUID_TETRAHEDRON_REFINE_ENERGY) < need_energy)
	{
		return 8;
	}
	int need_item_count = gem_templ_target->_upgrade_item_cost - gem_templ->_upgrade_item_cost;
	int need_item_id = tetrahedron_config::GetInstance().GetGemUpgradeItemCostId();
	if (need_item_id > 0 && need_item_count > 0 && pimp->GetItemExistAtBackpackCount(need_item_id, false) < need_item_count)
	{
		return 10;
	}
	int need_money_count = gem_templ_target->_upgrade_money_cost - gem_templ->_upgrade_money_cost;
	if (need_money_count && pimp->GetMoney(MT_BIND) < need_money_count)
	{
		return 11;
	}
	FuncInfo func_info{kFuncCodeTetrahedron, cur_tid, target_tid};
	gen_item_t gen;
	gen.tid = target_tid;
	gen.count = 1;
	item *pItem = item_manager::GetInstance().GenerateItem(IGT_TETRAHEDRON_UPGRADE, gen);
	if (!pItem)
	{
		return 12;

	}
	//检查是否能放的下物品
	if (!pimp->CheckCanIncItem(func_info, pItem))
	{
		delete pItem;
		pimp->error_message(GS_ERRMSGTYPE_NORMAL, SHOWERRMSG_CHAT, S2C::ERR_SELF_SELECT_REWARD_SLOT_NOT_ENOUGH);
		return 13;
	}
	pimp->DecItemAtBackpack(func_info, cur_tid, 1, false);
	pimp->DecItemAtBackpack(func_info, need_item_id, need_item_count, false);
	pimp->ModifyReputation(func_info, GNET::REPUID_TETRAHEDRON_REFINE_ENERGY, -need_energy);
	pimp->DecMoney(func_info, MT_BIND, need_money_count);
	if (!pimp->IncItem(func_info, pItem))
	{
		delete pItem;
		return 14;
	}
	return S2C::ERR_TETRAHEDRON_SUCCESS;
}
int player_tetrahedron::UpgradeGemInHole(gplayer_imp *pimp, int arris_idx, int hole_idx, int target_tid)
{
	if (!pimp || (pimp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid)))
	{
		return 1;
	}
	if (!tetrahedron_config::GetInstance().IsIdxValid(arris_idx, hole_idx) || target_tid <= 0)
	{
		return 2;
	}
	int cur_gem = _data.GetGemInHole(arris_idx, hole_idx);
	if (cur_gem == 0)
	{
		return 3;
	}
	auto *ptempl = item_manager::GetInstance().GetItemTemplate(cur_gem);
	if (!ptempl)
	{
		return 4;
	}
	auto *ptempl_target = item_manager::GetInstance().GetItemTemplate(target_tid);
	if (!ptempl_target || ptempl_target->GetTemplateType() != ITT_ESTONE)
	{
		return 5;
	}
	auto *gem_templ = dynamic_cast<const item_template_estone *>(ptempl);
	auto *gem_templ_target = dynamic_cast<const item_template_estone *>(ptempl_target);
	if (!gem_templ || !gem_templ_target || gem_templ->_for_kids || gem_templ_target->_for_kids)
	{
		return 7;
	}
	if (gem_templ_target->_min_equip_level > pimp->GetLevel())
	{
		return 8;
	}
	if (!tetrahedron_config::GetInstance().CanGemUpgrade(arris_idx, hole_idx, gem_templ_target->_stone_level))
	{
		return 9;
	}
	int need_energy = gem_templ_target->_upgrade_energy - gem_templ->_upgrade_energy;
	if (need_energy <= 0 || pimp->GetReputation(GNET::REPUID_TETRAHEDRON_REFINE_ENERGY) < need_energy)
	{
		return 10;
	}
	int need_item_count = gem_templ_target->_upgrade_item_cost - gem_templ->_upgrade_item_cost;
	int need_item_id = tetrahedron_config::GetInstance().GetGemUpgradeItemCostId();
	if (need_item_id > 0 && need_item_count > 0 && pimp->GetItemExistAtBackpackCount(need_item_id, false) < need_item_count)
	{
		return 11;
	}
	int need_money_count = gem_templ_target->_upgrade_money_cost - gem_templ->_upgrade_money_cost;
	if (need_money_count && pimp->GetMoney(MT_BIND) < need_money_count)
	{
		return 12;
	}
	FuncInfo func_info{kFuncCodeTetrahedron, cur_gem, target_tid};
	pimp->DecItemAtBackpack(func_info, need_item_id, need_item_count, false);
	pimp->ModifyReputation(func_info, GNET::REPUID_TETRAHEDRON_REFINE_ENERGY, -need_energy);
	pimp->DecMoney(func_info, MT_BIND, need_money_count);

	DeactivateGem(pimp, arris_idx, hole_idx);
	_data.MutableGemInHole(arris_idx, hole_idx) = target_tid;
	ActivateGem(pimp, arris_idx, hole_idx);

	UpdateGemLevel(pimp);
	pimp->GetAchievement().OnEuipStone(pimp); // 宝石成就检查要在UpdateGemLevel之后
	SendClientData(pimp);

	return S2C::ERR_TETRAHEDRON_SUCCESS;
}

/*
 * player_tetrahedron::private
 */
int player_tetrahedron::AttachGem(gplayer_imp *pimp, int arris_idx, int hole_idx, int gem_idx, int gem_tid)
{
	ASSERT(pimp);
	if (!tetrahedron_config::GetInstance().IsIdxValid(arris_idx, hole_idx))
	{
		return S2C::ERR_TETRAHEDRON_INVALID_IDX;
	}

	item_list& backpack = pimp->GetInventory().GetInventory(GNET::IL_BACKPACK);
	if (gem_idx >= backpack.Size())
	{
		return S2C::ERR_TETRAHEDRON_INVALID_GEM;
	}
	item *gem_it = backpack[gem_idx];
	if (!gem_it)
	{
		return S2C::ERR_TETRAHEDRON_INVALID_GEM;
	}
	if (gem_it->GetTID() != gem_tid)
	{
		return S2C::ERR_TETRAHEDRON_INVALID_GEM;
	}
	if (gem_it->IsSecurityLocked())
	{
		return S2C::ERR_TETRAHEDRON_INVALID_GEM;
	}

	const item_template *ptempl = item_manager::GetInstance().GetItemTemplate(gem_tid);
	if (!ptempl || ptempl->GetTemplateType() != ITT_ESTONE)
	{
		return S2C::ERR_TETRAHEDRON_INVALID_GEM;
	}

	const item_template_estone *gem_templ = (const item_template_estone *)ptempl;
	if (gem_templ->GetPlayerLevelRequire() > pimp->GetLevel())
	{
		return S2C::ERR_TETRAHEDRON_CONDITION;
	}
	if (gem_templ->_for_kids)
	{
		return S2C::ERR_TETRAHEDRON_CONDITION;
	}
	if (gem_templ->GetAddonGroupId() <= 0)
	{
		return S2C::ERR_TETRAHEDRON_CONFIG;
	}

	if (!tetrahedron_config::GetInstance().CanGemAttach(arris_idx, hole_idx, gem_templ->_stone_type, pimp->GetLevel(), GetGemMaxLevel()))
	{
		return S2C::ERR_TETRAHEDRON_CONDITION;
	}

	int& cur_gem = _data.MutableGemInHole(arris_idx, hole_idx);
	if (cur_gem > 0)
	{
		return S2C::ERR_TETRAHEDRON_GEM_ATTACHED;
	}
	cur_gem = gem_tid;
	pimp->DecItemAtIndex(GNET::IL_BACKPACK, gem_idx, 1, {kFuncCodeTetrahedron, arris_idx, hole_idx});
	ActivateGem(pimp, arris_idx, hole_idx);

	// for BI
	BI_LOG_GLOBAL(pimp->GetParent()->account);
	SLOG(FORMAT, "gem_attach_detach")
	.BI_HEADER2_GS(pimp)
	.P("gem_slot", hole_idx)
	.P("gem_tid", gem_tid)
	.P("gem_level", gem_templ->_stone_level)
	.P("action", 1) // 1-镶嵌
	.P("arris_idx", arris_idx)
	.P("gem_type", gem_templ->_stone_type);
	return S2C::ERR_TETRAHEDRON_SUCCESS;
}
int player_tetrahedron::DetachGem(gplayer_imp *pimp, int arris_idx, int hole_idx, int gem_tid)
{
	ASSERT(pimp);
	if (!tetrahedron_config::GetInstance().IsIdxValid(arris_idx, hole_idx))
	{
		return S2C::ERR_TETRAHEDRON_INVALID_IDX;
	}

	int& cur_gem = _data.MutableGemInHole(arris_idx, hole_idx);
	if (cur_gem <= 0)
	{
		return S2C::ERR_TETRAHEDRON_HOLE_DETACHED;
	}
	if (cur_gem != gem_tid)
	{
		return S2C::ERR_TETRAHEDRON_INVALID_GEM;
	}

	const item_template *pTemplate = item_manager::GetInstance().GetItemTemplate(gem_tid);
	if (!pTemplate || pTemplate->GetTemplateType() != ITT_ESTONE)
	{
		return S2C::ERR_TETRAHEDRON_INVALID_GEM;
	}
	const item_template_estone *gem_templ = (const item_template_estone *)pTemplate;

	gplayer_imp::TidCountMap gem_map;
	gem_map[gem_tid] = 1;
	unsigned int location_mask = 1 << GNET::IL_BACKPACK;
	if (!pimp->CheckCanIncItems(gem_map, location_mask))
	{
		return S2C::ERR_TETRAHEDRON_BACKPACK_FULL;
	}

	DeactivateGem(pimp, arris_idx, hole_idx);
	cur_gem = 0;

	gen_item_t gen;
	gen.tid = gem_tid;
	gen.count = 1;
	pimp->GenAndIncItem(IGT_TETRAHEDRON_DETACH_GEM, {kFuncCodeTetrahedron, arris_idx, hole_idx}, gen);

	// for BI
	BI_LOG_GLOBAL(pimp->GetParent()->account);
	SLOG(FORMAT, "gem_attach_detach")
	.BI_HEADER2_GS(pimp)
	.P("gem_slot", hole_idx)
	.P("gem_tid", gem_tid)
	.P("gem_level", gem_templ->_stone_level)
	.P("action", 2) // 2-摘除
	.P("arris_idx", arris_idx)
	.P("gem_type", gem_templ->_stone_type);
	return S2C::ERR_TETRAHEDRON_SUCCESS;
}
int player_tetrahedron::SelectGfxSuit(gplayer_imp *pimp, int level)
{
	ASSERT(pimp);

	if (level <= 0) // 取消光效
	{
#if 0
		// 策划说 不管玩家是否设置了“无光效” _enable_gfx都启用
		if (level < 0) // 不再自动刷新的光效
		{
			_data._enable_gfx = false;
		}
#endif
		if (_data._gfx_gem_level > 0)
		{
			SetGfx(pimp, 0, 0);
		}
		return S2C::ERR_TETRAHEDRON_SUCCESS;
	}

	if (_data._max_gem_level < level) // 玩家从未达到过的等级
	{
		return S2C::ERR_TETRAHEDRON_SUIT_LEVEL_LOW;
	}

	int new_suit_addon = 0;
	int gfx_id = 0;
	int new_suit_id = equipment_suit_manager::GetInstance().GetGemSuit(level, new_suit_addon, gfx_id);
	if (new_suit_id <= 0)
	{
		return S2C::ERR_TETRAHEDRON_CONFIG;
	}
	if (new_suit_id != pimp->GetParent()->gem_gfx_suit_id && gfx_id > 0)
	{
		SetGfx(pimp, level, new_suit_id);
	}

	_data._enable_gfx = true;
	return S2C::ERR_TETRAHEDRON_SUCCESS;
}

void player_tetrahedron::SetGfx(gcreature_imp *pimp, int level, int suit_id)
{
	_data._gfx_gem_level = level;
	pimp->SyncPlayerImportentInfoToDel();

	pimp->GetParent()->gem_gfx_suit_id = suit_id;
	pimp->Runner()->send_suit_info(pimp->GetParent()->enhance_gfx_suit_id, pimp->GetParent()->gem_gfx_suit_id, false);
}

void player_tetrahedron::__UpdateRefineProp(gcreature_imp *pImp, int refine_id, int old_lv, int new_lv)
{
	auto *p_cfg = tetrahedron_config::GetInstance().GetRefineCfg(refine_id);
	if (!p_cfg)
	{
		return;
	}
	__UpdateRefineProp(pImp, p_cfg->_enhance_id, p_cfg->_enhance_base_id, old_lv, new_lv);
}

void player_tetrahedron::__UpdateRefineProp(gcreature_imp *pImp, int e_cfg_id, int b_cfg_id, int old_lv, int new_lv)
{
	creature_enhance_if cef(pImp);
	auto p_enhance_config = ENHANCE_CONFIG::get(e_cfg_id);
	if (!p_enhance_config)
	{
		return;
	}
	if (old_lv > 0)
	{
		memset(&_tmp_base_prop, 0, sizeof(_tmp_base_prop));
		int config_idx = old_lv - 1;
		if (config_idx < 0 || config_idx >= p_enhance_config->configs.size())
		{
			return;
		}
		float ratio = p_enhance_config->configs[config_idx].basic_prop_addition_percent * 0.01f;
		PlayerBasePropEnhance(EBRT_TETRAHEDRON_REFINE, b_cfg_id, &_tmp_base_prop, ratio);
		cef.GetProperty().DecByStruct(_tmp_base_prop);
	}
	if (new_lv > 0)
	{
		memset(&_tmp_base_prop, 0, sizeof(_tmp_base_prop));
		int config_idx = new_lv - 1;
		if (config_idx < 0 || config_idx >= p_enhance_config->configs.size())
		{
			return;
		}
		float ratio = p_enhance_config->configs[config_idx].basic_prop_addition_percent * 0.01f;
		PlayerBasePropEnhance(EBRT_TETRAHEDRON_REFINE, b_cfg_id, &_tmp_base_prop, ratio);
		cef.GetProperty().IncByStruct(_tmp_base_prop);
	}
}

int player_tetrahedron::RefineUpgrade(gplayer_imp *pImp, int refine_id, int old_exp, int new_exp)
{
	if (!pImp)
	{
		return 1;
	}
	int old_lv = tetrahedron_config::GetInstance().CalcRefineLv(old_exp);
	int new_lv = tetrahedron_config::GetInstance().CalcRefineLv(new_exp);
	int old_stage_lv = GetRefineStageLv();
	SLOG(INFO, "player_tetrahedron::RefineUpgrade")
	.P("roleid", pImp->GetRoleID())
	.PS(refine_id)
	.PS(old_exp)
	.PS(old_lv)
	.PS(new_exp)
	.PS(new_lv)
	.PS(old_stage_lv);
	auto *p_cfg = tetrahedron_config::GetInstance().GetRefineCfg(refine_id);
	if (!p_cfg)
	{
		return 2;
	}
	int enhance_config_id = p_cfg->_enhance_id;
	if (enhance_config_id <= 0)
	{
		return 3;
	}
	auto p_enhance_config = ENHANCE_CONFIG::get(enhance_config_id);
	if (!p_enhance_config)
	{
		return 4;
	}
	int p_size = tetrahedron_config::GetInstance().GetRefineSize();
	if (_data._refine_exps.size() < p_size)
	{
		_data._refine_exps.resize(p_size, 0);
	}
	if (refine_id - 1 < 0 || refine_id > _data._refine_exps.size())
	{
		return 5;
	}
	int gem_min_lv = std::min(_cur_gem_level, _cur_spirit_gem_level);
	int max_lv = tetrahedron_config::GetInstance().GetMaxRefineLv(gem_min_lv);
	//判断等级是否合法
	if (old_exp >= new_exp || old_lv > new_lv || old_lv > p_enhance_config->configs.size()
	        || new_lv > p_enhance_config->configs.size() || new_lv > max_lv
	        || old_exp != _data._refine_exps[refine_id - 1])
	{
		return 6;
	}
	//判断能量
	if (pImp->GetReputation(GNET::REPUID_TETRAHEDRON_REFINE_ENERGY) < new_exp - old_exp)
	{
		return 7;
	}
	pImp->ModifyReputation({kFuncCodeTetrahedron, 0}, GNET::REPUID_TETRAHEDRON_REFINE_ENERGY, old_exp - new_exp);
	//加经验
	_data._refine_exps[refine_id - 1] = new_exp;
	//更新属性
	if (old_lv < new_lv)
	{
		__UpdateRefineProp(pImp, p_cfg->_enhance_id, p_cfg->_enhance_base_id, old_lv, new_lv);
	}
	int new_stage_lv = GetRefineStageLv();
	if (old_stage_lv < new_stage_lv)
	{
		creature_enhance_if cef(pImp);
		auto *p_old_stage_cfg = tetrahedron_config::GetInstance().GetRefineStageConfig(old_stage_lv);
		if (p_old_stage_cfg && p_old_stage_cfg->_add_group_id)
		{
			addon_manager::GetInstance().DeactivateGroup(cef, p_old_stage_cfg->_add_group_id);
		}
		auto *p_new_stage_cfg = tetrahedron_config::GetInstance().GetRefineStageConfig(new_stage_lv);
		if (p_new_stage_cfg && p_new_stage_cfg->_add_group_id)
		{
			addon_manager::GetInstance().ActivateGroup(cef, p_new_stage_cfg->_add_group_id);
		}
	}

	BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
	SLOG(FORMAT, "tetrahedron_refine_upgrade")
	.BI_HEADER2_GS(pImp)
	.PS(refine_id)
	.PS(old_exp)
	.PS(old_lv)
	.PS(new_exp)
	.PS(new_lv)
	.PS(gem_min_lv)
	.PS(old_stage_lv)
	.PS(new_stage_lv);
	return S2C::ERR_TETRAHEDRON_SUCCESS;
}

int player_tetrahedron::GemCompose(gplayer_imp *pimp, const PB::tetrahedron_refine_t_NS::tetrahedron_refine_t_upgrade_item_info& up_item_info)
{
	if (!pimp)
	{
		return 1;
	}
	int location = up_item_info.location();
	int arris_idx = up_item_info.index();
	int hole_idx = up_item_info.slot();
	int backpack_index = up_item_info.slot();
	int target_tid = up_item_info.to_item_id();
	int ret = 0;
	if (location == GNET::IL_TETRAHEDRON)
	{
		ret = UpgradeGemInHole(pimp, arris_idx, hole_idx, target_tid);
	}
	else
	{
		ret = UpgradeGemInBackpack(pimp, backpack_index, target_tid);
	}
	SLOG(INFO, "player_tetrahedron::GemCompose")
	.P("roleid", pimp->GetRoleID())
	.PS(location)
	.PS(arris_idx)
	.PS(hole_idx)
	.PS(target_tid)
	.PS(ret);
	return ret;
}

int player_tetrahedron::GemDecompose(gplayer_imp *pimp, const ::google::protobuf::RepeatedPtrField<PB::tetrahedron_refine_t_NS::tetrahedron_refine_t_item_info>& item_list)
{
	if (!pimp)
	{
		return 1;
	}

	auto& backpack = pimp->GetInventory().GetInventory(GNET::IL_BACKPACK);
	int total_energy = 0;
	std::stringstream ss_cost;
	for (int i = 0; i < item_list.size(); i++)
	{
		int item_index = item_list.Get(i).item_idx();
		int item_count = item_list.Get(i).item_count();
		auto *p_item = backpack[item_index];
		if (!p_item || item_count <= 0 || p_item->GetCount() < item_count)
		{
			return 2;
		}
		int gem_tid = p_item->GetTID();
		const item_template *pTemplate = item_manager::GetInstance().GetItemTemplate(gem_tid);
		if (!pTemplate || pTemplate->GetTemplateType() != ITT_ESTONE)
		{
			return 3;
		}
		auto *gem_templ = dynamic_cast<const item_template_estone *>(pTemplate);
		if (!gem_templ || gem_templ->_for_kids)
		{
			return 4;
		}
		total_energy += gem_templ->_decompose_energy * item_count;
		ss_cost << "(" << item_list.Get(i).item_idx() << "," << gem_tid << "," << item_list.Get(i).item_count() << "),";
	}
	for (int i = 0; i < item_list.size(); i++)
	{
		pimp->GetInventory().DecItemAtIndex(GNET::IL_BACKPACK, item_list.Get(i).item_idx(), item_list.Get(i).item_count(), {kFuncCodeTetrahedron});
	}
	pimp->ModifyReputation({kFuncCodeTetrahedron, 0}, GNET::REPUID_TETRAHEDRON_REFINE_ENERGY, total_energy);

	SLOG(INFO, "player_tetrahedron::GemDecompose")
	.P("roleid", pimp->GetRoleID())
	.P("item_list_size", item_list.size())
	.P("cost_items", ss_cost.str())
	.PS(total_energy);
	return S2C::ERR_TETRAHEDRON_SUCCESS;
}

void player_tetrahedron::ActivateGem(gcreature_imp *pimp, int arris_idx, int hole_idx) const
{
	ASSERT(pimp);
	if (!tetrahedron_config::GetInstance().IsIdxValid(arris_idx, hole_idx))
	{
		return;
	}

	int gem_tid = _data.GetGemInHole(arris_idx, hole_idx);
	if (gem_tid <= 0)
	{
		return;
	}

	auto *ptempl = item_manager::GetInstance().GetItemTemplate(gem_tid);
	if (!ptempl || ptempl->GetTemplateType() != ITT_ESTONE)
	{
		return;
	}
	const item_template_estone *gem_templ = (const item_template_estone *)ptempl;

	creature_enhance_if cef(pimp);
	addon_manager::GetInstance().ActivateGroup(cef, gem_templ->GetAddonGroupId());
}
void player_tetrahedron::DeactivateGem(gplayer_imp *pimp, int arris_idx, int hole_idx) const
{
	ASSERT(pimp);
	if (!tetrahedron_config::GetInstance().IsIdxValid(arris_idx, hole_idx))
	{
		return;
	}

	int gem_tid = _data.GetGemInHole(arris_idx, hole_idx);
	if (gem_tid <= 0)
	{
		return;
	}

	auto *ptempl = item_manager::GetInstance().GetItemTemplate(gem_tid);
	if (!ptempl || ptempl->GetTemplateType() != ITT_ESTONE)
	{
		return;
	}
	const item_template_estone *gem_templ = (const item_template_estone *)ptempl;

	creature_enhance_if cef(pimp);
	addon_manager::GetInstance().DeactivateGroup(cef, gem_templ->GetAddonGroupId());
}
void player_tetrahedron::ActivateAllGem(gcreature_imp *pimp) const
{
	for (int vertex_idx = 1; vertex_idx < tetrahedron_config::VERTEX_COUNT + 1; ++vertex_idx)
	{
		ActivateGem(pimp, 0, vertex_idx);
	}
	for (int arris_idx = 1; arris_idx < tetrahedron_config::ARRIS_COUNT + 1; ++arris_idx)
	{
		for (int hole_idx = 1; hole_idx < tetrahedron_config::HOLE_COUNT_PER_ARRIS + 1; ++hole_idx)
		{
			ActivateGem(pimp, arris_idx, hole_idx);
		}
	}
	for (int spirit_gem_idx = 1; spirit_gem_idx < tetrahedron_config::SPIRIT_GEM_COUNT + 1; ++spirit_gem_idx)
	{
		ActivateGem(pimp, tetrahedron_config::SPIRIT_GEM_ARRIS, spirit_gem_idx);
	}
}
void player_tetrahedron::UpdateGemLevel(gcreature_imp *pimp)
{
	int old_gem_level = _cur_gem_level;
	_cur_gem_level = INT_MAX;
	_gem_level_counter.Clear();

	// 顶点
	for (int i = 1; i < tetrahedron_config::VERTEX_COUNT + 1; ++i)
	{
		int gem_tid = _data._vertexes[i];
		if (gem_tid <= 0) // 该孔位未镶嵌宝石
		{
			_cur_gem_level = 0;
			continue;
		}

		const item_template *ptempl = item_manager::GetInstance().GetItemTemplate(gem_tid);
		if (!ptempl || ptempl->GetTemplateType() != ITT_ESTONE) // 该孔位镶嵌了无效的宝石 never
		{
			_cur_gem_level = 0;
			continue;
		}

		auto *gem_templ = (const item_template_estone *)ptempl;
		if (_cur_gem_level > gem_templ->_stone_level)
		{
			_cur_gem_level = gem_templ->_stone_level;
		}
		_gem_level_counter.Increase(gem_templ->_stone_level);
	}
	// 棱
	for (int i = 1; i < tetrahedron_config::ARRIS_COUNT + 1; ++i)
	{
		for (int j = 1; j < tetrahedron_config::HOLE_COUNT_PER_ARRIS + 1; ++j)
		{
			int gem_tid = _data._arrises[i]._holes[j];
			if (gem_tid <= 0) // 该孔位未镶嵌宝石
			{
				_cur_gem_level = 0;
				continue;
			}

			const item_template *ptempl = item_manager::GetInstance().GetItemTemplate(gem_tid);
			if (!ptempl || ptempl->GetTemplateType() != ITT_ESTONE) // 该孔位镶嵌了无效的宝石 never
			{
				_cur_gem_level = 0;
				continue;
			}

			auto *gem_templ = (const item_template_estone *)ptempl;
			if (_cur_gem_level > gem_templ->_stone_level)
			{
				_cur_gem_level = gem_templ->_stone_level;
			}
			_gem_level_counter.Increase(gem_templ->_stone_level);
		}
	}

	if (_cur_gem_level == INT_MAX) // 魔盒未镶嵌宝石
	{
		_cur_gem_level = 0;
	}

	// 更新历史最高等级
	if (_cur_gem_level > _data._max_gem_level)
	{
		int old_max_gem_level = _data._max_gem_level;
		_data._max_gem_level = _cur_gem_level;

		int last_valid_gfx_level = _data._gfx_gem_level;
		int last_valid_gfx_suit  = 0;

		// for BI
		GNET::BIPlayerInfo bi;
		gplayer_imp *pImp = NULL;
		if (pimp->IsPlayerClass())
		{
			pImp = (gplayer_imp *)pimp;
			INTERPROCESS::interprocess_data *ipd = GetInterProcessData();
			if (ipd)
			{
				ipd->ip_bi_player_info_manager.Get(pImp->GetParent()->account, bi);
			}
		}
		// 解锁(old_max_gem_level, _data._max_gem_level]区间的套装言灵
		for (int i = old_max_gem_level + 1; i <= _data._max_gem_level; ++i)
		{
			int addon = 0, kotodama_index = 0;
			int gfx_id  = 0;
			int suit_id = equipment_suit_manager::GetInstance().GetGemSuit(i, addon, gfx_id, &kotodama_index);
			if (suit_id <= 0) // 没有套装
			{
				continue;
			}

			if (gfx_id > 0)
			{
				last_valid_gfx_level = i;
				last_valid_gfx_suit  = suit_id;
			}

			if (pimp->IsPlayerClass())
			{
				SLOG(FORMAT, "gem_suit_active")
				.BI_HEADER2_GS(pImp)
				.P("gem_suit_level", i)
				.P("gem_suit_id", suit_id)
				.P("kotodama_id", kotodama_index)
				.P("weapon_gfx_id", gfx_id > 0 ? gfx_id : 0);
			}

			if (kotodama_index <= 0) // 没有言灵
			{
				continue;
			}
			player_kotodama *kotodama = pimp->GetKotodamaP();
			if (kotodama == NULL)
			{
				continue;//不会走到这里
			}
			auto *kotodama_templ = kotodama_template_manager::GetInstance().GetKotodamaTempalte(kotodama_index);
			if (!kotodama_templ)
			{
				continue;
			}
			if (!kotodama->CheckLearn(*kotodama_templ, KOTODAMA_UNLOCK_TYPE_BY_TETRAHEDRON))
			{
				continue;
			}

			kotodama->Learn(*kotodama_templ, KOTODAMA_UNLOCK_TYPE_BY_TETRAHEDRON);

		}

		if (_data._enable_gfx && last_valid_gfx_level != _data._gfx_gem_level)
		{
			SetGfx(pimp, last_valid_gfx_level, last_valid_gfx_suit);
		}
	}

	auto changeAddons = [pimp](int old_level, int cur_level, void (equipment_suit_manager::*GetAddons)(int, int, std::vector<int>&) const)
	{
		if (old_level == cur_level)
		{
			return;
		}

		int  level_lower = old_level;
		int  level_upper = cur_level;
		bool do_activate = cur_level > old_level;
		if (!do_activate)
		{
			std::swap(level_lower, level_upper);
		}

		std::vector<int> addon_groups;
		(equipment_suit_manager::GetInstance().*GetAddons)(level_lower, level_upper, addon_groups);
		if (addon_groups.empty())
		{
			return;
		}

		creature_enhance_if cei(pimp);
		for (int ag : addon_groups)
		{
			if (do_activate)
			{
				addon_manager::GetInstance().ActivateGroup(cei, ag);
			}
			else
			{
				addon_manager::GetInstance().DeactivateGroup(cei, ag);
			}
		}
	};
	// 刷新附加属性
	changeAddons(old_gem_level, _cur_gem_level, &equipment_suit_manager::GetGemSuitAddonGroupsBetween);

	// 精神圣核套装属性
	int old_spirit_gem_level = _cur_spirit_gem_level;
	_cur_spirit_gem_level = INT_MAX;
	for (int spirit_gem_idx = 1; spirit_gem_idx < tetrahedron_config::SPIRIT_GEM_COUNT + 1; ++spirit_gem_idx)
	{
		int gem_tid = _data._spirit_gems[spirit_gem_idx];
		if (gem_tid <= 0) // 该孔位未镶嵌宝石
		{
			_cur_spirit_gem_level = 0;
			continue;
		}

		const item_template *ptempl = item_manager::GetInstance().GetItemTemplate(gem_tid);
		if (!ptempl || ptempl->GetTemplateType() != ITT_ESTONE) // 该孔位镶嵌了无效的宝石 never
		{
			_cur_spirit_gem_level = 0;
			continue;
		}

		auto *gem_templ = (const item_template_estone *)ptempl;
		if (_cur_spirit_gem_level > gem_templ->_stone_level)
		{
			_cur_spirit_gem_level = gem_templ->_stone_level;
		}
		_gem_level_counter.Increase(gem_templ->_stone_level);
	}

	_gem_level_counter.BackwardPropagate();

	if (_cur_spirit_gem_level == INT_MAX) // 魔盒未镶嵌宝石
	{
		_cur_spirit_gem_level = 0;
	}

	// 更新历史最高等级
	if (_cur_spirit_gem_level > _data._max_spirit_gem_level)
	{
		_data._max_spirit_gem_level = _cur_spirit_gem_level;
	}

	// 精神圣核套装属性
	changeAddons(old_spirit_gem_level, _cur_spirit_gem_level, &equipment_suit_manager::GetSpiritGemSuitAddonGroupsBetween);
}

float player_tetrahedron::GetGemLevelAverage() const
{
	int count = 0;
	int level = 0;

	// 顶点, 4
	for (int i = 1; i < tetrahedron_config::VERTEX_COUNT + 1; ++i)
	{
		++count;
		int gem_tid = _data._vertexes[i];
		if (gem_tid <= 0) // 该孔位未镶嵌宝石
		{
			continue;
		}

		const item_template *ptempl = item_manager::GetInstance().GetItemTemplate(gem_tid);
		if (!ptempl || ptempl->GetTemplateType() != ITT_ESTONE)
		{
			continue;
		}
		auto *gem_templ = (const item_template_estone *)ptempl;
		level += gem_templ->_stone_level;
	}
	// 棱 6X2
	for (int i = 1; i < tetrahedron_config::ARRIS_COUNT + 1; ++i)
	{
		for (int j = 1; j < tetrahedron_config::HOLE_COUNT_PER_ARRIS + 1; ++j)
		{
			++count;
			int gem_tid = _data._arrises[i]._holes[j];
			if (gem_tid <= 0)
			{
				continue;
			}

			const item_template *ptempl = item_manager::GetInstance().GetItemTemplate(gem_tid);
			if (!ptempl || ptempl->GetTemplateType() != ITT_ESTONE)
			{
				continue;
			}
			auto *gem_templ = (const item_template_estone *)ptempl;
			level += gem_templ->_stone_level;
		}
	}
	//精神 8
	for (int spirit_gem_idx = 1; spirit_gem_idx < tetrahedron_config::SPIRIT_GEM_COUNT + 1; ++spirit_gem_idx)
	{
		++count;
		int gem_tid = _data._spirit_gems[spirit_gem_idx];
		if (gem_tid <= 0)
		{
			continue;
		}

		const item_template *ptempl = item_manager::GetInstance().GetItemTemplate(gem_tid);
		if (!ptempl || ptempl->GetTemplateType() != ITT_ESTONE)
		{
			continue;
		}

		auto *gem_templ = (const item_template_estone *)ptempl;
		level += gem_templ->_stone_level;
	}
	if (count <= 0)
	{
		return 0;
	}
	return (float)level / count;
}

void player_tetrahedron::SendToClient(gplayer_imp *pimp, const PB::gp_tetrahedron_op_result& result) const
{
	pimp->Runner()->CommonSend<S2C::CMD::PBS2C>(result);
}

void player_tetrahedron::MakeInfoLog(std::stringstream& activeKoto, std::stringstream& activeGfx, std::stringstream& allGems, std::stringstream& spiritGems)
{
	for (int i = 1; i <= _data._max_gem_level; ++i)
	{
		int addon = 0, kotodama_index = 0;
		int gfx_id  = 0;
		int suit_id = equipment_suit_manager::GetInstance().GetGemSuit(i, addon, gfx_id, &kotodama_index);
		if (suit_id <= 0) // 没有套装
		{
			continue;
		}
		if (i > 1)
		{
			activeKoto << ",";
			activeGfx << ",";
		}
		activeKoto << kotodama_index;
		activeGfx << (gfx_id > 0 ? gfx_id : 0);
	}
	// 宝石tid,宝石arrise,宝石hole_index,宝石类型,宝石等级
	auto FuncMakeGemInfo = [](std::stringstream & allGems, int gem_tid, int arris_idx, int hole_idx)
	{
		const item_template *ptempl = item_manager::GetInstance().GetItemTemplate(gem_tid);
		if (!ptempl || ptempl->GetTemplateType() != ITT_ESTONE)
		{
			return;
		}

		const item_template_estone *gem_templ = (const item_template_estone *)ptempl;
		if (allGems.tellp() > 0)
		{
			allGems << ";";
		}
		allGems << gem_tid << "," << arris_idx << "," << hole_idx << "," << gem_templ->_stone_type << "," << gem_templ->_stone_level;
	};
	for (int i = 1; i <= tetrahedron_config::VERTEX_COUNT; ++i)
	{
		if (_data._vertexes[i] != 0)
		{
			FuncMakeGemInfo(allGems, _data._vertexes[i], 0, i);
		}
	}

	for (int i_arris = 1; i_arris <= tetrahedron_config::ARRIS_COUNT; ++ i_arris)
	{
		for (int i_hole = 0; i_hole <= tetrahedron_config::HOLE_COUNT_PER_ARRIS; ++ i_hole)
		{
			if (_data._arrises[i_arris]._holes[i_hole] != 0)
			{
				FuncMakeGemInfo(allGems, _data._arrises[i_arris]._holes[i_hole], i_arris, i_hole);
			}
		}
	}
	for (int i = 1; i <= tetrahedron_config::SPIRIT_GEM_COUNT; ++i)
	{
		if (_data._spirit_gems[i] != 0)
		{
			FuncMakeGemInfo(spiritGems, _data._spirit_gems[i], tetrahedron_config::SPIRIT_GEM_ARRIS, i);
		}
	}
}

int player_tetrahedron::GetTotalSpiritGemLevel() const
{
	int total_level = 0;
	for (int i = 1; i <= tetrahedron_config::SPIRIT_GEM_COUNT; ++i)
	{
		int gem_tid = _data._spirit_gems[i];
		if (gem_tid == 0)
		{
			continue;
		}
		const item_template *ptempl = item_manager::GetInstance().GetItemTemplate(gem_tid);
		if (!ptempl || ptempl->GetTemplateType() != ITT_ESTONE)
		{
			continue;
		}

		const item_template_estone *gem_templ = (const item_template_estone *)ptempl;
		total_level += gem_templ->_stone_level;
	}
	return total_level;
}

/*
 * Debug
 */
void player_tetrahedron::DebugForceLevel(gplayer_imp *pimp, int level)
{
	memset(_data._vertexes, 0, sizeof(_data._vertexes));
	memset(_data._arrises, 0, sizeof(_data._arrises));
	_data._max_gem_level = 0;
	_data._gfx_gem_level = 0;

	int type_list[tetrahedron_config::SURFACE_COUNT] = {1, 0, 3, 2};
	for (int type : type_list)
	{
		int gem_tid = tetrahedron_config::GetInstance().GetGemTID(type, level);
		if (gem_tid <= 0)
		{
			continue;
		}

		// 顶点
		for (int vertex_idx = 1; vertex_idx <= tetrahedron_config::VERTEX_COUNT; ++vertex_idx)
		{
			if (_data._vertexes[vertex_idx])
			{
				continue;
			}
			if (!tetrahedron_config::GetInstance().CanGemAttach(0, vertex_idx, type, pimp->GetLevel()))
			{
				continue;
			}
			_data._vertexes[vertex_idx] = gem_tid;
		}
		// 棱
		for (int arris_idx = 1; arris_idx <= tetrahedron_config::ARRIS_COUNT; ++arris_idx)
		{
			for (int hole_idx = 1; hole_idx <= tetrahedron_config::HOLE_COUNT_PER_ARRIS; ++hole_idx)
			{
				if (_data._arrises[arris_idx]._holes[hole_idx])
				{
					continue;
				}
				if (!tetrahedron_config::GetInstance().CanGemAttach(arris_idx, hole_idx, type, pimp->GetLevel()))
				{
					continue;
				}
				_data._arrises[arris_idx]._holes[hole_idx] = gem_tid;
			}
		}
	}

	UpdateGemLevel(pimp);
	pimp->GetAchievement().OnEuipStone(pimp);
	SendClientData(pimp);
}

void player_tetrahedron::DebugForceSpiritLevel(gplayer_imp *pimp, int level)
{
	memset(_data._spirit_gems, 0, sizeof(_data._spirit_gems));
	int gem_tid = tetrahedron_config::GetInstance().GetGemTID(EXPESTONETYPE_SPIRIT, level);
	if (gem_tid <= 0)
	{
		return;
	}
	// 顶点
	for (int spirit_gem_idx = 1; spirit_gem_idx < tetrahedron_config::SPIRIT_GEM_COUNT + 1; ++spirit_gem_idx)
	{
		if (_data._spirit_gems[spirit_gem_idx])
		{
			continue;
		}
		if (!tetrahedron_config::GetInstance().CanGemAttach(tetrahedron_config::SPIRIT_GEM_ARRIS, spirit_gem_idx, EXPESTONETYPE_SPIRIT, pimp->GetLevel(), GetGemMaxLevel()))
		{
			continue;
		}
		_data._spirit_gems[spirit_gem_idx] = gem_tid;
	}

	UpdateGemLevel(pimp);
	pimp->GetAchievement().OnEuipStone(pimp);
	SendClientData(pimp);
}

void player_tetrahedron::MakeRoleTradeTetrahedronInfo(gplayer_imp *pimp, PB::role_trade_tetrahedron_info& t_info)
{
	t_info.set_score(pimp->GetPlayerMisc().fight_cap_cache().gem_fc_value() + pimp->GetPlayerMisc().fight_cap_cache().spirit_gem_fc_value());
#define SWITCH_GEM(gem_tid) \
		if (gem_tid <= 0) \
		{ \
			continue; \
		} \
		int gem_type = tetrahedron_config::GetInstance().GetGemType(gem_tid); \
		switch (gem_type) \
		{ \
			case (int)EXPESTONETYPE_EARTH: \
				{ \
					t_info.add_earth(gem_tid); \
				} \
				break; \
			case (int)EXPESTONETYPE_FIRE: \
				{ \
					t_info.add_fire(gem_tid); \
				} \
				break; \
			case (int)EXPESTONETYPE_WATER: \
				{ \
					t_info.add_water(gem_tid); \
				} \
				break; \
			case (int)EXPESTONETYPE_WIND: \
				{ \
					t_info.add_wind(gem_tid); \
				} \
				break; \
			case (int)EXPESTONETYPE_SPIRIT: \
				{ \
					t_info.add_spirit(gem_tid); \
				} \
				break; \
			default: \
				break; \
		}

	for (int i = 1; i < tetrahedron_config::VERTEX_COUNT + 1; ++i)
	{
		int gem_tid = _data._vertexes[i];
		SWITCH_GEM(gem_tid)
	}
	for (int i = 1; i < tetrahedron_config::ARRIS_COUNT + 1; ++i)
	{
		for (int j = 1; j < tetrahedron_config::HOLE_COUNT_PER_ARRIS + 1; ++j)
		{
			int gem_tid = _data._arrises[i]._holes[j];
			SWITCH_GEM(gem_tid)
		}
	}
	for (int i = 1; i < tetrahedron_config::SPIRIT_GEM_COUNT + 1; ++i)
	{
		int gem_tid = _data._spirit_gems[i];
		SWITCH_GEM(gem_tid)
	}

#undef SWITCH_GEM
}

void player_tetrahedron::IDIPDelTetrahedron(gcreature_imp *pimp, int category, int level)
{
	LOG_TRACE("player_tetrahedron::IDIPDelTetrahedron::roleid=%ld:category=%d:level=%d", pimp->GetRoleID(), category, level);
	bool success = false;
	for (int vertex_idx = 1; !success &&  vertex_idx < tetrahedron_config::VERTEX_COUNT + 1; ++vertex_idx)
	{
		success = IDIPDetachGem(pimp, 0, vertex_idx, category, level);
	}
	for (int arris_idx = 1;  !success && arris_idx < tetrahedron_config::ARRIS_COUNT + 1; ++arris_idx)
	{
		for (int hole_idx = 1;  !success && hole_idx < tetrahedron_config::HOLE_COUNT_PER_ARRIS + 1; ++hole_idx)
		{
			success = IDIPDetachGem(pimp, arris_idx, hole_idx, category, level);
		}
	}
	for (int spirit_gem_idx = 1; !success &&  spirit_gem_idx < tetrahedron_config::SPIRIT_GEM_COUNT + 1; ++spirit_gem_idx)
	{
		success = IDIPDetachGem(pimp, tetrahedron_config::SPIRIT_GEM_ARRIS, spirit_gem_idx, category, level);
	}

}
bool  player_tetrahedron::IDIPDetachGem(gcreature_imp *pimp, int arris_idx, int hole_idx, int category, int level)
{
	if (!pimp)
	{
		return false;
	}
	if (!tetrahedron_config::GetInstance().IsIdxValid(arris_idx, hole_idx))
	{
		return false;
	}

	int& cur_gem = _data.MutableGemInHole(arris_idx, hole_idx);
	if (cur_gem <= 0)
	{
		return false;
	}

	const item_template *pTemplate = item_manager::GetInstance().GetItemTemplate(cur_gem);
	if (!pTemplate || pTemplate->GetTemplateType() != ITT_ESTONE)
	{
		return false;
	}
	const item_template_estone *gem_templ = (const item_template_estone *)pTemplate;

	if ( gem_templ->_stone_type == category && gem_templ->_stone_level == level)
	{
		cur_gem = 0;
		gplayer_imp *pImp = dynamic_cast<gplayer_imp *>(pimp);
		if (pImp)
		{
			DeactivateGem(pImp, arris_idx, hole_idx);
			UpdateGemLevel(pImp);
			SendClientData(pImp);
			LOG_TRACE("player_tetrahedron::IDIPDetachGem::roleid=%ld:arris_idx=%d:hole_idx=%d:category=%d:level=%d:item_tid=%d", pimp->GetRoleID(), arris_idx, hole_idx, category, level, cur_gem);
		}
		return true;
	}
	return false;
}

void player_tetrahedron::DebugForceRefineLevel(gplayer_imp *pimp, int refine_id, int level)
{
	// 校验参数
	int refine_index = refine_id - 1;
	if (refine_index < 0 || refine_index >= _data._refine_exps.size())	
	{
		return;
	}
	if (level <= 0)
	{
		return;
	}

	// 计算新等级所需要的经验
	const int cur_refine_exp = _data._refine_exps[refine_index];
	const int cur_refine_lv = tetrahedron_config::GetInstance().CalcRefineLv(cur_refine_exp);
	const int new_refine_lv = cur_refine_lv + level;
	const int new_refine_exp = tetrahedron_config::GetInstance().CalcRefineExp(new_refine_lv);
	const int delta_exp = new_refine_exp - cur_refine_exp;
	if (delta_exp <= 0)
	{
		return;
	}
	// 需要先把需要的经验加到身上（核元能量）
	pimp->ModifyReputation({kFuncCodeTetrahedron, 0}, GNET::REPUID_TETRAHEDRON_REFINE_ENERGY, delta_exp);
	// 然后调用升级接口
	int ret = RefineUpgrade(pimp, refine_id, cur_refine_exp, new_refine_exp);
	
	// 通知客户端结果
	PB::gp_tetrahedron_op_result notify;
	notify.set_retcode(ret);
	notify.set_op_type(PB::gp_tetrahedron_op::OT_REFINE_UPGRADE);
	notify.mutable_refine()->set_refine_id(refine_id);
	notify.mutable_refine()->set_refine_old_exp(cur_refine_exp);
	notify.mutable_refine()->set_refine_new_exp(new_refine_exp);
	SendToClient(pimp, notify);

	LOG_TRACE("player_tetrahedron::DebugForceRefineLevel:ret=%d:roleid=%ld:refine_id=%d:level=[%d-%d],exp=[%d-%d]", ret, pimp->GetRoleID(), refine_id, cur_refine_lv, new_refine_lv, cur_refine_exp, new_refine_exp);
}