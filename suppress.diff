diff --git a/game/gs/player.cpp b/game/gs/player.cpp
index 6e38cb0de8..d03a5aeca6 100644
--- a/game/gs/player.cpp
+++ b/game/gs/player.cpp
@@ -3860,6 +3860,8 @@ void gplayer_imp::OnEnterSceneInit(bool reconnect, const link_id_t& old_lid, boo
 		}
 		LOG_TRACE("FinishedTimeList roleid:%ld count:%u", _parent->ID.id, index);
 	}
+
+	
 }
 
 void gplayer_imp::ActiveAddonByUnlockedPhotoid()
diff --git a/game/gs/player_misc.cpp b/game/gs/player_misc.cpp
index 047c2d1be0..7678d5667b 100644
--- a/game/gs/player_misc.cpp
+++ b/game/gs/player_misc.cpp
@@ -19903,63 +19903,62 @@ void player_personal_card::HandleClientOp(const PB::gp_personal_card_op& op)
 	}
 }
 
-ConditionSuppressCombatStat::ConditionSuppressCombatStat(gcreature_imp* imp, int system)
-	: _imp(imp), _system(system)
+ConditionSuppressCombatStat::ConditionSuppressCombatStat(gcreature_imp* imp)
+	: _imp(imp)
 {
 	if (_imp->GetParent()->scene_tag == 0)
 	{
 		GLog::log(LOG_ERR, "ConditionSuppressCombatStat: no scene detected,roleid=%ld", _imp->GetRoleID());
 		return;
 	}
-	if (checkSceneTag() || checkInstanceConfig())
-	{
-		_check_flag = true;
-	}
+	collectSuppressFunctionalLabelBySceneTag(_functional_label_set, _imp);
+	collectSuppressFunctionalLabelByInstance(_functional_label_set, _imp);
 }
 
-bool ConditionSuppressCombatStat::checkSceneTag() const
+ConditionSuppressCombatStat& ConditionSuppressCombatStat::CheckFunctionalLabel(int functional_label)
+{
+	_check_flag = _functional_label_set.find(functional_label) != _functional_label_set.end();
+	return *this;
+}
+
+void ConditionSuppressCombatStat::collectSuppressFunctionalLabelBySceneTag(std::set<int>& functional_label_set, gcreature_imp* imp)
 {
 	const auto& conf = SkillConfig::GetInstance().GetSuppressCombatStatConfig();
 
 	// 如果使用系统级检查
-	auto iter = conf.scene_tag_config.find(_imp->GetParent()->scene_tag);
+	auto iter = conf.scene_tag_config.find(imp->GetParent()->scene_tag);
 	if (iter == conf.scene_tag_config.end())
 	{
-		return false;
+		return;
 	}
-	return isSystemEnabled(iter->second);
+	functional_label_set.insert(iter->second.begin(), iter->second.end());
 }
 
-bool ConditionSuppressCombatStat::checkInstanceConfig() const
+void ConditionSuppressCombatStat::collectSuppressFunctionalLabelByInstance(std::set<int>& functional_label_set, gcreature_imp* imp)
 {
 	const auto& conf = SkillConfig::GetInstance().GetSuppressCombatStatConfig();
 
 	// 先检查副本id
-	auto inst_iter = conf.instance_config.find(_imp->GetParent()->world_tid);
+	auto inst_iter = conf.instance_config.find(imp->GetParent()->world_tid);
 	if (inst_iter == conf.instance_config.end())
 	{
-		return false;
+		return;
 	}
 
 	// 再检查副本难度
 	// 注意此时可能还没EnterScene，所以不能取SceneImp，而是取了记录在人身上的难度字段
-	auto* player_imp = dynamic_cast<gplayer_imp*>(_imp);
+	auto* player_imp = dynamic_cast<gplayer_imp*>(imp);
 	if (!player_imp || player_imp->GetParent()->instance_mode == 0)
 	{
-		return false;
+		return;
 	}
 
 	auto mode_iter = inst_iter->second.find(player_imp->GetParent()->instance_mode);
 	if (mode_iter == inst_iter->second.end())
 	{
-		return false;
+		return;
 	}
-	return isSystemEnabled(mode_iter->second);
-}
-
-bool ConditionSuppressCombatStat::isSystemEnabled(const std::vector<int>& system_list) const
-{
-	return std::find(system_list.begin(), system_list.end(), _system) != system_list.end();
+	functional_label_set.insert(mode_iter->second.begin(), mode_iter->second.end());
 }
 
 ConditionSuppressCombatStat& ConditionSuppressCombatStat::Then(const std::function<void(gcreature_imp*)>& func, const std::string& desc)
@@ -19973,7 +19972,8 @@ ConditionSuppressCombatStat& ConditionSuppressCombatStat::Then(const std::functi
 		if (!desc.empty() && SERVER_CONFIG.debug_command_mode)
 		{
 			GLog::log(LOG_ACTION, "roleid %ld suppress combat stat:scenetag=%u,world_tid=%u,desc=%s", _imp->GetRoleID(), _imp->GetParent()->scene_tag, _imp->GetParent()->world_tid, desc.c_str());
-			_imp->DebugUTF8Say(("战斗状态抑制" + desc).c_str());
+			std::string msg = "战斗状态抑制" + desc;
+			_imp->DebugUTF8Say(msg.c_str());
 		}
 	}
 	return *this;
diff --git a/game/gs/player_misc.h b/game/gs/player_misc.h
index c8894e5965..0c0fa7daf9 100644
--- a/game/gs/player_misc.h
+++ b/game/gs/player_misc.h
@@ -4188,21 +4188,20 @@ public:
 		LONGWEN = 11,       // 龙纹系统 SKILL_FUNCTIONAL_LABEL_11
 		SEVEN_CRIME = 15,   // 七戒系统 SKILL_FUNCTIONAL_LABEL_15
 	};
-	
-	ConditionSuppressCombatStat(gcreature_imp* imp, int system);
 
+	ConditionSuppressCombatStat(gcreature_imp* imp);
+
+	ConditionSuppressCombatStat& CheckFunctionalLabel(int functional_label);
 	ConditionSuppressCombatStat& Then(const std::function<void(gcreature_imp*)>& func, const std::string& desc);
 	ConditionSuppressCombatStat& Else(const std::function<void(gcreature_imp*)>& func, const std::string& desc);
 private:
-	// 场景tag检测
-	bool checkSceneTag() const;
-	// 副本检测
-	bool checkInstanceConfig() const;
-	// 检查系统是否在启用列表中
-	bool isSystemEnabled(const std::vector<int>& system_list) const;
+	// 获取所在场景tag对应的抑制系统
+	static void collectSuppressFunctionalLabelBySceneTag(std::set<int>& functional_label_set, gcreature_imp* imp);
+	// 获取所在副本对应的抑制系统
+	static void collectSuppressFunctionalLabelByInstance(std::set<int>& functional_label_set, gcreature_imp* imp);
 
 	gcreature_imp* const _imp;
-	int _system;
+	std::set<int> _functional_label_set;
 	bool _check_flag = false;
 };
 
